package com.docs.common.filter;

import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.databind.ser.PropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter.FilterExceptFilter;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class PropertyPreExcludeFilter {

    private Set<String> excludes;

    public PropertyPreExcludeFilter() {
        this.excludes = new HashSet<>();
    }

    /**
     * 添加需要排除的属性
     *
     * @param filters 要排除的属性名称数组
     * @return PropertyPreExcludeFilter
     */
    public PropertyPreExcludeFilter addExcludes(String... filters) {
        excludes.addAll(Arrays.asList(filters));
        return this;
    }

    /**
     * 配置 Jackson ObjectMapper，应用排除属性的过滤器
     *
     * @param objectMapper Jackson的ObjectMapper实例
     * @param filterName   过滤器名称
     * @return 配置了过滤器的ObjectMapper
     */
    public ObjectMapper applyToObjectMapper(ObjectMapper objectMapper, String filterName) {
        PropertyFilter propertyFilter = SimpleBeanPropertyFilter.serializeAllExcept(excludes);
        SimpleFilterProvider filterProvider = new SimpleFilterProvider().addFilter(filterName, propertyFilter);
        return objectMapper.setFilterProvider(filterProvider);
    }
}