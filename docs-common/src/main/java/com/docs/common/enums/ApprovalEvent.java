package com.docs.common.enums;

public enum ApprovalEvent {
    SUBMIT_REQUEST("提交申请"),
    SUPERVISOR_APPROVE("主管批准"),
    SUPERVISOR_REJECT("主管拒绝"),
    MANAGER_APPROVE("经理批准"),
    MANAGER_REJECT("经理拒绝"),
    CANCEL_BY_REQUESTER("申请人撤销");
    // Potentially others like: EXPIRE("已过期"), AUTO_ESCALATE("自动升级") etc.

    private final String displayName;

    ApprovalEvent(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
