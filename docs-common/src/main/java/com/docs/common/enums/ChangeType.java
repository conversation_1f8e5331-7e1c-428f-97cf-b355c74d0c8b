package com.docs.common.enums;

/**
 * 变更类型枚举
 */
public enum ChangeType {
    /** 新增 */
    ADD("ADD", "新增"),
    
    /** 更新 */
    UPDATE("UPDATE", "更新"), 
    
    /** 删除 */
    DELETE("DELETE", "删除");
    
    private final String code;
    private final String display;
    
    ChangeType(String code, String display) {
        this.code = code;
        this.display = display;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDisplay() {
        return display;
    }
}