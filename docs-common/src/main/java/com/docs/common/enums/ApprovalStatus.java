package com.docs.common.enums;

/**
 * 文件下载审批状态枚举
 */
public enum ApprovalStatus {
    PENDING_SUPERVISOR_APPROVAL("待主管审批"),
    PENDING_MANAGER_APPROVAL("待经理审批"),
    APPROVED("审批通过"),
    REJECTED_BY_SUPERVISOR("主管拒绝"),
    REJECTED_BY_MANAGER("经理拒绝"),
    CANCELLED("已撤销");

    private final String displayName;

    ApprovalStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    // Optional: a static method to get enum from string, useful if not relying on jOOQ's default converters
    public static ApprovalStatus fromString(String text) {
        for (ApprovalStatus b : ApprovalStatus.values()) {
            if (b.name().equalsIgnoreCase(text)) {
                return b;
            }
        }
        return null; // Or throw exception
    }
}
