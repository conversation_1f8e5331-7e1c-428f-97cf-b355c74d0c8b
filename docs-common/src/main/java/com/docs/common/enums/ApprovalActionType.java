package com.docs.common.enums;

/**
 * 审批操作类型枚举
 * 提供了操作类型的显示名称、图标类型、颜色等元数据，以便前端更好地展示
 */
public enum ApprovalActionType {
    /**
     * 提交申请
     */
    SUBMIT("提交申请", "submit", "primary", "用户提交了文件下载申请"),

    /**
     * 主管批准
     */
    SUPERVISOR_APPROVE("主管批准", "approve", "success", "主管批准了申请，流转至经理审批"),

    /**
     * 主管拒绝
     */
    SUPERVISOR_REJECT("主管拒绝", "reject", "danger", "主管拒绝了申请，流程结束"),

    /**
     * 经理批准
     */
    MANAGER_APPROVE("经理批准", "approve", "success", "经理批准了申请，申请通过"),

    /**
     * 经理拒绝
     */
    MANAGER_REJECT("经理拒绝", "reject", "danger", "经理拒绝了申请，流程结束"),

    /**
     * 申请人撤销
     */
    CANCEL_REQUEST("撤销申请", "cancel", "warning", "申请人撤销了申请，流程结束");

    // 未来可能的扩展
    // AUTO_APPROVE("自动批准", "approve", "success", "系统自动批准了申请"),
    // AUTO_REJECT("自动拒绝", "reject", "danger", "系统自动拒绝了申请（可能由于超时等原因）"),
    // ESCALATE("升级处理", "escalate", "warning", "申请被升级到更高级别处理")

    /**
     * 显示名称，用于前端展示
     */
    private final String displayName;

    /**
     * 图标类型，用于前端展示，如 submit, approve, reject, cancel 等
     */
    private final String iconType;

    /**
     * 颜色类型，用于前端展示，如 primary, success, warning, danger 等
     */
    private final String colorType;

    /**
     * 详细描述，用于提示或帮助文本
     */
    private final String description;

    ApprovalActionType(String displayName, String iconType, String colorType, String description) {
        this.displayName = displayName;
        this.iconType = iconType;
        this.colorType = colorType;
        this.description = description;
    }

    /**
     * 获取显示名称
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取图标类型
     * @return 图标类型
     */
    public String getIconType() {
        return iconType;
    }

    /**
     * 获取颜色类型
     * @return 颜色类型
     */
    public String getColorType() {
        return colorType;
    }

    /**
     * 获取详细描述
     * @return 详细描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据字符串获取枚举值
     * @param text 枚举名称字符串
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ApprovalActionType fromString(String text) {
        if (text == null || text.isEmpty()) {
            return null;
        }

        for (ApprovalActionType type : ApprovalActionType.values()) {
            if (type.name().equalsIgnoreCase(text)) {
                return type;
            }
        }
        return null; // 或抛出异常
    }
}
