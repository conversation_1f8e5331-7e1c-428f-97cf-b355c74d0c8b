package com.docs.common.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 文件常量信息
 */
public final class FileConstants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 基础上传路径
     */
    public static final String BASE_UPLOAD_PATH = System.getProperty("os.name").toLowerCase().contains("windows") 
            ? "D:/docs-manage/files" 
            : System.getProperty("os.name").toLowerCase().contains("mac") 
                ? "/Users/<USER>/Downloads/files"
                : "/data/docs-manage/files";

    /** 文件类型：图纸 */
    public static final String FILE_TYPE_DRAWING = "图纸";

    /** 文件类型：BOM */
    public static final String FILE_TYPE_BOM = "BOM";

    /** 文件类型：软件 */
    public static final String FILE_TYPE_SOFTWARE = "软件";

    /**
     * 允许的文件扩展名
     */
    public static final List<String> ALLOWED_EXTENSIONS = Arrays.asList("xls", "xlsx", "pdf", "doc", "docx", "dwg", "zip", "rar", "7z"); // Merged extensions

    /**
     * PDF文件扩展名
     */
    public static final List<String> PDF_EXTENSIONS = Arrays.asList("pdf");

    /**
     * 压缩文件扩展名（2D/3D图纸）
     */
    public static final List<String> ARCHIVE_EXTENSIONS = Arrays.asList("zip", "rar", "7z");

    /**
     * 图纸子类型：PDF
     */
    public static final String DRAWING_SUB_TYPE_PDF = "PDF";

    /**
     * 图纸子类型：2D图纸
     */
    public static final String DRAWING_SUB_TYPE_2D = "2D";

    /**
     * 图纸子类型：3D图纸
     */
    public static final String DRAWING_SUB_TYPE_3D = "3D";

    /**
     * 图纸子类型：2D/3D压缩包
     */
    public static final String DRAWING_SUB_TYPE_2D3D = "2D/3D";

    /**
     * 有效的文件类型
     */
    public static final Set<String> VALID_FILE_TYPES = new HashSet<>(Arrays.asList(FILE_TYPE_DRAWING, FILE_TYPE_BOM, FILE_TYPE_SOFTWARE));

    /**
     * 有效的BOM类型
     */
    public static final Set<String> VALID_BOM_TYPES = new HashSet<>(Arrays.asList("EBOM", "PBOM")); // Keep as specific BOM types

    /**
     * 有效的图纸子类型
     */
    public static final Set<String> VALID_DRAWING_SUB_TYPES = new HashSet<>(Arrays.asList(DRAWING_SUB_TYPE_PDF, DRAWING_SUB_TYPE_2D, DRAWING_SUB_TYPE_3D, DRAWING_SUB_TYPE_2D3D));

    /**
     * 版本号正则表达式 (e.g., 1.0, 12.34)
     */
    public static final String VERSION_REGEX = "^\\d+\\.\\d+$";

    /**
     * 机型角色前缀
     */
    public static final String MODEL_ROLE_PREFIX = "Model_";
} 