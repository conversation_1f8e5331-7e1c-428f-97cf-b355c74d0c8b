package com.docs.common.utils;

import com.docs.common.core.page.PageDomain;
import com.docs.common.core.page.TableDataInfo;
import com.docs.common.core.page.TableSupport;

import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.Result;
import org.jooq.Select;
import org.jooq.SelectLimitPercentAfterOffsetStep;
import org.jooq.SelectOffsetStep;
import org.jooq.SelectOrderByStep;
import org.jooq.SortField;
import org.jooq.SortOrder;
import org.jooq.Table;
import org.jooq.impl.DSL;

import java.util.List;
import java.util.stream.Collectors;

/**
 * JOOQ分页工具类
 * 集成系统分页组件，提供与现有系统分页一致的分页方式
 * 
 * <AUTHOR>
 */
public class JooqPaginationUtils {

    /**
     * 从当前请求中获取分页参数进行分页
     * 
     * @param <R> 记录类型
     * @param dsl DSL上下文
     * @param query 查询对象
     * @return 表格分页数据对象
     */
    public static <R extends Record> TableDataInfo getDataTable(DSLContext dsl, Select<R> query) {
        // 获取分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        
        // 获取排序信息
        String orderBy = pageDomain.getOrderBy();
        
        // 如果有排序字段，并且原始查询可以排序
        if (StringUtils.isNotEmpty(orderBy) && query instanceof SelectOrderByStep) {
            // 处理排序（这里需要具体实现，根据实际情况可能需要调整）
            // 简单处理：假设orderBy的格式为"field_name asc/desc"
            String[] orderBySplit = orderBy.split(" ");
            if (orderBySplit.length >= 2) {
                String fieldName = orderBySplit[0];
                boolean isAsc = "asc".equalsIgnoreCase(orderBySplit[1]);
                
                // 使用field方法构建排序字段，此处仅为示例
                Field<?> field = DSL.field(fieldName);
                query = ((SelectOrderByStep<R>) query).orderBy(isAsc ? field.asc() : field.desc());
            }
        }
        
        // 执行分页查询
        return getDataTable(dsl, query, pageNum, pageSize);
    }

    /**
     * 使用指定的页码和每页大小进行分页
     * 
     * @param <R> 记录类型
     * @param dsl DSL上下文
     * @param query 查询对象
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页记录数
     * @return 表格分页数据对象
     */
    public static <R extends Record> TableDataInfo getDataTable(
            DSLContext dsl, 
            Select<R> query, 
            Integer pageNum, 
            Integer pageSize) {
        
        // 参数校验
        pageNum = (pageNum == null || pageNum < 1) ? 1 : pageNum;
        pageSize = (pageSize == null || pageSize < 1) ? 10 : pageSize;
        
        // 计算总记录数
        long total = dsl.fetchCount(query);
        
        // 应用分页 - 将查询直接转换为Table，然后创建新查询
        Table<R> subQuery = query.asTable("t");
        
        // 从子查询中选择所有字段并应用分页
        int offset = (pageNum - 1) * pageSize;
        List<R> records = dsl.select()
                .from(subQuery)
                .offset(offset)
                .limit(pageSize)
                .fetchInto(query.getRecordType());
        
        // 创建TableDataInfo对象并返回
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);  // 成功状态码
        rspData.setMsg("查询成功");
        rspData.setRows(records);
        rspData.setTotal(total);
        
        return rspData;
    }
    
    /**
     * 将JOOQ查询结果转换为指定POJO类型的TableDataInfo
     * 
     * @param <R> 记录类型
     * @param <E> 目标类型
     * @param dsl DSL上下文
     * @param query 查询对象
     * @param targetClass 目标类类型
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 包含转换后数据的表格分页数据对象
     */
    public static <R extends Record, E> TableDataInfo getDataTable(
            DSLContext dsl, 
            Select<R> query, 
            Class<E> targetClass,
            Integer pageNum, 
            Integer pageSize) {
        
        // 参数校验
        pageNum = (pageNum == null || pageNum < 1) ? 1 : pageNum;
        pageSize = (pageSize == null || pageSize < 1) ? 10 : pageSize;
        
        // 计算总记录数
        long total = dsl.fetchCount(query);
        
        // 应用分页 - 将查询直接转换为Table，然后创建新查询
        Table<R> subQuery = query.asTable("t");
        
        // 从子查询中选择所有字段并应用分页
        int offset = (pageNum - 1) * pageSize;
        List<E> rows = dsl.select()
                .from(subQuery)
                .offset(offset)
                .limit(pageSize)
                .fetchInto(targetClass);
        
        // 创建TableDataInfo对象并返回
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);  // 成功状态码
        rspData.setMsg("查询成功");
        rspData.setRows(rows);
        rspData.setTotal(total);
        
        return rspData;
    }
    
    /**
     * 处理排序字段，并返回添加了排序条件的查询对象
     * 
     * @param <R> 记录类型
     * @param query 可排序的查询对象
     * @param pageDomain 分页参数对象
     * @param fieldMapping 字段映射关系，key为前端参数名，value为数据库字段名
     * @return 添加了排序条件的查询对象
     */
    public static <R extends Record> Select<R> handleOrderBy(
            SelectOrderByStep<R> query, 
            PageDomain pageDomain, 
            java.util.Map<String, String> fieldMapping) {
        
        String orderByColumn = pageDomain.getOrderByColumn();
        String isAsc = pageDomain.getIsAsc();
        
        if (StringUtils.isNotEmpty(orderByColumn)) {
            // 获取实际的数据库字段名
            String dbFieldName = fieldMapping.getOrDefault(orderByColumn, orderByColumn);
            
            // 构建排序字段
            Field<?> field = DSL.field(dbFieldName);
            SortOrder sortOrder = "asc".equalsIgnoreCase(isAsc) ? SortOrder.ASC : SortOrder.DESC;
            
            // 应用排序
            return query.orderBy(field.sort(sortOrder));
        }
        
        return query;
    }
} 