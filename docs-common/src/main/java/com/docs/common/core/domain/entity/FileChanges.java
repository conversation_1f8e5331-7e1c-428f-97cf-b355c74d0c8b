package com.docs.common.core.domain.entity;

import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FileChanges {
    private Integer id;

    /**
    * 物料编码ID
    */
    private Integer bomItemId;

    /**
    * 关联文件ID
    */
    private Integer fileId;

    /**
    * 变更前版本
    */
    private String oldVersion;

    /**
    * 变更后版本
    */
    private String newVersion;

    /**
    * 变更原因
    */
    private String changeReason;

    /**
    * 变更人
    */
    private Long changedBy;

    /**
    * 变更时间
    */
    private Date changedAt;

    /**
     * 操作人名称
     */
    private String operName;

    /**
     * 查询条件：产品型号ID
     */
    private Integer productModelId;

    /**
     * 查询条件：文件类型
     */
    private String fileType;

    /**
     * 查询条件：子类型/图纸编号
     */
    private String subType;
}