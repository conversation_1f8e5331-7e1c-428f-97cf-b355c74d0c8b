package com.docs.common.core.domain.entity;

import com.docs.common.enums.ApprovalStatus;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 审批请求值对象，用于前端展示
 */
@Data
@Builder
public class ApprovalRequestVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 审批请求ID */
    private Long id;

    /** 文件ID */
    private Long fileId;

    /** 文件名称 */
    private String fileName;

    /** 文件路径 */
    private String filePath;

    /** 申请人ID */
    private Long requesterId;

    /** 申请人姓名 */
    private String requesterName;

    /** 申请人昵称 */
    private String requesterNickName;

    /** 申请人部门 */
    private String requesterDepartment;

    /** 申请理由 */
    private String requestReason;

    /** 当前状态 */
    private ApprovalStatus status;

    /** 状态描述 */
    private String statusDesc;

    /** 主管ID */
    private Long supervisorId;

    /** 主管姓名 */
    private String supervisorName;

    /** 主管昵称 */
    private String supervisorNickName;

    /** 主管部门 */
    private String supervisorDepartment;

    /** 经理ID */
    private Long managerId;

    /** 经理姓名 */
    private String managerName;

    /** 经理昵称 */
    private String managerNickName;

    /** 经理部门 */
    private String managerDepartment;

    /** 处理人姓名（最后一个操作人） */
    private String approverName;

    /** 处理人昵称（最后一个操作人） */
    private String approverNickName;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;

    /** 操作记录列表 */
    private List<ApprovalActionRecordVO> actionRecords;
}
