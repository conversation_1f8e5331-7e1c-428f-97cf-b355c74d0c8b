package com.docs.common.core.domain.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import org.springframework.web.multipart.MultipartFile;

@Data
public class FileChangeRequest {
    @NotNull(message = "文件ID不能为空")
    private Integer fileId;
    
    // 产品模型ID
    private Integer productModelId;
    
    @NotBlank(message = "新版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+$", message = "版本号格式不正确")
    private String newVersion;
    
    @NotBlank(message = "变更理由不能为空")
    private String changeReason;

    // 不需要校验，因为在 Controller 中会接收
    private MultipartFile file;
    
    // 支持多文件上传
    private MultipartFile[] files;
} 