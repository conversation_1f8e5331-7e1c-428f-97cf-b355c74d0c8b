package com.docs.common.core.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * BOM图纸变更历史DTO - 新版本
 * 支持4种文件类型的完整变更历史追踪
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BomDrawingChangeHistoryDTO {
    
    /** 变更记录ID */
    private Long id;
    
    /** BOM项ID */
    private Integer bomItemId;
    
    /** 物料编码 */
    private String materialCode;
    
    /** 物料名称 */
    private String materialName;
    
    /** 规格型号 */
    private String specification;
    
    /** 变更类型: ADD, UPDATE, DELETE */
    private String changeType;
    
    /** 文件类型: PDF, 2D, 3D, COMPRESSED */
    private String fileType;
    
    /** 变更前图纸编号 */
    private String oldDrawingNo;
    
    /** 变更前文件ID */
    private Integer oldFileId;
    
    /** 变更前文件名 */
    private String oldFileName;
    
    /** 变更前文件版本 */
    private String oldFileVersion;
    
    /** 变更后图纸编号 */
    private String newDrawingNo;
    
    /** 变更后文件ID */
    private Integer newFileId;
    
    /** 变更后文件名 */
    private String newFileName;
    
    /** 变更后文件版本 */
    private String newFileVersion;
    
    /** 变更原因 */
    private String changeReason;
    
    /** 变更人ID */
    private Integer changedBy;
    
    /** 变更人姓名 */
    private String changedByName;
    
    /** 变更时间 */
    private LocalDateTime changedAt;
    
    /** 变更前文件路径 */
    private String oldFilePath;
    
    /** 变更前文件状态 */
    private String oldFileStatus;
    
    /** 变更后文件路径 */
    private String newFilePath;
    
    /** 变更后文件状态 */
    private String newFileStatus;
    
    /**
     * 获取变更类型的中文显示
     */
    public String getChangeTypeDisplay() {
        if (changeType == null) return "";
        switch (changeType) {
            case "ADD": return "新增";
            case "UPDATE": return "更新";
            case "DELETE": return "删除";
            default: return changeType;
        }
    }
    
    /**
     * 获取文件类型的中文显示
     */
    public String getFileTypeDisplay() {
        if (fileType == null) return "";
        switch (fileType) {
            case "PDF": return "PDF文档";
            case "2D": return "2D图纸";
            case "3D": return "3D图纸";
            case "COMPRESSED": return "压缩包";
            default: return fileType;
        }
    }
}