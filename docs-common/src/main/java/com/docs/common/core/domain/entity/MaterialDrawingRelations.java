package com.docs.common.core.domain.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

/**
 * 物料与图纸关联表
 */
@Data
@NoArgsConstructor
public class MaterialDrawingRelations {
    /**
    * 主键ID
    */
    private Integer id;

    /**
    * 物料编码
    */
    private String materialCode;

    /**
    * 物料名称
    */
    private String materialName;

    /**
    * 型号规格
    */
    private String specification;

    /**
    * 图纸编号
    */
    private String drawingNo;

    /**
    * 创建时间
    */
    private Date createdAt;

    /**
    * 更新时间
    */
    private Date updatedAt;
}