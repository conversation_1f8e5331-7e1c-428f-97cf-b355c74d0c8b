package com.docs.common.core.domain.entity;

import com.docs.common.enums.ApprovalActionType;
import com.docs.common.enums.ApprovalStatus;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审批操作记录值对象，用于前端展示
 * 包含了操作记录的基本信息、操作人信息、操作类型信息、状态变化信息等
 */
@Data
@Builder
public class ApprovalActionRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long id;

    /** 关联的审批请求ID */
    private Long approvalRequestId;

    /** 操作人ID */
    private Long actorId;

    /** 操作人姓名 */
    private String actorName;

    /** 操作人部门 */
    private String actorDepartment;

    /** 操作类型 */
    private ApprovalActionType actionType;

    /** 操作类型显示名称 */
    private String actionTypeDesc;

    /** 操作类型图标 */
    private String actionTypeIcon;

    /** 操作类型颜色 */
    private String actionTypeColor;

    /** 操作类型详细描述 */
    private String actionTypeDescription;

    /** 审批意见/说明 */
    private String comment;

    /** 操作时间 */
    private LocalDateTime actionTime;

    /** 操作前状态 */
    private ApprovalStatus previousStatus;

    /** 操作前状态描述 */
    private String previousStatusDesc;

    /** 操作后状态 */
    private ApprovalStatus nextStatus;

    /** 操作后状态描述 */
    private String nextStatusDesc;
}
