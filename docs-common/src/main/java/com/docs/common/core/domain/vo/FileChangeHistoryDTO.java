package com.docs.common.core.domain.vo;

import com.docs.common.jooq.generated.enums.FilesStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 文件变更历史DTO，包含变更记录和相关文件信息
 * 
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FileChangeHistoryDTO {
    
    // 变更记录ID
    private Integer id;
    
    // 关联文件ID
    private Integer fileId;
    
    // 变更前版本
    private String oldVersion;
    
    // 变更后版本
    private String newVersion;
    
    // 变更原因
    private String changeReason;
    
    // 变更人ID
    private Integer changedBy;
    
    // 变更人姓名
    private String changedByName;
    
    // 变更时间
    private LocalDateTime changedAt;
    
    // 关联的文件信息（新版本文件）
    private FileInfo fileInfo;
    
    @Data
    public static class FileInfo {
        // 文件ID
        private Integer id;
        
        // 文件名称
        private String fileName;
        
        // 文件路径
        private String filePath;
        
        // 文件类型
        private String fileType;
        
        // 文件版本
        private String version;
        
        // 文件状态
        private FilesStatus status;
    }
}
