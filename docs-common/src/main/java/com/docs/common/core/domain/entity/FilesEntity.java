package com.docs.common.core.domain.entity;

import java.util.Date;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilesEntity {
    private Integer id;

    /**
     * 关联产品型号ID
     */
    private Integer productModelId;

    /**
     * 产品型号代码
     */
    private String modelCode;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 子类型(电子BOM/结构BOM/图纸编号)
     */
    private String subType;

    /**
     * 图纸编号
     */
    private String drawingNo;
    /**
     * 版本号
     */
    private String version;

    private String status;

    /**
     * 文件路径
     */
    private String filePath;

    private Date createdAt;

    private Date updatedAt;

    /**
     * 查询条件：用户有权限的机型ID集合
     */
    private Set<Integer> modelIds;

    private String materialNo;

}