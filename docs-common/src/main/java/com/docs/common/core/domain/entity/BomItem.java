package com.docs.common.core.domain.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * BOM表项实体类
 */
@Data
@NoArgsConstructor
public class BomItem {
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 关联文件ID
     */
    private Integer fileId;
    
    /**
     * 序号
     */
    private String itemNo;
    
    /**
     * 深度
     */
    private Integer depth;
    
    /**
     * 物料编码
     */
    private String materialCode;
    
    /**
     * 物料名称
     */
    private String materialName;
    
    /**
     * 型号规格
     */
    private String specification;
    
    /**
     * 供应商/品牌
     */
    private String supplier;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 数量
     */
    private Double quantity;
    
    /**
     * 生产属性
     */
    private String productionProperty;
    
    /**
     * 类别
     */
    private String category;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 所属BOM类型（电子BOM/结构BOM）
     */
    private String bomType;

    private Date createdAt;

    private Date updatedAt;
} 