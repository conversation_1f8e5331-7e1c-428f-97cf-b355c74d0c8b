package com.docs.common.core.domain.vo;

import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.enums.FilesStatus;
import com.docs.common.jooq.generated.tables.pojos.BomItems;
import org.springframework.format.annotation.DateTimeFormat;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class FileListVO {

    private Integer       id;
    private Integer       productModelId;
    private FilesFileType fileType;
    private String        subType;
    private String        version;
    private FilesStatus   status;
    private String        fileName;
    private String        filePath;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    private String        drawingNo;
    private String        bomHeader;
    private String        bomSubheader;

    // Add modelCode from ProductModels
    private String modelCode;
    
    // 标识文件是否有变更历史
    private Boolean hasChanges;

    // Ensure bomItems is initialized
    private List<BomItemsVO> bomItems = new ArrayList<>();

    public String getModelCode() {
        if(modelCode == null) {
            return "无";
        }
        return modelCode;
    }
}
