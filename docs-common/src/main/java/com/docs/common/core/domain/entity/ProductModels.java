package com.docs.common.core.domain.entity;

import java.util.Date;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
public class ProductModels {
    private Integer id;

    /**
     * 产品型号编码
     */
    @NotBlank(message = "产品型号编码不能为空")
    private String modelCode;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空")
    private String status;

    private Date createdAt;

    private Date updatedAt;
}