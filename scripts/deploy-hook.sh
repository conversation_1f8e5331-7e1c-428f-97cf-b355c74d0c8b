#!/bin/bash
# 部署钩子脚本 - 需要复制到服务器的 /data/docs-manage/deploy-hook.sh

# 启用错误检查
set -e

# 接收参数 - 是否重置MySQL和镜像来源
RESET_MYSQL=${1:-false}
IMAGE_SOURCE=${2:-remote}  # 'local' 表示本地构建, 'remote' 表示从Docker Hub拉取

# 标准化RESET_MYSQL参数值
RESET_MYSQL=$(echo "$RESET_MYSQL" | tr '[:upper:]' '[:lower:]' | xargs)
echo "===== 部署开始 ====="
echo "时间: $(date)"
echo "重置数据库: $RESET_MYSQL"
echo "镜像来源: $IMAGE_SOURCE"

# 创建SSL证书目录（如果需要）
mkdir -p /data/docs-manage/nginx/ssl

# 处理镜像
if [ "$IMAGE_SOURCE" = "remote" ]; then
    # 从Docker Hub拉取镜像
    DOCKER_USERNAME=${DOCKER_USERNAME:-$USER}
    echo "从Docker Hub拉取镜像: ${DOCKER_USERNAME}/docs-manage:latest"
    docker pull ${DOCKER_USERNAME}/docs-manage:latest
    # 重命名镜像以便统一使用
    docker tag ${DOCKER_USERNAME}/docs-manage:latest docs-manage:latest
    echo "镜像拉取完成"
else
    echo "使用本地构建的镜像"

    # 检查必要的文件和目录
    echo "检查必要的构建文件..."
    missing_files=false

    if [ ! -f "Dockerfile" ]; then
        echo "错误: Dockerfile不存在"
        missing_files=true
    fi

    if [ ! -f "docker-entrypoint.sh" ]; then
        echo "错误: docker-entrypoint.sh不存在"
        missing_files=true
    fi

    if [ ! -d "docs-ui" ]; then
        echo "错误: docs-ui 目录不存在，无法找到 nginx.conf 或 dist"
        missing_files=true
    elif [ ! -f "docs-ui/nginx.conf" ]; then
        echo "错误: docs-ui/nginx.conf 不存在"
        missing_files=true
    elif [ ! -d "docs-ui/dist" ]; then
        echo "错误: docs-ui/dist 不存在 - 前端构建产物未正确复制"
        missing_files=true
    fi

    if [ ! -d "docs-admin" ] || ! ls docs-admin/target/*.jar 1> /dev/null 2>&1; then
        echo "错误: docs-admin/target 中未找到 JAR 文件"
        missing_files=true
    fi

    if [ "$missing_files" = "true" ]; then
        echo "构建失败: 缺少必要的文件或目录"
        ls -la
        exit 1
    fi

    # 构建镜像
    echo "开始构建Docker镜像..."
    if ! docker build -t docs-manage:latest .; then
        echo "Docker构建失败"
        exit 1
    fi
    echo "Docker镜像构建成功"
fi

# 更新docker-compose.yml中的镜像名称
sed -i 's|\${DOCKER_USERNAME}/docs-manage:latest|docs-manage:latest|g' docker-compose.yml

# 处理数据库重置
if [[ "$RESET_MYSQL" == "true" ]] || [[ "$RESET_MYSQL" == "1" ]] || [[ "$RESET_MYSQL" == "yes" ]] || [[ "$RESET_MYSQL" == *"true"* ]]; then
    echo "重置数据库 (in-place)..."

    # 停止所有容器
    echo "停止所有容器..."
    docker compose down
    echo "所有容器已停止"

    # 删除MySQL数据卷
    echo "删除MySQL数据卷..."
    # 使用docker volume ls查找正确的数据卷名称
    MYSQL_VOLUME=$(docker volume ls | grep mysql_data | awk '{print $2}')
    if [ -n "$MYSQL_VOLUME" ]; then
        echo "找到MySQL数据卷: $MYSQL_VOLUME"
        docker volume rm $MYSQL_VOLUME || true
    else
        echo "未找到MySQL数据卷，跳过删除步骤"
    fi

    # 启动MySQL容器（会自动创建新的数据卷和初始化数据库）
    echo "启动MySQL容器..."
    docker compose up -d mysql
    echo "等待MySQL启动..."
    sleep 30

    # 检查MySQL容器是否运行
    if ! docker ps | grep -q docs-mysql; then
        echo "错误: MySQL容器未运行，请检查日志"
        docker logs docs-mysql || true
        exit 1
    fi
else
    # 如果不重置数据库，只停止应用容器
    echo "停止应用容器..."
    docker compose stop docs-manage
    echo "应用容器已停止"
fi

# 从init.sql初始化数据库
if [[ "$RESET_MYSQL" == "true" ]] || [[ "$RESET_MYSQL" == "1" ]] || [[ "$RESET_MYSQL" == "yes" ]] || [[ "$RESET_MYSQL" == *"true"* ]]; then
    echo "从 /data/docs-manage/sql/init.sql 初始化数据库..."
    INIT_SQL_PATH="/data/docs-manage/sql/init.sql"

    if [ ! -f "$INIT_SQL_PATH" ]; then
        echo "错误: 未找到初始化SQL文件 $INIT_SQL_PATH"
        exit 1
    fi

    MAX_RETRIES=3
    RETRY_COUNT=0
    SUCCESS=false

    while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$SUCCESS" = "false" ]; do
        echo "尝试初始化数据库 (尝试 $((RETRY_COUNT+1))/$MAX_RETRIES)..."

        # 尝试使用max_allowed_packet参数
        if cat "$INIT_SQL_PATH" | docker exec -i docs-mysql mysql -uroot -p123456 --max_allowed_packet=64M; then
            echo "数据库初始化成功"
            SUCCESS=true
        else
            ERROR_CODE=$?
            echo "初始化失败，错误代码: $ERROR_CODE，尝试使用最小参数..."

            # 尝试不使用额外参数
            if cat "$INIT_SQL_PATH" | docker exec -i docs-mysql mysql -uroot -p123456; then
                echo "使用最小参数初始化数据库成功"
                SUCCESS=true
            else
                ERROR_CODE=$?
                RETRY_COUNT=$((RETRY_COUNT+1))
                echo "数据库初始化失败，错误代码: $ERROR_CODE"

                if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
                    echo "等待30秒后重试..."
                    sleep 30

                    # 最后一次重试时，使用特殊处理大文件的方法
                    if [ $RETRY_COUNT -eq $(($MAX_RETRIES-1)) ]; then
                        echo "最终重试: 使用特殊方法处理大型SQL文件..."
                        # 将SQL文件复制到容器中直接执行
                        docker cp "$INIT_SQL_PATH" docs-mysql:/tmp/init.sql
                        if docker exec docs-mysql bash -c "mysql -uroot -p123456 < /tmp/init.sql"; then
                            echo "使用容器内直接执行初始化数据库成功"
                            SUCCESS=true
                            # 跳过下一次重试，因为已经成功
                            break
                        else
                            echo "容器内直接执行也失败，进入最终重试"
                        fi
                    fi
                else
                    echo "达到最大重试次数，初始化失败"
                    exit 1
                fi
            fi
        fi
    done

    # 确保应用在成功初始化后启动
    echo "启动所有容器..."
    docker compose up -d
    echo "等待容器启动..."
    sleep 15
fi

# 无论是否重置数据库，都确保所有容器启动
echo "启动/更新所有容器..."
docker compose up -d

# 验证部署状态
echo "验证部署状态..."
sleep 10

# 检查应用容器
if docker ps | grep -q docs-manage; then
    echo "应用容器 (docs-manage) 运行正常"
else
    echo "警告: 应用容器 (docs-manage) 未运行"
    docker logs docs-manage || true
    echo "部署可能失败，请检查日志"
fi

# 检查Nginx容器
if docker ps | grep -q docs-ui; then
    echo "Nginx容器 (docs-ui) 运行正常"
else
    echo "警告: Nginx容器 (docs-ui) 未运行"
    docker logs docs-ui || true
    echo "可能是Nginx配置问题，请检查"
fi

# 检查Redis容器
if docker ps | grep -q docs-redis; then
    echo "Redis容器 (docs-redis) 运行正常"
else
    echo "警告: Redis容器 (docs-redis) 未运行"
    docker logs docs-redis || true
    echo "可能是Redis配置问题，请检查"
fi

# 检查MySQL容器
if docker ps | grep -q docs-mysql; then
    echo "MySQL容器 (docs-mysql) 运行正常"
else
    echo "警告: MySQL容器 (docs-mysql) 未运行"
    docker logs docs-mysql || true
    echo "可能是MySQL配置问题，请检查"
fi

echo "===== 部署完成 ====="