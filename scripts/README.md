# 手动部署脚本使用指南

## 概述

这是一个简洁、可靠的手动部署脚本，专为中国大陆服务器环境设计，采用原生部署方式（前后端不使用Docker），只有MySQL和Redis使用Docker容器。

## 特性

- ✅ **简洁可靠** - 避免复杂的自动化部署问题
- ✅ **原生部署** - 前后端直接部署到服务器，性能更好
- ✅ **国内优化** - 使用国内镜像源，网络稳定
- ✅ **模块化设计** - 支持单独构建/部署各个组件
- ✅ **详细日志** - 完整的构建和部署日志
- ✅ **服务管理** - 进程管理和状态监控

## 前提条件

### 环境要求
- CentOS 7+ / Ubuntu 18+
- Java 8+
- Maven 3.6+
- Node.js 14+
- Nginx
- Docker (仅用于MySQL/Redis)

### 目录结构
```
/data/docs-manage/
├── docs-admin/          # 后端Spring Boot应用模块
├── docs-files/          # 文件管理模块
├── docs-ui/             # 前端Vue.js项目
├── scripts/             # 部署脚本
├── logs/                # 日志目录
├── sql/                 # 数据库脚本
└── pom.xml              # Maven多模块项目根POM
```

## 快速开始

### 1. 准备工作

```bash
# 确保代码已复制到服务器
cd /data/docs-manage

# 确保MySQL容器运行
docker ps | grep mysql

# 给脚本执行权限
chmod +x scripts/manual-deploy.sh
```

### 2. 完整部署

```bash
# 执行完整部署（推荐）
./scripts/manual-deploy.sh

# 或者显式指定
./scripts/manual-deploy.sh full-deploy
```

### 3. 查看服务状态

```bash
./scripts/manual-deploy.sh status
```

## 详细用法

### 命令选项

| 命令 | 说明 |
|------|------|
| `full-deploy` | 完整部署（默认） |
| `build-backend` | 仅构建后端 |
| `build-frontend` | 仅构建前端 |
| `deploy-backend` | 仅部署后端 |
| `deploy-frontend` | 仅部署前端 |
| `stop` | 停止所有服务 |
| `status` | 查看服务状态 |
| `help` | 显示帮助信息 |

### 使用示例

```bash
# 仅构建后端
./scripts/manual-deploy.sh build-backend

# 仅构建前端
./scripts/manual-deploy.sh build-frontend

# 重新部署后端
./scripts/manual-deploy.sh deploy-backend

# 停止所有服务
./scripts/manual-deploy.sh stop

# 查看服务状态
./scripts/manual-deploy.sh status
```

## 配置说明

### 主要配置文件

- `scripts/deploy.conf` - 部署配置文件
- `scripts/manual-deploy.sh` - 主部署脚本

### 关键配置项

```bash
# 项目路径
PROJECT_ROOT="/data/docs-manage"

# 后端配置
BACKEND_MODULE="docs-admin"
BACKEND_JAR_NAME="docs-admin.jar"
BACKEND_PORT="8081"

# 前端配置
NGINX_WEB_ROOT="/var/www/html/docs"

# 数据库配置
MYSQL_HOST="localhost"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD="123456"
```

## 部署流程

### 完整部署流程

1. **环境检查** - 验证必要工具和目录
2. **数据库检查** - 确认MySQL容器运行状态
3. **停止现有服务** - 优雅停止后端服务
4. **构建后端** - Maven构建Spring Boot应用
5. **构建前端** - npm构建Vue.js应用
6. **部署前端** - 复制到nginx目录
7. **配置nginx** - 生成nginx配置文件
8. **启动后端** - 后台启动Spring Boot服务
9. **验证部署** - 检查服务状态

### 构建产物

- **后端**: `docs-admin/target/docs-admin-1.0.0.jar`
- **前端**: `docs-ui/dist/` → `/var/www/html/docs/`

### 多模块项目说明

项目采用Maven多模块结构：
- **根目录**: 执行`mvn package`构建所有模块
- **docs-admin**: Spring Boot主应用模块
- **docs-files**: 文件管理功能模块
- **docs-ui**: 前端Vue.js应用

## 服务管理

### 后端服务

- **启动方式**: `java -jar` 后台运行
- **PID文件**: `/var/run/docs-backend.pid`
- **日志文件**: `/data/docs-manage/logs/backend.log`
- **端口**: 8081

### 前端服务

- **Web服务器**: Nginx
- **配置文件**: `/etc/nginx/conf.d/docs-manage.conf`
- **静态文件**: `/var/www/html/docs/`
- **端口**: 80

### 访问地址

- **前端**: http://localhost
- **后端API**: http://localhost/api

## 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 检查Java/Maven版本
   java -version
   mvn -version

   # 查看构建日志
   tail -f logs/backend.log
   ```

2. **服务启动失败**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep 8081

   # 查看服务状态
   ./scripts/manual-deploy.sh status
   ```

3. **数据库连接失败**
   ```bash
   # 检查MySQL容器
   docker ps | grep mysql

   # 测试数据库连接
   docker exec -it mysql容器名 mysql -uroot -p123456
   ```

### 日志文件

- **后端日志**: `/data/docs-manage/logs/backend.log`
- **Nginx访问日志**: `/var/log/nginx/access.log`
- **Nginx错误日志**: `/var/log/nginx/error.log`
