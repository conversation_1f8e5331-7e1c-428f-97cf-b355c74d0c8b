#!/bin/bash

# 初始化数据库脚本
# 用法:
#   ./init-database.sh          # 使用Docker容器初始化（默认）
#   ./init-database.sh docker   # 使用Docker容器初始化
#   ./init-database.sh local    # 使用本地MySQL初始化

set -e

# 解析命令行参数
INIT_TYPE=${1:-"docker"}  # 默认使用Docker

# 配置变量
DATABASE_NAME="file_manage"
SQL_FILE="sql/init.sql"

# Docker配置
CONTAINER_NAME="docs-mysql"
DOCKER_USERNAME="root"
DOCKER_PASSWORD="123456"

# 本地MySQL配置
LOCAL_HOST="localhost"
LOCAL_PORT="3306"
LOCAL_USERNAME="root"
LOCAL_PASSWORD="123456"

# 颜色输出函数
log_info() {
    echo -e "\033[34m[INFO]\033[0m $1"
}

log_success() {
    echo -e "\033[32m[SUCCESS]\033[0m $1"
}

log_warning() {
    echo -e "\033[33m[WARNING]\033[0m $1"
}

log_error() {
    echo -e "\033[31m[ERROR]\033[0m $1"
}

# 检查SQL文件是否存在
check_sql_file() {
    log_info "检查SQL文件..."
    if [ ! -f "$SQL_FILE" ]; then
        log_error "SQL文件不存在: $SQL_FILE"
        exit 1
    fi
    log_success "SQL文件检查通过: $SQL_FILE"
}

# Docker方式初始化
init_with_docker() {
    log_info "使用Docker容器初始化数据库..."
    
    # 检查Docker容器是否运行
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        log_error "MySQL容器 $CONTAINER_NAME 未运行"
        log_info "尝试启动容器..."
        if docker ps -a | grep -q "$CONTAINER_NAME"; then
            docker start "$CONTAINER_NAME"
            sleep 5
        else
            log_error "容器不存在，请先创建并启动MySQL容器"
            exit 1
        fi
    fi
    
    # 测试数据库连接
    log_info "测试数据库连接..."
    if ! docker exec "$CONTAINER_NAME" mysql -u"$DOCKER_USERNAME" -p"$DOCKER_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
        log_error "无法连接到MySQL数据库"
        exit 1
    fi
    log_success "数据库连接测试通过"
    
    # 检查数据库是否存在
    log_info "检查数据库是否存在..."
    DB_EXISTS=$(docker exec "$CONTAINER_NAME" mysql -u"$DOCKER_USERNAME" -p"$DOCKER_PASSWORD" -e "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME='$DATABASE_NAME';" 2>/dev/null | grep -c "$DATABASE_NAME" || echo "0")
    
    if [ "$DB_EXISTS" -eq 0 ]; then
        log_info "数据库不存在，创建数据库: $DATABASE_NAME"
        docker exec "$CONTAINER_NAME" mysql -u"$DOCKER_USERNAME" -p"$DOCKER_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS $DATABASE_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        log_success "数据库创建成功"
    else
        log_warning "数据库已存在: $DATABASE_NAME"
        read -p "是否要重新初始化数据库？这将删除所有现有数据 (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            log_info "删除并重新创建数据库..."
            docker exec "$CONTAINER_NAME" mysql -u"$DOCKER_USERNAME" -p"$DOCKER_PASSWORD" -e "DROP DATABASE IF EXISTS $DATABASE_NAME;"
            docker exec "$CONTAINER_NAME" mysql -u"$DOCKER_USERNAME" -p"$DOCKER_PASSWORD" -e "CREATE DATABASE $DATABASE_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
            log_success "数据库重新创建成功"
        else
            log_info "跳过数据库重新创建"
        fi
    fi
    
    # 导入SQL文件
    log_info "导入SQL文件到数据库..."
    docker cp "$SQL_FILE" "$CONTAINER_NAME:/tmp/init.sql"
    
    # 设置字符编码并执行SQL
    docker exec -e LANG=C.UTF-8 -e LC_ALL=C.UTF-8 "$CONTAINER_NAME" mysql \
        -u"$DOCKER_USERNAME" \
        -p"$DOCKER_PASSWORD" \
        --default-character-set=utf8mb4 \
        "$DATABASE_NAME" \
        -e "source /tmp/init.sql"
    
    if [ $? -eq 0 ]; then
        log_success "SQL文件导入成功"
    else
        log_error "SQL文件导入失败"
        exit 1
    fi
    
    # 清理临时文件
    docker exec "$CONTAINER_NAME" rm -f /tmp/init.sql
}

# 本地MySQL方式初始化
init_with_local() {
    log_info "使用本地MySQL初始化数据库..."
    
    # 测试数据库连接
    log_info "测试数据库连接..."
    if ! mysql -h"$LOCAL_HOST" -P"$LOCAL_PORT" -u"$LOCAL_USERNAME" -p"$LOCAL_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
        log_error "无法连接到本地MySQL数据库"
        log_error "请检查MySQL服务是否运行，以及用户名密码是否正确"
        exit 1
    fi
    log_success "数据库连接测试通过"
    
    # 检查数据库是否存在
    log_info "检查数据库是否存在..."
    DB_EXISTS=$(mysql -h"$LOCAL_HOST" -P"$LOCAL_PORT" -u"$LOCAL_USERNAME" -p"$LOCAL_PASSWORD" -e "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME='$DATABASE_NAME';" 2>/dev/null | grep -c "$DATABASE_NAME" || echo "0")
    
    if [ "$DB_EXISTS" -eq 0 ]; then
        log_info "数据库不存在，创建数据库: $DATABASE_NAME"
        mysql -h"$LOCAL_HOST" -P"$LOCAL_PORT" -u"$LOCAL_USERNAME" -p"$LOCAL_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS $DATABASE_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        log_success "数据库创建成功"
    else
        log_warning "数据库已存在: $DATABASE_NAME"
        read -p "是否要重新初始化数据库？这将删除所有现有数据 (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            log_info "删除并重新创建数据库..."
            mysql -h"$LOCAL_HOST" -P"$LOCAL_PORT" -u"$LOCAL_USERNAME" -p"$LOCAL_PASSWORD" -e "DROP DATABASE IF EXISTS $DATABASE_NAME;"
            mysql -h"$LOCAL_HOST" -P"$LOCAL_PORT" -u"$LOCAL_USERNAME" -p"$LOCAL_PASSWORD" -e "CREATE DATABASE $DATABASE_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
            log_success "数据库重新创建成功"
        else
            log_info "跳过数据库重新创建"
        fi
    fi
    
    # 导入SQL文件
    log_info "导入SQL文件到数据库..."
    mysql -h"$LOCAL_HOST" -P"$LOCAL_PORT" -u"$LOCAL_USERNAME" -p"$LOCAL_PASSWORD" --default-character-set=utf8mb4 "$DATABASE_NAME" < "$SQL_FILE"
    
    if [ $? -eq 0 ]; then
        log_success "SQL文件导入成功"
    else
        log_error "SQL文件导入失败"
        exit 1
    fi
}

# 验证初始化结果
verify_initialization() {
    log_info "验证数据库初始化结果..."
    
    if [ "$INIT_TYPE" = "docker" ]; then
        # Docker方式验证
        TABLE_COUNT=$(docker exec "$CONTAINER_NAME" mysql -u"$DOCKER_USERNAME" -p"$DOCKER_PASSWORD" "$DATABASE_NAME" -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='$DATABASE_NAME';" 2>/dev/null | tail -n 1)
    else
        # 本地方式验证
        TABLE_COUNT=$(mysql -h"$LOCAL_HOST" -P"$LOCAL_PORT" -u"$LOCAL_USERNAME" -p"$LOCAL_PASSWORD" "$DATABASE_NAME" -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='$DATABASE_NAME';" 2>/dev/null | tail -n 1)
    fi
    
    if [ "$TABLE_COUNT" -gt 0 ]; then
        log_success "数据库初始化验证通过，共创建 $TABLE_COUNT 个表"
    else
        log_error "数据库初始化验证失败，未发现任何表"
        exit 1
    fi
}

# 显示使用说明
show_usage() {
    echo ""
    log_info "数据库初始化完成！"
    echo ""
    echo "使用说明:"
    echo "- Docker方式初始化: ./init-database.sh 或 ./init-database.sh docker"
    echo "- 本地MySQL初始化: ./init-database.sh local"
    echo ""
    echo "数据库信息:"
    echo "- 数据库名: $DATABASE_NAME"
    echo "- 字符集: utf8mb4"
    echo "- 排序规则: utf8mb4_unicode_ci"
    echo ""
    echo "连接信息:"
    if [ "$INIT_TYPE" = "docker" ]; then
        echo "- 容器名: $CONTAINER_NAME"
        echo "- 用户名: $DOCKER_USERNAME"
    else
        echo "- 主机: $LOCAL_HOST:$LOCAL_PORT"
        echo "- 用户名: $LOCAL_USERNAME"
    fi
}

# 主函数
main() {
    log_info "开始初始化数据库..."
    echo "======================================="
    echo "初始化类型: $INIT_TYPE"
    echo "数据库名: $DATABASE_NAME"
    echo "SQL文件: $SQL_FILE"
    echo "======================================="
    
    check_sql_file
    
    case "$INIT_TYPE" in
        "docker")
            init_with_docker
            ;;
        "local")
            init_with_local
            ;;
        *)
            log_error "不支持的初始化类型: $INIT_TYPE"
            log_info "支持的类型: docker, local"
            exit 1
            ;;
    esac
    
    verify_initialization
    show_usage
}

# 执行主函数
main
