#!/bin/bash

# 图纸文件子类型下载权限配置脚本 - Docker版本
#
# 功能：为docs-mysql容器应用图纸文件子类型下载权限配置
# 作者：Claude Code
# 日期：$(date +%Y-%m-%d)

set -e

# 配置参数
CONTAINER_NAME="docs-mysql"
DATABASE_NAME="file_manage"
USERNAME="root"
PASSWORD="123456"
SQL_FILE="sql/drawing_subtype_permissions.sql"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker容器是否运行
check_container() {
    log_info "检查Docker容器状态..."
    
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        log_error "Docker容器 '$CONTAINER_NAME' 未运行"
        log_info "请先启动容器：docker-compose up -d mysql"
        exit 1
    fi
    
    log_success "Docker容器 '$CONTAINER_NAME' 正在运行"
}

# 检查SQL文件是否存在
check_sql_file() {
    log_info "检查SQL文件..."
    
    if [ ! -f "$SQL_FILE" ]; then
        log_error "SQL文件 '$SQL_FILE' 不存在"
        exit 1
    fi
    
    log_success "SQL文件 '$SQL_FILE' 检查通过"
}

# 测试数据库连接
test_connection() {
    log_info "测试数据库连接..."
    
    if ! docker exec "$CONTAINER_NAME" mysql -u"$USERNAME" -p"$PASSWORD" -e "SELECT 1" >/dev/null 2>&1; then
        log_error "无法连接到数据库，请检查容器状态和数据库凭据"
        exit 1
    fi
    
    log_success "数据库连接测试通过"
}

# 检查数据库是否存在
check_database() {
    log_info "检查数据库 '$DATABASE_NAME'..."
    
    if ! docker exec "$CONTAINER_NAME" mysql -u"$USERNAME" -p"$PASSWORD" -e "USE $DATABASE_NAME" >/dev/null 2>&1; then
        log_error "数据库 '$DATABASE_NAME' 不存在"
        exit 1
    fi
    
    log_success "数据库 '$DATABASE_NAME' 检查通过"
}

# 应用权限配置
apply_permissions() {
    log_info "应用图纸文件子类型下载权限配置..."
    
    # 复制SQL文件到容器
    docker cp "$SQL_FILE" "$CONTAINER_NAME:/tmp/drawing_subtype_permissions.sql"

    # 执行SQL脚本
    if docker exec "$CONTAINER_NAME" mysql -u"$USERNAME" -p"$PASSWORD" "$DATABASE_NAME" < /dev/stdin <<EOF
source /tmp/drawing_subtype_permissions.sql
EOF
    then
        log_success "权限配置应用成功"
    else
        log_error "权限配置应用失败"
        exit 1
    fi
    
    # 清理临时文件
    docker exec "$CONTAINER_NAME" rm -f /tmp/drawing_subtype_permissions.sql
}

# 验证配置结果
verify_configuration() {
    log_info "验证权限配置..."

    # 检查图纸下载权限菜单 (2030-2033)
    local download_menu_count=$(docker exec "$CONTAINER_NAME" mysql -u"$USERNAME" -p"$PASSWORD" "$DATABASE_NAME" -se "
        SELECT COUNT(*) FROM sys_menu WHERE menu_id BETWEEN 2030 AND 2033;
    ")

    if [ "$download_menu_count" -eq 4 ]; then
        log_success "图纸下载权限配置验证通过 (新增 $download_menu_count 个下载权限项)"
    else
        log_warning "图纸下载权限数量异常，预期4个，实际 $download_menu_count 个"
    fi

    # 检查图纸权限管理菜单 (2035-2037)
    local management_menu_count=$(docker exec "$CONTAINER_NAME" mysql -u"$USERNAME" -p"$PASSWORD" "$DATABASE_NAME" -se "
        SELECT COUNT(*) FROM sys_menu WHERE menu_id BETWEEN 2035 AND 2037;
    ")

    if [ "$management_menu_count" -eq 3 ]; then
        log_success "图纸权限管理配置验证通过 (新增 $management_menu_count 个管理权限项)"
    else
        log_warning "图纸权限管理数量异常，预期3个，实际 $management_menu_count 个"
    fi

    # 检查角色权限分配
    local role_menu_count=$(docker exec "$CONTAINER_NAME" mysql -u"$USERNAME" -p"$PASSWORD" "$DATABASE_NAME" -se "
        SELECT COUNT(*) FROM sys_role_menu WHERE menu_id BETWEEN 2030 AND 2037;
    ")

    log_info "角色权限分配数量：$role_menu_count 个"

    # 显示图纸下载权限配置详情
    log_info "图纸下载权限配置详情："
    docker exec "$CONTAINER_NAME" mysql -u"$USERNAME" -p"$PASSWORD" "$DATABASE_NAME" -e "
        SELECT
            m.menu_id,
            m.menu_name,
            m.perms,
            m.status
        FROM sys_menu m
        WHERE m.menu_id BETWEEN 2030 AND 2033
        ORDER BY m.menu_id;
    "

    # 显示图纸权限管理配置详情
    log_info "图纸权限管理配置详情："
    docker exec "$CONTAINER_NAME" mysql -u"$USERNAME" -p"$PASSWORD" "$DATABASE_NAME" -e "
        SELECT
            m.menu_id,
            m.menu_name,
            m.parent_id,
            m.perms,
            m.status
        FROM sys_menu m
        WHERE m.menu_id BETWEEN 2035 AND 2037
        ORDER BY m.menu_id;
    "

    # 显示角色权限分配详情
    log_info "角色权限分配详情："
    docker exec "$CONTAINER_NAME" mysql -u"$USERNAME" -p"$PASSWORD" "$DATABASE_NAME" -e "
        SELECT
            r.role_name,
            m.menu_name,
            m.perms
        FROM sys_role r
        JOIN sys_role_menu rm ON r.role_id = rm.role_id
        JOIN sys_menu m ON rm.menu_id = m.menu_id
        WHERE m.menu_id BETWEEN 2030 AND 2037
        ORDER BY r.role_id, m.menu_id;
    "
}

# 显示使用说明
show_usage() {
    log_info "权限配置完成后的使用说明："
    echo "
1. 管理员登录系统
2. 进入 系统管理 → 角色管理
3. 选择要配置的角色，点击 '分配权限'
4. 在权限树中找到 '文件管理' → 展开查看新增的图纸下载权限：
   - PDF图纸下载
   - 2D图纸下载
   - 3D图纸下载
   - 2D/3D压缩包下载
5. 根据需要为角色勾选相应权限
6. 保存配置，权限立即生效

图纸权限管理：
1. 进入 文档管理 → 图纸权限
2. 查看当前权限映射配置
3. 测试图纸类型权限映射
4. 监控配置状态和健康度

权限标识符：
- files:download:pdf    (PDF图纸下载)
- files:download:2d     (2D图纸下载)
- files:download:3d     (3D图纸下载)
- files:download:2d3d   (2D/3D压缩包下载)

管理权限标识符：
- files:drawing:permissions:view (查看图纸权限配置)
- files:drawing:permissions:test (测试图纸权限映射)

角色权限分配建议：
- 超级管理员(admin)：拥有所有图纸类型下载权限 + 管理权限
- 部门经理(manager)：拥有所有图纸类型下载权限
- 部门主管(supervisor)：拥有PDF和2D图纸下载权限
- 普通员工(user)：仅拥有PDF图纸下载权限
"
}

# 主函数
main() {
    log_info "开始执行图纸文件子类型下载权限配置..."
    echo "======================================="

    check_container
    check_sql_file
    test_connection
    check_database
    apply_permissions
    verify_configuration

    echo "======================================="
    log_success "图纸文件子类型下载权限配置完成！"

    show_usage
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        echo "使用方法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --help, -h     显示此帮助信息"
        echo "  --verify       仅验证当前配置状态"
        echo ""
        echo "示例:"
        echo "  $0              # 执行完整的权限配置"
        echo "  $0 --verify     # 验证当前权限配置状态"
        exit 0
        ;;
    --verify)
        log_info "验证当前权限配置状态..."
        check_container
        test_connection
        check_database
        verify_configuration
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "未知参数: $1"
        log_info "使用 --help 查看帮助信息"
        exit 1
        ;;
esac