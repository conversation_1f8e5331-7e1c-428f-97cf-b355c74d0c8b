#!/bin/bash

# 手动部署脚本 - 简洁版本
# 适用于中国大陆服务器环境，原生部署方式

set -e  # 遇到错误立即退出

# =============================================================================
# 配置区域
# =============================================================================

# 项目配置
PROJECT_ROOT="/data/docs-manage"
BACKEND_MODULE="docs-admin"
BACKEND_JAR_NAME="docs-admin.jar"
BACKEND_PORT="8081"
FRONTEND_DIST_DIR="docs-ui/dist"
NGINX_WEB_ROOT="/var/www/html/docs"

# 服务配置
BACKEND_PID_FILE="/var/run/docs-backend.pid"
NGINX_CONFIG_FILE="/etc/nginx/conf.d/docs-manage.conf"

# 数据库配置
MYSQL_HOST="localhost"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD="123456"
MYSQL_DATABASE="file_manage"

# Redis配置
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_DATABASE="0"

# =============================================================================
# 工具函数
# =============================================================================

log_info() {
    echo -e "\033[0;32m[INFO]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "\033[0;33m[WARN]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "\033[0;31m[ERROR]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_step() {
    echo -e "\033[0;36m[STEP]\033[0m $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v "$1" &> /dev/null; then
        log_error "命令 '$1' 未找到，请先安装"
        exit 1
    fi
}

# 检查服务状态
check_service_status() {
    local service_name="$1"
    if systemctl is-active --quiet "$service_name"; then
        log_info "$service_name 服务正在运行"
        return 0
    else
        log_warn "$service_name 服务未运行"
        return 1
    fi
}

# 停止后端服务
stop_backend() {
    if [ -f "$BACKEND_PID_FILE" ]; then
        local pid=$(cat "$BACKEND_PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "停止后端服务 (PID: $pid)"
            kill -TERM "$pid"
            sleep 5
            if kill -0 "$pid" 2>/dev/null; then
                log_warn "优雅停止失败，强制终止"
                kill -KILL "$pid"
            fi
        fi
        rm -f "$BACKEND_PID_FILE"
    fi
}

# 启动后端服务
start_backend() {
    local jar_file="$PROJECT_ROOT/$BACKEND_MODULE/target/$BACKEND_JAR_NAME"

    if [ ! -f "$jar_file" ]; then
        log_error "后端JAR文件不存在: $jar_file"
        log_error "请确保已在项目根目录执行Maven构建"
        exit 1
    fi
    
    log_info "启动后端服务..."
    cd "$PROJECT_ROOT"

    # 设置环境变量
    export SPRING_DATASOURCE_DRUID_MASTER_URL="*****************************************************************************************************************************************************************"
    export SPRING_DATASOURCE_DRUID_MASTER_USERNAME="$MYSQL_USER"
    export SPRING_DATASOURCE_DRUID_MASTER_PASSWORD="$MYSQL_PASSWORD"
    export SPRING_REDIS_HOST="$REDIS_HOST"
    export SPRING_REDIS_PORT="$REDIS_PORT"
    export SPRING_REDIS_DATABASE="$REDIS_DATABASE"

    log_info "数据库连接: $SPRING_DATASOURCE_DRUID_MASTER_URL"
    log_info "Redis连接: $SPRING_REDIS_HOST:$SPRING_REDIS_PORT"

    # 后台启动Spring Boot应用
    nohup java -jar \
        -Xms512m -Xmx1024m \
        -Dspring.profiles.active=prod \
        -Dserver.port="$BACKEND_PORT" \
        "$jar_file" \
        > logs/backend.log 2>&1 &
    
    local pid=$!
    echo "$pid" > "$BACKEND_PID_FILE"
    
    log_info "后端服务已启动 (PID: $pid)"
    
    # 等待服务启动
    sleep 10
    if kill -0 "$pid" 2>/dev/null; then
        log_info "✓ 后端服务启动成功"
    else
        log_error "✗ 后端服务启动失败"
        cat logs/backend.log | tail -20
        exit 1
    fi
}

# =============================================================================
# 主要功能函数
# =============================================================================

# 环境检查
check_environment() {
    log_step "检查部署环境..."
    
    # 检查必要命令
    check_command "java"
    check_command "mvn"
    check_command "node"
    check_command "npm"
    check_command "nginx"
    check_command "docker"
    
    # 检查Java版本
    local java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "Java版本: $java_version"
    
    # 检查Maven版本
    local maven_version=$(mvn -version | head -n 1 | cut -d' ' -f3)
    log_info "Maven版本: $maven_version"
    
    # 检查Node.js版本
    local node_version=$(node -v)
    log_info "Node.js版本: $node_version"
    
    # 检查项目目录
    if [ ! -d "$PROJECT_ROOT" ]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
    log_info "当前工作目录: $(pwd)"
    
    # 创建必要目录
    mkdir -p logs
    mkdir -p "$NGINX_WEB_ROOT"
    
    log_info "✓ 环境检查完成"
}

# 检查数据库连接
check_database() {
    log_step "检查数据库连接..."
    
    # 检查MySQL容器状态
    if ! docker ps | grep -q mysql; then
        log_error "MySQL容器未运行，请先启动MySQL容器"
        exit 1
    fi
    
    # 测试数据库连接
    if docker exec -i $(docker ps | grep mysql | awk '{print $1}') \
        mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "USE $MYSQL_DATABASE; SELECT 1;" > /dev/null 2>&1; then
        log_info "✓ 数据库连接正常"
    else
        log_error "✗ 数据库连接失败"
        exit 1
    fi
}

# 构建后端
build_backend() {
    log_step "构建后端应用..."

    # 在项目根目录执行构建（多模块项目）
    cd "$PROJECT_ROOT"

    # 清理之前的构建
    log_info "清理Maven缓存..."
    mvn clean -q

    # 配置Maven使用国内镜像
    log_info "使用阿里云Maven镜像进行构建..."

    # 构建项目（包含jOOQ代码生成）
    log_info "构建多模块Maven项目..."
    if mvn package -DskipTests \
        -Dmaven.repo.local=/root/.m2/repository \
        -s /root/.m2/settings.xml; then
        log_info "✓ 后端构建成功"
    else
        log_error "✗ 后端构建失败"
        exit 1
    fi

    # 验证JAR文件
    local jar_file="$BACKEND_MODULE/target/$BACKEND_JAR_NAME"
    if [ -f "$jar_file" ]; then
        local jar_size=$(ls -lh "$jar_file" | awk '{print $5}')
        log_info "JAR文件大小: $jar_size"
        log_info "JAR文件路径: $jar_file"
    else
        log_error "JAR文件未生成: $jar_file"
        log_error "请检查Maven构建日志和模块配置"
        exit 1
    fi
}

# 构建前端
build_frontend() {
    log_step "构建前端应用..."
    
    cd "$PROJECT_ROOT/docs-ui"
    
    # 配置npm使用国内镜像
    log_info "配置npm使用淘宝镜像..."
    npm config set registry https://registry.npmmirror.com
    
    # 清理之前的构建
    log_info "清理前端缓存..."
    rm -rf node_modules dist package-lock.json
    
    # 安装依赖
    log_info "安装前端依赖..."
    if npm install; then
        log_info "✓ 依赖安装成功"
    else
        log_error "✗ 依赖安装失败"
        exit 1
    fi
    
    # 构建生产版本
    log_info "构建前端应用..."
    if npm run build:prod; then
        log_info "✓ 前端构建成功"
    else
        log_error "✗ 前端构建失败"
        exit 1
    fi
    
    # 验证构建产物
    if [ -d "dist" ] && [ "$(ls -A dist)" ]; then
        local dist_size=$(du -sh dist | cut -f1)
        log_info "构建产物大小: $dist_size"
    else
        log_error "前端构建产物为空"
        exit 1
    fi
}

# 部署前端
deploy_frontend() {
    log_step "部署前端应用..."

    # 复制构建产物到nginx目录
    log_info "复制前端文件到nginx目录..."
    rm -rf "$NGINX_WEB_ROOT"/*
    cp -r "$PROJECT_ROOT/$FRONTEND_DIST_DIR"/* "$NGINX_WEB_ROOT/"

    # 设置文件权限
    # 检查nginx用户是否存在，如果不存在则使用www-data或root
    if id "nginx" &>/dev/null; then
        chown -R nginx:nginx "$NGINX_WEB_ROOT"
    elif id "www-data" &>/dev/null; then
        chown -R www-data:www-data "$NGINX_WEB_ROOT"
    else
        # 如果都不存在，使用root用户但设置适当权限
        chown -R root:root "$NGINX_WEB_ROOT"
        log_warn "nginx和www-data用户都不存在，使用root用户"
    fi
    chmod -R 755 "$NGINX_WEB_ROOT"

    log_info "✓ 前端部署完成"
}

# 配置nginx 
configure_nginx() {
    log_step "配置nginx..."

    # 检查nginx主配置文件，支持多种安装方式
    local main_nginx_conf=""
    local actual_conf_dir=""

    # 尝试不同的nginx配置文件路径
    if [ -f "/etc/nginx/nginx.conf" ]; then
        main_nginx_conf="/etc/nginx/nginx.conf"
    elif [ -f "/usr/local/nginx/conf/nginx.conf" ]; then
        main_nginx_conf="/usr/local/nginx/conf/nginx.conf"
    else
        log_error "找不到nginx主配置文件"
        exit 1
    fi

    log_info "找到nginx主配置文件: $main_nginx_conf"

    if [ -f "$main_nginx_conf" ]; then
        # 检查主配置文件中include的目录
        if grep -q "include.*conf.d.*\.conf" "$main_nginx_conf"; then
            # 根据主配置文件路径确定conf.d目录
            if [[ "$main_nginx_conf" == "/usr/local/nginx/conf/nginx.conf" ]]; then
                actual_conf_dir="/usr/local/nginx/conf/conf.d"
            else
                actual_conf_dir="/etc/nginx/conf.d"
            fi
        elif grep -q "include.*sites-enabled" "$main_nginx_conf"; then
            actual_conf_dir="/etc/nginx/sites-enabled"
        else
            # 如果都没有，添加include指令到主配置文件
            log_warn "nginx主配置文件中没有找到include指令，将添加conf.d目录"
            if [[ "$main_nginx_conf" == "/usr/local/nginx/conf/nginx.conf" ]]; then
                actual_conf_dir="/usr/local/nginx/conf/conf.d"
            else
                actual_conf_dir="/etc/nginx/conf.d"
            fi
            # 备份原配置文件
            cp "$main_nginx_conf" "$main_nginx_conf.backup"
            # 在http块中添加include指令
            sed -i "/http {/a\\    include $actual_conf_dir/*.conf;" "$main_nginx_conf"
        fi
    fi

    # 使用检测到的配置目录
    NGINX_CONFIG_FILE="$actual_conf_dir/docs-manage.conf"
    log_info "使用nginx配置目录: $actual_conf_dir"

    # 确保nginx配置目录存在
    if [ ! -d "$actual_conf_dir" ]; then
        log_info "创建nginx配置目录: $actual_conf_dir"
        mkdir -p "$actual_conf_dir"
    fi

    # 生成nginx配置文件
    log_info "生成nginx配置文件: $NGINX_CONFIG_FILE"
    cat > "$NGINX_CONFIG_FILE" << EOF
server {
    listen 80;
    server_name localhost;

    # gzip压缩配置
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;

    # 静态资源缓存和优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root $NGINX_WEB_ROOT;
        expires max;
        access_log off;
        add_header Cache-Control "public, max-age=31536000";
    }

    # API请求代理到后端服务 - 生产环境路径
    location /prod-api/ {
        proxy_pass http://localhost:$BACKEND_PORT/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 300s;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";

        # 文件上传大小限制
        client_max_body_size 100M;
    }

    # API请求代理到后端服务 - 开发环境路径
    location /api/ {
        proxy_pass http://localhost:$BACKEND_PORT/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 300s;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";

        # 文件上传大小限制
        client_max_body_size 100M;
    }

    # SPA路由 - 所有不匹配上面规则的请求都转发到index.html
    location / {
        root $NGINX_WEB_ROOT;
        index index.html index.htm;
        try_files \$uri \$uri/ @router;
    }

    location @router {
        rewrite ^.*$ /index.html last;
    }

    # 禁止访问.htaccess文件
    location ~ /\.ht {
        deny all;
    }

    # 错误页面配置
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root $NGINX_WEB_ROOT;
    }
}
EOF

    # 测试nginx配置
    if nginx -t; then
        log_info "✓ nginx配置验证成功"
    else
        log_error "✗ nginx配置验证失败"
        exit 1
    fi

    # 重新加载nginx
    if systemctl reload nginx; then
        log_info "✓ nginx配置已重新加载"
    else
        log_error "✗ nginx重新加载失败"
        exit 1
    fi
}

# 诊断nginx配置
diagnose_nginx() {
    log_step "诊断nginx配置..."

    echo "=== Nginx配置诊断 ==="

    # 检查nginx主配置文件
    local main_conf=""
    if [ -f "/etc/nginx/nginx.conf" ]; then
        main_conf="/etc/nginx/nginx.conf"
    elif [ -f "/usr/local/nginx/conf/nginx.conf" ]; then
        main_conf="/usr/local/nginx/conf/nginx.conf"
    fi

    if [ -n "$main_conf" ] && [ -f "$main_conf" ]; then
        echo "✓ 主配置文件存在: $main_conf"
        echo "Include指令:"
        grep -n "include" "$main_conf" | head -5
    else
        echo "✗ 找不到nginx主配置文件"
    fi

    echo ""
    echo "=== 配置文件检查 ==="
    if [ -f "$NGINX_CONFIG_FILE" ]; then
        echo "✓ 项目配置文件存在: $NGINX_CONFIG_FILE"
        echo "文件大小: $(ls -lh "$NGINX_CONFIG_FILE" | awk '{print $5}')"
    else
        echo "✗ 项目配置文件不存在: $NGINX_CONFIG_FILE"
    fi

    echo ""
    echo "=== 前端文件检查 ==="
    if [ -d "$NGINX_WEB_ROOT" ] && [ "$(ls -A "$NGINX_WEB_ROOT" 2>/dev/null)" ]; then
        echo "✓ 前端文件目录存在: $NGINX_WEB_ROOT"
        echo "文件数量: $(find "$NGINX_WEB_ROOT" -type f | wc -l)"
        if [ -f "$NGINX_WEB_ROOT/index.html" ]; then
            echo "✓ index.html存在"
        else
            echo "✗ index.html不存在"
        fi
    else
        echo "✗ 前端文件目录为空或不存在: $NGINX_WEB_ROOT"
    fi

    echo ""
    echo "=== Nginx配置测试 ==="
    nginx -t 2>&1

    echo ""
    echo "=== 当前加载的配置文件 ==="
    nginx -T 2>/dev/null | grep -E "server_name|listen|location|root" | head -10
}

# 查看服务状态
show_status() {
    log_step "查看服务状态..."

    echo "=== 系统服务状态 ==="
    check_service_status "nginx" && echo "✓ Nginx: 运行中" || echo "✗ Nginx: 未运行"

    echo ""
    echo "=== 后端服务状态 ==="
    if [ -f "$BACKEND_PID_FILE" ]; then
        local pid=$(cat "$BACKEND_PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "✓ 后端服务: 运行中 (PID: $pid)"
            echo "  端口: $BACKEND_PORT"
            echo "  日志: $PROJECT_ROOT/logs/backend.log"
        else
            echo "✗ 后端服务: 进程不存在"
        fi
    else
        echo "✗ 后端服务: 未启动"
    fi

    echo ""
    echo "=== Docker容器状态 ==="
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(mysql|redis|NAMES)"

    echo ""
    echo "=== 端口占用情况 ==="
    netstat -tlnp | grep -E ":80|:$BACKEND_PORT|:3306|:6379" || echo "未发现相关端口占用"
}

# 停止所有服务
stop_services() {
    log_step "停止所有服务..."

    # 停止后端服务
    stop_backend

    log_info "✓ 所有服务已停止"
}

# 完整部署
full_deploy() {
    log_step "开始完整部署..."

    # 环境检查
    check_environment
    check_database

    # 停止现有服务
    stop_backend

    # 构建应用
    build_backend
    build_frontend

    # 部署应用
    deploy_frontend
    configure_nginx
    start_backend

    # 验证部署
    sleep 5
    show_status

    log_step "✓ 部署完成！"
    echo ""
    echo "访问地址:"
    echo "  前端: http://localhost"
    echo "  后端API: http://localhost/api 或 http://localhost/prod-api"
    echo ""
    echo "测试命令:"
    echo "  curl localhost                    # 测试前端"
    echo "  curl localhost/api               # 测试后端API"
    echo "  curl localhost:$BACKEND_PORT     # 直接访问后端"
    echo ""
    echo "日志文件:"
    echo "  后端日志: $PROJECT_ROOT/logs/backend.log"
    echo "  Nginx日志: /var/log/nginx/"
    echo ""
    echo "如果遇到问题，请运行: $0 diagnose"
}

# 显示使用帮助
show_help() {
    echo "手动部署脚本使用说明"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build-backend    仅构建后端"
    echo "  build-frontend   仅构建前端"
    echo "  deploy-backend   仅部署后端"
    echo "  deploy-frontend  仅部署前端"
    echo "  full-deploy      完整部署（默认）"
    echo "  stop             停止所有服务"
    echo "  status           查看服务状态"
    echo "  diagnose         诊断nginx配置问题"
    echo "  help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 完整部署"
    echo "  $0 build-backend     # 仅构建后端"
    echo "  $0 status            # 查看服务状态"
    echo "  $0 stop              # 停止所有服务"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    local action="${1:-full-deploy}"

    case "$action" in
        "build-backend")
            check_environment
            check_database
            build_backend
            ;;
        "build-frontend")
            check_environment
            build_frontend
            ;;
        "deploy-backend")
            check_environment
            stop_backend
            start_backend
            ;;
        "deploy-frontend")
            check_environment
            deploy_frontend
            configure_nginx
            ;;
        "full-deploy")
            full_deploy
            ;;
        "stop")
            stop_services
            ;;
        "status")
            show_status
            ;;
        "diagnose")
            diagnose_nginx
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $action"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
