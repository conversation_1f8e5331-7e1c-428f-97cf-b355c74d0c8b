#!/bin/bash

# 快速启动脚本 - 用于测试手动部署脚本

set -e

echo "=== 手动部署脚本快速测试 ==="
echo ""

# 检查脚本是否存在
if [ ! -f "scripts/manual-deploy.sh" ]; then
    echo "❌ 错误: manual-deploy.sh 脚本不存在"
    exit 1
fi

# 检查脚本权限
if [ ! -x "scripts/manual-deploy.sh" ]; then
    echo "🔧 设置脚本执行权限..."
    chmod +x scripts/manual-deploy.sh
fi

echo "✅ 脚本检查完成"
echo ""

# 显示帮助信息
echo "📖 显示帮助信息:"
./scripts/manual-deploy.sh help
echo ""

# 检查服务状态
echo "🔍 检查当前服务状态:"
./scripts/manual-deploy.sh status
echo ""

echo "🚀 快速测试完成！"
echo ""
echo "接下来你可以："
echo "  1. 执行完整部署: ./scripts/manual-deploy.sh"
echo "  2. 仅构建后端: ./scripts/manual-deploy.sh build-backend"
echo "  3. 仅构建前端: ./scripts/manual-deploy.sh build-frontend"
echo "  4. 查看服务状态: ./scripts/manual-deploy.sh status"
echo ""
