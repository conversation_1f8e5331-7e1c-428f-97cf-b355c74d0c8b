#!/bin/bash

# 导出Docker数据库到/root目录的脚本
# 数据库名称: docs-manage
# 用法:
#   ./export-database.sh          # 导出结构+数据（默认）
#   ./export-database.sh all      # 导出结构+数据
#   ./export-database.sh schema   # 仅导出结构
#   ./export-database.sh data     # 仅导出数据

set -e

# 解析命令行参数
EXPORT_TYPE=${1:-"all"}  # 默认导出全部

# 配置变量
CONTAINER_NAME="docs-mysql"
DATABASE_NAME="file_manage"
EXPORT_DIR="/root"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# 根据导出类型设置文件名
case "$EXPORT_TYPE" in
    "schema")
        BACKUP_FILE="docs-manage_schema_${TIMESTAMP}.sql"
        EXPORT_DESC="表结构"
        ;;
    "data")
        BACKUP_FILE="docs-manage_data_${TIMESTAMP}.sql"
        EXPORT_DESC="数据"
        ;;
    "all"|*)
        BACKUP_FILE="docs-manage_backup_${TIMESTAMP}.sql"
        EXPORT_DESC="表结构+数据"
        ;;
esac

BACKUP_PATH="${EXPORT_DIR}/${BACKUP_FILE}"

echo "开始导出数据库..."
echo "导出类型: $EXPORT_DESC"

# 检查Docker容器是否运行
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    echo "错误: MySQL容器 $CONTAINER_NAME 未运行"
    echo "请先启动容器: docker-compose up -d mysql"
    exit 1
fi

# 创建导出目录（如果不存在）
mkdir -p "$EXPORT_DIR"

# 设置字符编码环境变量
export LC_ALL=C.UTF-8
export LANG=C.UTF-8

# 根据导出类型执行不同的导出命令
echo "正在导出数据库 $DATABASE_NAME 到 $BACKUP_PATH..."

case "$EXPORT_TYPE" in
    "schema")
        echo "导出内容: 仅表结构"
        docker exec -e LANG=C.UTF-8 -e LC_ALL=C.UTF-8 "$CONTAINER_NAME" mysqldump \
            -u root \
            -p123456 \
            --single-transaction \
            --routines \
            --triggers \
            --events \
            --add-drop-table \
            --no-data \
            --default-character-set=utf8mb4 \
            --set-charset \
            --set-gtid-purged=OFF \
            --no-tablespaces \
            "$DATABASE_NAME" > "$BACKUP_PATH"
        ;;
    "data")
        echo "导出内容: 仅数据"
        docker exec -e LANG=C.UTF-8 -e LC_ALL=C.UTF-8 "$CONTAINER_NAME" mysqldump \
            -u root \
            -p123456 \
            --single-transaction \
            --no-create-info \
            --complete-insert \
            --extended-insert \
            --lock-tables=false \
            --default-character-set=utf8mb4 \
            --set-charset \
            --hex-blob \
            --set-gtid-purged=OFF \
            --no-tablespaces \
            "$DATABASE_NAME" > "$BACKUP_PATH"
        ;;
    "all"|*)
        echo "导出内容: 表结构 + 数据"
        docker exec -e LANG=C.UTF-8 -e LC_ALL=C.UTF-8 "$CONTAINER_NAME" mysqldump \
            -u root \
            -p123456 \
            --single-transaction \
            --routines \
            --triggers \
            --events \
            --add-drop-table \
            --add-locks \
            --complete-insert \
            --extended-insert \
            --lock-tables=false \
            --default-character-set=utf8mb4 \
            --set-charset \
            --hex-blob \
            --set-gtid-purged=OFF \
            --no-tablespaces \
            "$DATABASE_NAME" > "$BACKUP_PATH"
        ;;
esac

# 检查导出是否成功
if [ $? -eq 0 ]; then
    echo "数据库导出成功!"
    echo "文件位置: $BACKUP_PATH"
    echo "文件大小: $(du -h "$BACKUP_PATH" | cut -f1)"

    # 确保文件编码正确
    echo "正在检查和修复文件编码..."

    # 在文件开头添加UTF-8 BOM和字符集声明
    temp_file="${BACKUP_PATH}.tmp"
    {
        echo "-- MySQL dump with UTF-8 encoding"
        echo "-- Generated by docs-manage export script"
        echo "-- Date: $(date)"
        echo ""
        echo "/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;"
        echo "/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;"
        echo "/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;"
        echo "/*!40101 SET NAMES utf8mb4 */;"
        echo ""
        cat "$BACKUP_PATH"
        echo ""
        echo "/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;"
        echo "/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;"
        echo "/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;"
    } > "$temp_file"

    mv "$temp_file" "$BACKUP_PATH"
    echo "文件编码修复完成"

    # 验证导出内容
    echo ""
    echo "验证导出内容:"

    case "$EXPORT_TYPE" in
        "schema")
            echo "- 表结构数量: $(grep -c "CREATE TABLE" "$BACKUP_PATH" 2>/dev/null || echo "0")"
            echo "- 存储过程/函数: $(grep -c "CREATE.*PROCEDURE\|CREATE.*FUNCTION" "$BACKUP_PATH" 2>/dev/null || echo "0")"
            echo "- 触发器数量: $(grep -c "CREATE.*TRIGGER" "$BACKUP_PATH" 2>/dev/null || echo "0")"
            if grep -q "CREATE TABLE" "$BACKUP_PATH"; then
                echo "✓ 确认包含表结构"
            else
                echo "⚠ 警告: 未检测到表结构"
            fi
            ;;
        "data")
            echo "- INSERT语句数量: $(grep -c "INSERT INTO" "$BACKUP_PATH" 2>/dev/null || echo "0")"
            echo "- 数据行数估计: $(grep -c "INSERT INTO" "$BACKUP_PATH" 2>/dev/null || echo "0")"
            if grep -q "INSERT INTO" "$BACKUP_PATH"; then
                echo "✓ 确认包含表数据"
            else
                echo "⚠ 警告: 未检测到INSERT语句，数据库可能为空"
            fi
            ;;
        "all"|*)
            echo "- 表结构数量: $(grep -c "CREATE TABLE" "$BACKUP_PATH" 2>/dev/null || echo "0")"
            echo "- INSERT语句数量: $(grep -c "INSERT INTO" "$BACKUP_PATH" 2>/dev/null || echo "0")"
            echo "- 存储过程/函数: $(grep -c "CREATE.*PROCEDURE\|CREATE.*FUNCTION" "$BACKUP_PATH" 2>/dev/null || echo "0")"
            echo "- 触发器数量: $(grep -c "CREATE.*TRIGGER" "$BACKUP_PATH" 2>/dev/null || echo "0")"

            if grep -q "CREATE TABLE" "$BACKUP_PATH"; then
                echo "✓ 确认包含表结构"
            else
                echo "⚠ 警告: 未检测到表结构"
            fi

            if grep -q "INSERT INTO" "$BACKUP_PATH"; then
                echo "✓ 确认包含表数据"
            else
                echo "⚠ 警告: 未检测到INSERT语句，数据库可能为空"
            fi
            ;;
    esac
else
    echo "数据库导出失败!"
    exit 1
fi

# 可选：创建压缩文件
echo "正在创建压缩文件..."
gzip "$BACKUP_PATH"
COMPRESSED_FILE="${BACKUP_PATH}.gz"

echo "压缩完成!"
echo "压缩文件: $COMPRESSED_FILE"
echo "压缩后大小: $(du -h "$COMPRESSED_FILE" | cut -f1)"

echo "导出完成!"
echo ""
echo "使用说明:"
echo "- 导出结构+数据: ./export-database.sh 或 ./export-database.sh all"
echo "- 仅导出结构: ./export-database.sh schema"
echo "- 仅导出数据: ./export-database.sh data"