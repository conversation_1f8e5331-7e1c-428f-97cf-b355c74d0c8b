# 手动部署脚本配置文件
# 请根据实际环境修改以下配置

# =============================================================================
# 项目配置
# =============================================================================

# 项目根目录
PROJECT_ROOT="/data/docs-manage"

# 后端配置
BACKEND_MODULE="docs-admin"
BACKEND_JAR_NAME="docs-admin-1.0.0.jar"
BACKEND_PORT="8081"
BACKEND_PROFILE="prod"

# 前端配置
FRONTEND_DIST_DIR="docs-ui/dist"
NGINX_WEB_ROOT="/var/www/html/docs"

# =============================================================================
# 服务配置
# =============================================================================

# PID文件路径
BACKEND_PID_FILE="/var/run/docs-backend.pid"

# Nginx配置文件路径
NGINX_CONFIG_FILE="/etc/nginx/conf.d/docs-manage.conf"

# =============================================================================
# 数据库配置
# =============================================================================

# MySQL连接配置
MYSQL_HOST="localhost"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD="123456"
MYSQL_DATABASE="file_manage"

# Redis配置
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_DATABASE="0"

# =============================================================================
# JVM配置
# =============================================================================

# Java内存配置
JVM_XMS="512m"
JVM_XMX="1024m"

# JVM额外参数
JVM_OPTS="-Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai"

# =============================================================================
# 镜像源配置
# =============================================================================

# Maven镜像源
MAVEN_MIRROR="https://maven.aliyun.com/repository/public"

# NPM镜像源
NPM_REGISTRY="https://registry.npmmirror.com"

# =============================================================================
# 网络配置
# =============================================================================

# 域名配置（可选）
# 如果设置了域名，nginx配置将使用该域名
# DOMAIN_NAME="your-domain.com"

# SSL配置（可选）
# 如果需要HTTPS，请设置以下配置
# SSL_CERT_PATH="/path/to/ssl.crt"
# SSL_KEY_PATH="/path/to/ssl.key"

# =============================================================================
# 日志配置
# =============================================================================

# 日志目录
LOG_DIR="$PROJECT_ROOT/logs"

# 日志级别
LOG_LEVEL="INFO"

# 日志文件最大大小（MB）
LOG_MAX_SIZE="100"

# =============================================================================
# 备份配置
# =============================================================================

# 是否启用自动备份
ENABLE_BACKUP="true"

# 备份目录
BACKUP_DIR="$PROJECT_ROOT/backups"

# 备份保留天数
BACKUP_RETENTION_DAYS="7"
