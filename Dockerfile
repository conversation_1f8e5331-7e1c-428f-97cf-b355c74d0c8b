# 最终运行镜像
FROM openjdk:8-jre-slim
WORKDIR /app

# 安装环境依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建目录结构
RUN mkdir -p /app/static /app/data/images /app/data/logs

# 复制JAR文件 (从构建上下文中的 docs-admin/target/ 获取)
COPY docs-admin/target/*.jar /app/app.jar

# 复制SQL文件
COPY sql/ /app/sql/

# 配置和启动脚本
COPY docker-entrypoint.sh /
RUN chmod +x /docker-entrypoint.sh
ENTRYPOINT ["/docker-entrypoint.sh"]