# HTTP服务器 - 重定向到HTTPS
server {
    listen 80;
    server_name localhost;

    # 将所有HTTP请求重定向到HTTPS
    return 301 https://$host$request_uri;
}

# HTTPS服务器
server {
    listen 443 ssl;
    server_name localhost;

    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/_.anynet.it.crt;
    ssl_certificate_key /etc/nginx/ssl/_.anynet.it.key;

    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;

    # gzip
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;

    # 静态资源直接读取
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root /usr/share/nginx/html;
        expires max;
        access_log off;
        add_header Cache-Control "public, max-age=31536000";
    }

    # API请求代理到后端服务
    location /prod-api/ {
        proxy_pass http://docs-manage:8081/;  # 使用Docker服务名
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 300s;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # SPA路由 - 所有不匹配上面规则的请求都转发到index.html
    location / {
        root /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ @router;
    }

    location @router {
        rewrite ^.*$ /index.html last;
    }

    # 禁止访问.htaccess文件
    location ~ /\.ht {
        deny all;
    }

    # 错误页面配置
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}