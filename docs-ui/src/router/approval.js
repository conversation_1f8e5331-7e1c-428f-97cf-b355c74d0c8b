import Layout from '@/layout'

// 文件下载审批相关路由
export default [
  {
    path: '/docs/files/approval',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'apply',
        component: () => import('@/views/docs/files/approval/apply'),
        name: 'ApplyDownload',
        meta: { title: '文件下载申请', activeMenu: '/docs/files' }
      },
      {
        path: 'detail',
        component: () => import('@/views/docs/files/approval/detail'),
        name: 'ApprovalDetail',
        meta: { title: '申请详情', activeMenu: '/docs/files' }
      }
    ]
  }
]
