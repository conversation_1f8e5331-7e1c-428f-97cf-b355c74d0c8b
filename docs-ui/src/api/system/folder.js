import request from '@/utils/request'

// 查询列表
export function listFolder(query) {
  return request({
    url: '/system/folder/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getFolder(id) {
  return request({
    url: '/system/folder/' + id,
    method: 'get'
  })
}

// 新增
export function addFolder(data) {
  return request({
    url: '/system/folder',
    method: 'post',
    data: data
  })
}

// 修改
export function updateFolder(data) {
  return request({
    url: '/system/folder',
    method: 'put',
    data: data
  })
}

// 删除
export function delFolder(id) {
  return request({
    url: '/system/folder/' + id,
    method: 'delete'
  })
}