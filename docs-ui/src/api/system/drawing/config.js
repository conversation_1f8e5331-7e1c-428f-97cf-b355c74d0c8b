import request from '@/utils/request'

// 获取图纸权限配置
export function getDrawingPermissionConfig() {
  return request({
    url: '/files/drawing/permissions/config',
    method: 'get'
  })
}

// 获取指定子类型的权限代码
export function getDrawingPermissionCode(subType) {
  return request({
    url: `/files/drawing/permissions/subtype/${subType}`,
    method: 'get'
  })
}

// 获取所有支持的子类型
export function getSupportedSubTypes() {
  return request({
    url: '/files/drawing/permissions/subtypes',
    method: 'get'
  })
}

// 测试权限映射
export function testDrawingPermission(data) {
  return request({
    url: '/files/drawing/permissions/test',
    method: 'post',
    data: data
  })
}

// 获取配置状态信息
export function getDrawingPermissionStatus() {
  return request({
    url: '/files/drawing/permissions/status',
    method: 'get'
  })
}
