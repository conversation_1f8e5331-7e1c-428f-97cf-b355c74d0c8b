import request from '@/utils/request'

// 查询列表
export function listModel(query) {
  return request({
    url: '/system/model/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getModel(id) {
  return request({
    url: '/system/model/' + id,
    method: 'get'
  })
}

// 新增
export function addModel(data) {
  return request({
    url: '/system/model',
    method: 'post',
    data: data
  })
}

// 修改
export function updateModel(data) {
  return request({
    url: '/system/model',
    method: 'put',
    data: data
  })
}

// 删除
export function delModel(id) {
  return request({
    url: '/system/model/' + id,
    method: 'delete'
  })
}


export function deviceInfoList(query){
  return request({
    url: '/system/model/info-list',
    method: 'get',
    params: query
  })
}