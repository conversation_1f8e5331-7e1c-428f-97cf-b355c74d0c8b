import request from '@/utils/request'

// 查询列表
export function listShelf(query) {
  return request({
    url: '/system/shelf/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getShelf(id) {
  return request({
    url: '/system/shelf/' + id,
    method: 'get'
  })
}

// 新增
export function addShelf(data) {
  return request({
    url: '/system/shelf',
    method: 'post',
    data: data
  })
}

// 修改
export function updateShelf(data) {
  return request({
    url: '/system/shelf',
    method: 'put',
    data: data
  })
}

// 删除
export function delShelf(id) {
  return request({
    url: '/system/shelf/' + id,
    method: 'delete'
  })
}


//借出
export function lendShelf(data) {
  return request({
    url: '/system/model/loanout',
    method: 'post',
    data: data
  })
}

//归还
export function returnShelf(data) {
  return request({
    url: '/system/model/return',
    method: 'put',
    data: data
  })
}
//借出列表
export function listLoanOut(query) {
  return request({
    url: '/system/model/loanout/list',
    method: 'get',
    params: query
  })
}