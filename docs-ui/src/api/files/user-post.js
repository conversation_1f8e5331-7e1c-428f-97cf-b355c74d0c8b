import request from '@/utils/request'
import store from '@/store'
import { UserConstants } from '@/utils/constants'

// 获取当前用户的岗位信息（使用Vuex中的数据）
export function getCurrentUserPosts() {
  // 直接从Vuex store获取岗位编码
  const postCodes = store.getters && store.getters.postCodes
  return Promise.resolve(postCodes || [])
}

// 获取当前用户的岗位信息（兼容旧版本，从profile接口获取）
export function getCurrentUserPostsFromProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  }).then(response => {
    // 从用户个人信息中提取岗位信息
    const posts = response.postGroup ? response.postGroup.split(',') : []
    return posts
  })
}

// 检查当前用户是否为超级管理员
export function isSuperAdmin() {
  const roles = store.getters && store.getters.roles
  return roles && roles.includes('admin')
}

// 检查当前用户是否需要提交申请（普通员工或主管）
export function needsToSubmitApplication() {
  // 超级管理员不需要申请
  if (isSuperAdmin()) {
    return Promise.resolve(false)
  }

  // 直接从Vuex获取用户岗位标识
  const isManager = store.getters && store.getters.isManager

  // 只有经理不需要申请，其他都需要申请
  return Promise.resolve(!isManager)
}

// 检查当前用户是否为普通员工
export function isRegularEmployee() {
  // 超级管理员不是普通员工
  if (isSuperAdmin()) {
    return Promise.resolve(false)
  }

  // 直接从Vuex获取用户岗位标识
  const isEmployee = store.getters && store.getters.isEmployee
  const isSupervisor = store.getters && store.getters.isSupervisor
  const isManager = store.getters && store.getters.isManager

  // 只有普通员工岗位，且不是主管或经理
  return Promise.resolve(isEmployee && !isSupervisor && !isManager)
}

// 检查当前用户是否有审批权限（主管或经理）
export function hasApprovalPermission() {
  // 超级管理员不参与具体的审批流程，所以不显示"待我审批"标签页
  // 但超级管理员可以查看所有审批记录
  if (isSuperAdmin()) {
    return Promise.resolve(false)
  }

  // 直接从Vuex获取用户岗位标识
  const isSupervisor = store.getters && store.getters.isSupervisor
  const isManager = store.getters && store.getters.isManager

  // 主管或经理有审批权限
  return Promise.resolve(isSupervisor || isManager)
}

// 检查当前用户是否有查看所有审批记录的权限（超级管理员）
export function hasViewAllPermission() {
  return isSuperAdmin()
}
