import request from '@/utils/request'

// 提交文件下载申请
export function submitApprovalRequest(fileId, reason) {
  return request({
    url: '/files/approval/submit',
    method: 'post',
    params: {
      fileId,
      reason
    }
  })
}

// 批准申请
export function approveRequest(requestId, comment) {
  return request({
    url: '/files/approval/approve',
    method: 'post',
    params: {
      requestId,
      comment
    }
  })
}

// 拒绝申请
export function rejectRequest(requestId, comment) {
  return request({
    url: '/files/approval/reject',
    method: 'post',
    params: {
      requestId,
      comment
    }
  })
}

// 撤销申请
export function cancelRequest(requestId) {
  return request({
    url: '/files/approval/cancel',
    method: 'post',
    params: {
      requestId
    }
  })
}

// 获取申请详情
export function getRequestDetail(requestId) {
  return request({
    url: `/files/approval/detail/${requestId}`,
    method: 'get'
  })
}

// 获取我的申请列表
export function listMyRequests(query) {
  return request({
    url: '/files/approval/my-requests',
    method: 'get',
    params: query
  })
}

// 获取待我审批的列表
export function listPendingApprovals(query) {
  return request({
    url: '/files/approval/pending-approvals',
    method: 'get',
    params: query
  })
}

// 获取已完成的审批列表 (使用专门的后端接口)
export function listCompletedApprovals(query) {
  return request({
    url: '/files/approval/completed-approvals',
    method: 'get',
    params: query
  })
}
