import request from '@/utils/request'

// 查询BOM数据列表
export function listBomItems(query) {
  return request({
    url: '/files/bom/list',
    method: 'get',
    params: query
  })
}

// 根据文件ID获取BOM数据
export function getBomItemsByFileId(fileId) {
  return request({
    url: `/files/bom/file/${fileId}`,
    method: 'get'
  })
}

// 获取BOM数据详细信息
export function getBomItem(id) {
  return request({
    url: `/files/bom/${id}`,
    method: 'get'
  })
}

// 修改BOM数据
export function updateBomItem(data) {
  return request({
    url: '/files/bom',
    method: 'put',
    data: data
  })
}

// 删除BOM数据
export function deleteBomItem(id) {
  return request({
    url: `/files/bom/${id}`,
    method: 'delete'
  })
}

// 重新解析BOM文件
export function reparseBomFile(fileId, data) {
  return request({
    url: `/files/bom/reparse/${fileId}`,
    method: 'post',
    data: data
  })
}
