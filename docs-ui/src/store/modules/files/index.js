import { listFiles, listProductModels, addProductModel, uploadFile, getFileChanges,
  changeFileVersion, downloadFile, deleteFile, uploadDrawing, uploadMultipleDrawings } from '@/api/files/index'
import { getBomItemsByFileId } from '@/api/files/bom'
import { getFileNameFromPath, downloadFileFromBlob } from '@/views/docs/files/utils/fileUtils'

const state = {
  // 文件列表数据
  fileList: [],
  // 总条数
  total: 0,
  // 产品型号列表
  productModelOptions: [],
  // 加载状态
  loading: false,
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    productModelId: undefined,
    fileType: undefined,
    status: undefined,
    materialNo: undefined,
    drawingNo: undefined,
    fileId: undefined
  },
  // 当前选中的文件（用于历史记录等）
  currentFile: null,
  // 变更历史
  historyList: [],
  historyTotal: 0
}

const mutations = {
  SET_FILE_LIST(state, fileList) {
    state.fileList = fileList
  },
  SET_TOTAL(state, total) {
    state.total = total
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  SET_QUERY_PARAMS(state, queryParams) {
    state.queryParams = queryParams
  },
  SET_PRODUCT_MODEL_OPTIONS(state, options) {
    state.productModelOptions = options
  },
  SET_CURRENT_FILE(state, file) {
    state.currentFile = file
  },
  SET_HISTORY_LIST(state, historyList) {
    state.historyList = historyList
  },
  SET_HISTORY_TOTAL(state, total) {
    state.historyTotal = total
  },
  RESET_QUERY_PARAMS(state) {
    state.queryParams = {
      pageNum: 1,
      pageSize: 10,
      productModelId: undefined,
      fileType: undefined,
      status: undefined,
      materialNo: undefined,
      drawingNo: undefined,
      fileId: undefined
    }
  }
}

const actions = {
  // 获取文件列表
  getFileList({ commit, state }, params = {}) {
    commit('SET_LOADING', true)
    const queryParams = { ...state.queryParams, ...params }

    return new Promise((resolve, reject) => {
      listFiles(queryParams).then(response => {
        commit('SET_FILE_LIST', response.rows)
        commit('SET_TOTAL', response.total)
        commit('SET_LOADING', false)
        resolve(response)
      }).catch(error => {
        commit('SET_LOADING', false)
        reject(error)
      })
    })
  },

  // 获取产品型号列表
  getProductModels({ commit }) {
    return new Promise((resolve, reject) => {
      listProductModels().then(response => {
        commit('SET_PRODUCT_MODEL_OPTIONS', response.data)
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 新增产品型号
  addProductModel({ dispatch }, modelData) {
    return new Promise((resolve, reject) => {
      addProductModel(modelData).then(response => {
        // 重新获取产品型号列表
        dispatch('getProductModels')
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 上传文件
  uploadFile({ dispatch }, formData) {
    return new Promise((resolve, reject) => {
      uploadFile(formData).then(response => {
        // 上传完成后刷新文件列表
        dispatch('getFileList')
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 获取文件变更历史
  getFileChanges({ commit }, params) {
    return new Promise((resolve, reject) => {
      getFileChanges(params).then(response => {
        commit('SET_HISTORY_LIST', response.rows)
        commit('SET_HISTORY_TOTAL', response.total)
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 变更文件版本
  changeFileVersion({ dispatch }, formData) {
    return new Promise((resolve, reject) => {
      changeFileVersion(formData).then(response => {
        // 版本变更后刷新文件列表
        dispatch('getFileList')
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 下载文件
  downloadFile(_, fileId) {
    return new Promise((resolve, reject) => {
      downloadFile(fileId).then(response => {
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 删除文件
  deleteFile({ dispatch }, fileId) {
    return new Promise((resolve, reject) => {
      deleteFile(fileId).then(response => {
        // 删除后刷新文件列表
        dispatch('getFileList')
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 上传图纸（单文件）
  uploadDrawing({ dispatch }, formData) {
    return new Promise((resolve, reject) => {
      uploadDrawing(formData).then(response => {
        // 上传完成后刷新文件列表
        dispatch('getFileList')
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 上传图纸（多文件）
  uploadMultipleDrawings({ dispatch }, formData) {
    return new Promise((resolve, reject) => {
      uploadMultipleDrawings(formData).then(response => {
        // 上传完成后刷新文件列表
        dispatch('getFileList')
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 根据文件ID获取BOM数据
  getBomItemsByFileId({ commit }, fileId) {
    return new Promise((resolve, reject) => {
      getBomItemsByFileId(fileId).then(response => {
        resolve(response.data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // 设置查询参数
  setQueryParams({ commit, dispatch }, params) {
    commit('SET_QUERY_PARAMS', { ...state.queryParams, ...params, pageNum: 1 })
    dispatch('getFileList')
  },

  // 重置查询参数
  resetQueryParams({ commit, dispatch }) {
    commit('RESET_QUERY_PARAMS')
    dispatch('getFileList')
  },

  // 设置当前文件
  setCurrentFile({ commit }, file) {
    commit('SET_CURRENT_FILE', file)
  }
}

const getters = {
  fileList: state => state.fileList,
  loading: state => state.loading,
  total: state => state.total,
  productModelOptions: state => state.productModelOptions,
  queryParams: state => state.queryParams,
  currentFile: state => state.currentFile,
  historyList: state => state.historyList,
  historyTotal: state => state.historyTotal
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
