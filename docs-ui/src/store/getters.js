const getters = {
  sidebar: state => state.app.sidebar,
  size: state => state.app.size,
  device: state => state.app.device,
  dict: state => state.dict.dict,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  introduction: state => state.user.introduction,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  // 新增：岗位相关getters
  postCodes: state => state.user.postCodes,
  isSupervisor: state => state.user.isSupervisor,
  isManager: state => state.user.isManager,
  isEmployee: state => state.user.isEmployee,
  permission_routes: state => state.permission.routes,
  topbarRouters:state => state.permission.topbarRouters,
  defaultRoutes:state => state.permission.defaultRoutes,
  sidebarRouters:state => state.permission.sidebarRouters,

  // 文件管理模块
  fileList: state => state.files.fileList,
  fileTotal: state => state.files.total,
  fileLoading: state => state.files.loading,
  productModelOptions: state => state.files.productModelOptions,
  fileQueryParams: state => state.files.queryParams,
  currentFile: state => state.files.currentFile,
  fileHistoryList: state => state.files.historyList,
  fileHistoryTotal: state => state.files.historyTotal
}
export default getters
