<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="指令单号" prop="orderNumber">
        <el-input v-model="queryParams.orderNumber" placeholder="请输入指令单号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="产品序列号" prop="productSerialNumber">
        <el-input v-model="queryParams.productSerialNumber" placeholder="请输入产品序列号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="部门" prop="department">
        <el-input v-model="queryParams.department" placeholder="请输入部门" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="表单名称" prop="formName">
        <el-input v-model="queryParams.formName" placeholder="请输入表单名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>

    </el-form>


    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:model:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:model:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:model:remove']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:model:export']">导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="modelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="货架ID" align="center" prop="shelfId" />
      <el-table-column label="货架编号" align="center" prop="shelfNumber" />
      <el-table-column label="文件夹编号" align="center" prop="folderNumber" />
      <el-table-column label="机种名称" align="center" prop="modelName" />
      <el-table-column label="指令单号" align="center" prop="orderNumber" />
      <el-table-column label="产品序列号" align="center" prop="productSerialNumber" />
      <el-table-column label="部门" align="center" prop="department" />
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column label="表单名称" align="center" prop="formName" />
      <el-table-column label="借出状态" align="center" prop="loanStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.loanStatus === 0" type="success">未借出</el-tag>
          <el-tag v-else type="danger">已借出</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.loanStatus === 0" size="mini" type="text" icon="el-icon-share"
            @click="handleLoanOut(scope.row)" v-hasPermi="['system:model:loanout']">借出
          </el-button>
          <el-button  size="mini" type="text" icon="el-icon-info"
            @click="getListLoanOut(scope.row)" v-hasPermi="['system:model:loanoutInfo']">借出详情
          </el-button>
          <el-button v-if="scope.row.loanStatus === 1" size="mini" type="text" icon="el-icon-download"
            @click="handleReturn(scope.row)" v-hasPermi="['system:model:return']">归还
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:model:edit']">修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:model:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <el-dialog :title="title" :visible.sync="loanoutOpen" width="600px" append-to-body>
      <el-form :model="loanoutForm" ref="loanoutForm" :rules="loanoutFormRules" label-width="120px">

        <el-form-item label="货架ID" prop="shelfId">
          <el-input v-model="loanoutForm.shelfId" readonly placeholder="已选货架编号">
          </el-input>
        </el-form-item>
        <el-form-item label="借出人" prop="borrower">
          <el-input v-model="loanoutForm.borrower" placeholder="请输入借出人名称" />
        </el-form-item>
        <el-form-item label="借出原因" prop="reason">
          <el-input v-model="loanoutForm.reason" placeholder="请输入借出原因" />
        </el-form-item>
        <el-form-item label="借出时间" prop="loanTime">
          <el-date-picker v-model="loanoutForm.loanTime" type="datetime" placeholder="选择年份"
            value-format="yyyy-mm-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitLoanoutForm">提交</el-button>
          <el-button @click="loanoutOpen = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog :title="title" :visible.sync="returnOpen" width="600px" append-to-body>
      <el-form :model="returnForm" ref="returnForm" :rules="returnFormRules" label-width="120px">

        <el-form-item label="货架ID" prop="shelfId">
          <el-input v-model="returnForm.shelfId" readonly placeholder="已选货架编号">
          </el-input>
        </el-form-item>
        <el-form-item label="归还时间" prop="returnTime">
          <el-date-picker v-model="returnForm.returnTime" type="datetime" placeholder="选择年份"
            value-format="yyyy-mm-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitReturnForm">提交</el-button>
          <el-button @click="returnOpen = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog :title="title" :visible.sync="listLoanoutOpen" width="800px" append-to-body>
      <el-table v-loading="loanoutLoading" :data="loanoutList">
      <el-table-column label="货架ID" align="center" prop="shelfId" />
      <el-table-column label="借出人" align="center" prop="borrower" />
      <el-table-column label="借出理由" align="center" prop="reason" />
      <el-table-column label="借出时间" align="center" prop="loanTime" />
      <el-table-column label="归还时间" align="center" prop="returnTime" />
      <el-table-column label="借出状态" align="center" prop="loanStatus">
        <template slot-scope="scope">
        <el-tag v-if="scope.row.returnTime === null" type="danger">已借出</el-tag>
        <el-tag v-else type="success">已归还</el-tag>
        </template>
      </el-table-column>
      </el-table>
      <pagination v-show="loanoutTotal > 0" :total="loanoutTotal" :page.sync="listLoanoutQuery.pageNum" :limit.sync="listLoanoutQuery.pageSize"
      @pagination="getListLoanOut" />
    </el-dialog>
    
    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form :model="form" ref="form" :rules="rules" label-width="100px">
        <el-form-item label="货架编号" prop="shelfNumber">
          <el-input v-model="form.shelfNumber" placeholder="请输入货架编号" />
        </el-form-item>

        <el-form-item label="文件夹编号" prop="folderNumber">
          <el-input v-model="form.folderNumber" placeholder="请输入文件夹编号" />
        </el-form-item>

        <el-form-item label="机种名称" prop="modelName">
          <el-input v-model="form.modelName" placeholder="请输入机种名称" />
        </el-form-item>

        <el-form-item label="指令单号" prop="orderNumber">
          <el-input v-model="form.orderNumber" placeholder="请输入指令单号" />
        </el-form-item>

        <el-form-item label="部门" prop="department">
          <el-input v-model="form.department" placeholder="请输入部门" />
        </el-form-item>

        <el-form-item label="年份" prop="year">
          <!--          <el-input v-model="form.year" placeholder="请输入年份" />-->
          <el-date-picker v-model="form.year" type="year" placeholder="选择年份" value-format="yyyy">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="表单名称" prop="formName">
          <el-input v-model="form.formName" placeholder="请输入表单名称" />
        </el-form-item>

        <el-form-item label="产品序列号" prop="productSerialNumber">
          <el-input v-model="form.productSerialNumber" placeholder="请输入产品序列号" />
        </el-form-item>

      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getModel, delModel, addModel, updateModel, deviceInfoList } from "@/api/system/model";
import { lendShelf, returnShelf,listLoanOut } from "@/api/system/shelf";

export default {
  name: "Model",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      modelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示借出弹出层
      loanoutOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNumber: null,
        productSerialNumber: null,
        department: null,
        formName: null
      },
      // 表单参数
      form: {
        shelfNumber: '',
        folderNumber: '',
        modelName: '',
        orderNumber: '',
        department: '',
        year: '',
        formName: '',
        productSerialNumber: ''
      },
      loanoutForm: {
        shelfId: '',
        borrower: '',
        reason: '',
        loanTime: ''
      },
      // 归还弹出层
      returnOpen: false,
      // 归还表单
      returnForm: {
        shelfId: '',
        returnTime: ''
      },
      listLoanoutQuery:{
        pageNum: 1,
        pageSize: 10,
        shelfId: null
      },
      // 借出列表弹出层
      listLoanoutOpen: false,
      // 借出列表
      loanoutLoading: false,
      // 借出列表数据
      loanoutList: [],
      // 借出列表总数
      loanoutTotal: 0,
      returnFormRules: {
        shelfId: [{ required: true, message: '请货架ID', trigger: 'change' }],
        returnTime: [{ required: true, message: '请选择归还时间', trigger: 'blur' }]
      },
      loanoutFormRules: {
        shelfIds: [{ required: true, message: '请选择至少一个货架', trigger: 'change' }],
        borrower: [{ required: true, message: '请输入借出人名称', trigger: 'blur' }],
        reason: [{ required: true, message: '请输入借出原因', trigger: 'blur' }],
        loanTime: [{ required: true, message: '请选择出借时间', trigger: 'blur' }]
      },
      rules: {
        shelfNumber: [{ required: true, message: '货架编号为必填项', trigger: 'blur' }],
        folderNumber: [{ required: true, message: '文件夹编号为必填项', trigger: 'blur' }],
        modelName: [{ required: true, message: '机种名称为必填项', trigger: 'blur' }],
        orderNumber: [{ required: true, message: '指令单号为必填项', trigger: 'blur' }],
        department: [{ required: true, message: '部门为必填项', trigger: 'blur' }],
        year: [{ required: true, message: '年份为必填项', trigger: 'blur' }],
        formName: [{ required: true, message: '表单名称为必填项', trigger: 'blur' }],
        productSerialNumber: [{ required: true, message: '产品序列号为必填项', trigger: 'blur' }]
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      console.log(this.queryParams)
      deviceInfoList(this.queryParams).then(response => {
        this.modelList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        shelfNumber: '',
        folderNumber: '',
        modelName: '',
        orderNumber: '',
        department: '',
        year: '',
        formName: '',
        productSerialNumber: ''
      };
      this.resetForm("form");

      this.loanoutForm = {
        shelfId: '',
        borrower: '',
        reason: '',
        loanTime: ''
      };

      this.returnForm = {
        shelfId: '',
        returnTime: ''
      }

      this.resetForm("loanoutForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.shelfId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.shelfId || this.ids
      getModel(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.shelfId != null) {
            updateModel(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addModel(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        } else {
          console.log('表单验证失败');
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.shelfId || this.ids;
      this.$modal.confirm('是否确认删除编号为"' + ids + '"的数据项？').then(function () {
        return delModel(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/model/export', {
        ...this.queryParams
      }, `model_${new Date().getTime()}.xlsx`)
    },
    /**
     * 借出按钮操作
     * @param row 
     */
    handleLoanOut(row) {
      this.reset();
      this.title = "借出";
      this.loanoutForm.shelfId = row.shelfId;
      this.loanoutOpen = true;
    },
    /**
     * 提交借出操作
     */
    submitLoanoutForm() {
      this.$refs["loanoutForm"].validate(valid => {
        if (valid) {
          lendShelf(this.loanoutForm).then(res => {
            this.open = false;
            this.getList();
          })
          this.$modal.msgSuccess("借出成功");
          this.loanoutOpen = false;
        } else {
          console.log('表单验证失败');
        }
      });
    },
    /**
     * 归还按钮操作
     * @param row 
     */
    handleReturn(row) {
      this.reset();
      this.title = "归还";
      this.returnForm.shelfId = row.shelfId;
      this.returnOpen = true;
    },
    submitReturnForm() {
      this.$refs["returnForm"].validate(valid => {
        if (valid) {
          returnShelf(this.returnForm).then(res => {
            this.open = false;
            this.getList();
          })
          this.$modal.msgSuccess("归还成功");
          this.returnOpen = false;
        } else {
          console.log('表单验证失败');
        }
      });
    },
    /**
     * 借出详情
     * @param row 
     */
    getListLoanOut(row) {
      this.loanoutLoading = true;
      this.listLoanoutQuery.shelfId = row.shelfId;
      listLoanOut(this.listLoanoutQuery).then(res => {
        this.loanoutList = res.rows;
        this.loanoutTotal = res.total;
        this.loanoutLoading = false;
      })
      this.title = "借出列表";
      this.listLoanoutOpen = true;
    }
  }
};
</script>
