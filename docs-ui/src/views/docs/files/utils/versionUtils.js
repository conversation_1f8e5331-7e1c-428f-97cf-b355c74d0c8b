/**
 * 从完整版本号中提取实际版本号部分
 * @param {string} fullVersion 完整版本号，如 "1043JS-BOM-000-V1.0"
 * @returns {string} 实际版本号，如 "1.0"
 */
export function extractVersionNumber(fullVersion) {
  if (!fullVersion) return fullVersion;

  // 匹配形如 "1043JS-BOM-000-V1.0" 中的 "1.0"
  const versionRegex = /.*?[vV](\d+\.\d+)$/;
  const match = fullVersion.match(versionRegex);

  // 如果匹配到复杂格式版本号，返回提取的部分
  if (match && match[1]) {
    return match[1];
  }

  // 如果不是复杂格式，尝试匹配纯数字版本 x.y
  const simpleVersionRegex = /^(\d+\.\d+)$/;
  const simpleMatch = fullVersion.match(simpleVersionRegex);
  if (simpleMatch && simpleMatch[1]) {
    return simpleMatch[1];
  }

  // 如果两种格式都不匹配，可能需要警告或返回null/undefined，这里暂时返回原始值
  console.warn(`Could not extract version number from: ${fullVersion}`);
  return fullVersion; // 或者返回 null 更合适？
}

/**
 * 比较两个版本号
 * @param {string} v1 版本号1，可以是完整格式如 "1043JS-BOM-000-V1.0" 或简单格式 "1.0"
 * @param {string} v2 版本号2，可以是完整格式如 "1043JS-BOM-000-V1.1" 或简单格式 "1.1"
 * @returns {number} 如果v1 > v2返回1，如果v1 < v2返回-1，如果相等返回0，如果无法比较返回 0 或抛出错误
 */
export function compareVersions(v1, v2) {
  if (!v1 || !v2) return 0;

  // 提取实际版本号部分
  const extractedV1 = extractVersionNumber(v1);
  const extractedV2 = extractVersionNumber(v2);

  // 检查提取后的版本号是否是 x.y 格式
  const versionFormatRegex = /^\d+\.\d+$/;
  if (!versionFormatRegex.test(extractedV1) || !versionFormatRegex.test(extractedV2)) {
    console.error(`Cannot compare versions with invalid format after extraction: v1='${v1}' (extracted='${extractedV1}'), v2='${v2}' (extracted='${extractedV2}')`);
    return 0; // 或者抛出错误 throw new Error('Invalid version format for comparison');
  }

  const v1Parts = extractedV1.split('.').map(Number);
  const v2Parts = extractedV2.split('.').map(Number);

  // 比较主版本号
  if (v1Parts[0] > v2Parts[0]) return 1;
  if (v1Parts[0] < v2Parts[0]) return -1;

  // 主版本号相同，比较次版本号
  // 注意：确保 v1Parts[1] 和 v2Parts[1] 存在且是数字
  const subV1 = v1Parts.length > 1 ? v1Parts[1] : 0;
  const subV2 = v2Parts.length > 1 ? v2Parts[1] : 0;

  if (subV1 > subV2) return 1;
  if (subV1 < subV2) return -1;

  // 版本号完全相同
  return 0;
}
