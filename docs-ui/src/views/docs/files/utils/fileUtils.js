/**
 * 文件管理模块工具函数
 */

/**
 * 格式化文件类型显示
 * @param {string} fileType 文件类型标识
 * @returns {string} 格式化后的文件类型名称
 */
export function formatFileType(fileType) {
  const fileTypeMap = {
    'BOM': 'BOM文件',
    'DRAWING': '图纸',
    'BOM_DRAWING': 'BOM图纸',
    'SOFTWARE': '软件',
    'RD_FILE': '研发文件'
  }
  return fileTypeMap[fileType] || fileType
}

/**
 * 生成文件类型选项数据
 * @returns {Array} 文件类型层级选项
 */
export function getFileTypeOptions() {
  return [
    {
      value: 'BOM',
      label: 'BOM',
      children: [
        {
          value: 'EBOM',
          label: '电子BOM'
        },
        {
          value: 'PBOM',
          label: '结构BOM'
        }
      ]
    },
    {
      value: 'DRAWING',
      label: '图纸'
    },
    {
      value: 'SOFTWARE',
      label: '软件'
    },
    {
      value: 'RD_FILE',
      label: '研发文件'
    }
  ]
}

/**
 * 创建文件下载链接并触发下载
 * @param {Blob} blob 文件Blob对象
 * @param {string} fileName 文件名
 */
export function downloadFileFromBlob(blob, fileName) {
  if ('download' in document.createElement('a')) {
    const elink = document.createElement('a')
    elink.download = fileName
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href)
    document.body.removeChild(elink)
  } else {
    navigator.msSaveBlob(blob, fileName)
  }
}

/**
 * 从文件路径中提取文件名
 * @param {string} filePath 文件路径
 * @returns {string} 文件名
 */
export function getFileNameFromPath(filePath) {
  return filePath.substring(filePath.lastIndexOf('/') + 1)
}

/**
 * 获取文件扩展名
 * @param {string} fileName 文件名
 * @returns {string} 扩展名(小写)
 */
export function getFileExtension(fileName) {
  return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase()
}

/**
 * 检查文件类型是否在允许列表中
 * @param {string} fileType 文件类型
 * @param {Array} allowedTypes 允许的文件类型数组
 * @returns {boolean} 是否允许
 */
export function isAllowedFileType(fileType, allowedTypes) {
  return allowedTypes.includes(fileType.toLowerCase())
}
