<template>
  <div class="app-container">
    <el-card
      class="box-card"
      style="margin-bottom: 15px"
      :body-style="{ padding: '20px 20px 0 20px' }"
    >
      <file-search
        :showSearch="showSearch"
        :productModelOptions="productModelOptions"
        :fileTypeOptions="fileTypeOptions"
        @query="handleQuery"
        @reset="resetQuery"
      />
    </el-card>

    <el-card class="box-card">
      <!-- 操作按钮区域 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['files:upload']"
            type="primary"
            plain
            icon="el-icon-plus"
            @click="handleAddModel"
            >新增机型</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['files:upload']"
            type="success"
            plain
            icon="el-icon-upload"
            @click="handleUpload"
            >上传文件</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getFileList"
        ></right-toolbar>
      </el-row>

      <!-- 文件列表 -->
      <file-table
        ref="fileTable"
        :loading="loading"
        :file-list="fileList"
        :total="total"
        :statusOptions="statusOptions"
        :highlight-file-id="highlightFileId"
        :should-highlight="shouldHighlight"
        @pagination="handlePagination"
        @download="handleDownload"
        @history="handleHistory"
        @history-fetch="getHistoryList"
        @change="handleChange"
        @delete="handleDelete"
        @upload-drawing="handleUploadDrawing"
        @download-drawing="handleDownloadDrawing"
        @bom-item-history="handleBomItemHistory"
      />
    </el-card>

    <!-- 新增机型对话框 -->
    <product-model-dialog
      :visible.sync='modelDialog.open'
      :title='modelDialog.title'
      @submit='submitProductModel'
    />

    <!-- 上传文件对话框 -->
    <file-upload-dialog
      :visible.sync='upload.open'
      :productModelOptions='productModelOptions'
      :fileTypeOptions='fileTypeOptions'
      @submit='submitUpload'
    />

    <!-- 变更历史对话框已移至FileTable组件中 -->
    <!-- 删除此处的history-dialog组件 -->

    <!-- 上传图纸对话框 -->
    <drawing-upload-dialog
      :visible.sync='drawingUpload.open'
      :bomItem='drawingUpload.bomItem'
      :productModelCode='drawingUpload.productModelCode'
      :uploadUrl='upload.url'
      @submit='submitDrawingUpload'
    />

    <!-- 申请下载权限对话框 -->
    <approval-request-dialog
      :visible.sync='approvalRequest.open'
      :fileId='approvalRequest.fileId'
      @success='handleApprovalSuccess'
    />
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import { getFileTypeOptions } from './utils/fileUtils'
import FileSearch from './components/FileSearch'
import FileTable from './components/FileTable'
import ProductModelDialog from './components/ProductModelDialog'
import FileUploadDialog from './components/FileUploadDialog'
// 移除 HistoryDialog 导入，改用 FileTable 中的历史对话框
import DrawingUploadDialog from './components/DrawingUploadDialog'
import ApprovalRequestDialog from './components/ApprovalRequestDialog'
import { getBomItemDrawingChanges, downloadFileWithHeaders } from '@/api/files'

export default {
  name: 'Files',
  components: {
    FileSearch,
    FileTable,
    ProductModelDialog,
    FileUploadDialog,
    DrawingUploadDialog,
    ApprovalRequestDialog
  },
  data() {
    return {
      // 显示搜索条件
      showSearch: true,
      // 状态数据字典
      statusOptions: [],
      // 文件类型选项
      fileTypeOptions: getFileTypeOptions(),
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productModelId: undefined,
        fileType: undefined,
        status: undefined,
        materialNo: undefined,
        drawingNo: undefined,
        fileId: undefined, // 添加fileId参数用于按ID查询
      },
      // 高亮显示的文件ID
      highlightFileId: null,
      // 是否应该高亮显示
      shouldHighlight: false,
      // 新增机型对话框
      modelDialog: {
        open: false,
        title: '新增机型'
      },
      // 上传文件参数
      upload: {
        open: false,
        url: '#',
      },
      // 变更历史参数
      history: {
        open: false,
        list: [],
        total: 0,
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          fileId: undefined,
        },
      },
      // 图纸上传参数
      drawingUpload: {
        open: false,
        bomItem: {},
        productModelCode: '',
        rules: {
          drawingNo: [
            { required: true, message: '图纸编号不能为空', trigger: 'blur' },
          ],
        },
      },
      // 申请下载权限参数
      approvalRequest: {
        open: false,
        fileId: null
      },
    }
  },
  created() {
    // 检查URL参数中是否有fileId
    const fileId = this.$route.query.fileId;
    const highlight = this.$route.query.highlight;

    // 如果有fileId参数，设置查询条件
    if (fileId) {
      this.queryParams.fileId = fileId;
      this.highlightFileId = fileId;
      this.shouldHighlight = highlight === 'true';
    }

    this.getFileList()
    this.getProductModels()
    this.getDicts('sys_normal_disable').then((response) => {
      this.statusOptions = response.data
    })
  },
  computed: {
    ...mapState({
      fileList: (state) => state.files.fileList,
      total: (state) => state.files.total,
      loading: (state) => state.files.loading,
      productModelOptions: (state) => state.files.productModelOptions,
      currentFile: (state) => state.files.currentFile,
    }),
    ...mapGetters(['fileQueryParams']),
  },
  methods: {
    ...mapActions('files', [
      'getFileList',
      'getProductModels',
      'setQueryParams',
      'resetQueryParams',
      'setCurrentFile',
      'uploadFile',
      'getFileChanges',
      'downloadFile',
      'deleteFile',
      'uploadDrawing',
      'uploadMultipleDrawings',
      'addProductModel',
      'changeFileVersion',
    ]),
    // 搜索按钮操作
    handleQuery(query) {
      this.setQueryParams(query)
    },
    // 重置按钮操作
    resetQuery() {
      this.resetQueryParams()
    },
    // 分页操作
    handlePagination(paginationInfo) {
      this.setQueryParams(paginationInfo)
    },
    // 打开新增机型对话框
    handleAddModel() {
      this.modelDialog.open = true
      this.modelDialog.title = '新增机型'
    },
    // 提交新增机型
    submitProductModel(formData) {
      this.addProductModel(formData).then((response) => {
        this.$modal.msgSuccess('新增成功')
        this.modelDialog.open = false
      })
    },
    // 打开上传文件对话框
    handleUpload() {
      this.upload.open = true
    },
    // 提交文件上传
    submitUpload(uploadData) {
      const { form, fileList } = uploadData
      const formData = new FormData()
      fileList.forEach((file) => {
        formData.append('files', file.raw)
      })
      formData.append('productModel', form.productModel)
      // 正确处理级联选择器的值
      if (Array.isArray(form.fileType) && form.fileType.length > 0) {
        // 第一个元素作为fileType
        formData.append('fileType', form.fileType[0])

        // 如果有第二个元素，则作为subType
        if (form.fileType.length > 1) {
          formData.append('subType', form.fileType[1])
        } else {
          // 对于图纸类型，使用drawingSubType
          if (form.fileType[0] === 'DRAWING' && form.drawingSubType) {
            formData.append('subType', form.drawingSubType)
          } else {
            // 没有选择子类型，传递空字符串
            formData.append('subType', '')
          }
        }
      }

      formData.append('version', form.version)

      if (form.drawingNo) {
        formData.append('drawingNo', form.drawingNo)
      } else {
        // 如果没有图纸编号，传递空字符串
        formData.append('drawingNo', '')
      }

      // 显示全屏加载

      // 显示全屏加载
      const loading = this.$loading({
        lock: true,
        text: '正在上传文件...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      this.uploadFile(formData)
        .then((response) => {
          this.$modal.msgSuccess('上传成功')
          this.upload.open = false
        })
        .catch((error) => {
          // 错误已经在全局拦截器中处理，这里不再重复显示
          console.log('文件上传失败:', error)
        })
        .finally(() => {
          // 关闭加载效果
          loading.close()
        })
    },
    // 打开变更历史对话框
    handleHistory(row) {
      console.log('执行handleHistory方法，参数:', row);
      // 设置当前文件
      this.setCurrentFile(row)

      // 找到FileTable组件并触发其handleHistory方法
      const fileTableRef = this.$refs.fileTable;
      if (fileTableRef) {
        console.log('找到FileTable组件引用，调用其handleHistory方法');
        fileTableRef.handleHistory(row);
      } else {
        console.error('未找到FileTable组件引用');
      }
    },
    // 获取变更历史列表
    getHistoryList(queryParams, callback) {
      console.log(queryParams)

      // 检查是否是BOM物料图纸变更历史查询
      if (queryParams.bomItemId) {
        // 调用API获取BOM物料图纸变更历史
        getBomItemDrawingChanges(queryParams.bomItemId).then(response => {
          // 更新历史记录列表和总数
          this.history.list = response.rows
          this.history.total = response.total

          // 如果有回调函数，则调用回调
          if (typeof callback === 'function') {
            callback(response)
          }
        })
      } else {
        // 调用API获取文件变更历史
        this.getFileChanges(queryParams).then(response => {
          // 更新历史记录列表和总数
          this.history.list = response.rows
          this.history.total = response.total

          // 如果有回调函数，则调用回调
          if (typeof callback === 'function') {
            callback(response)
          }
        })
      }
    },
    // 打开变更版本对话框
    handleChange(formData, callback) {
      // 显示全屏加载
      const loading = this.$loading({
        lock: true,
        text: '正在变更文件版本...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      // 调用 Vuex action 发送变更请求
      this.changeFileVersion(formData)
        .then(response => {
          this.$modal.msgSuccess('版本变更成功')
          // 执行回调函数结束对话框加载状态
          if (callback && typeof callback === 'function') {
            callback()
          }
        })
        .catch(error => {
          console.error('版本变更失败:', error)
          // 出错时也要执行回调函数结束加载状态
          if (callback && typeof callback === 'function') {
            callback()
          }
        })
        .finally(() => {
          // 关闭全屏加载
          loading.close()
        })
    },
    // 下载按钮操作
    handleDownload(row) {
      // 显示全屏加载
      const loading = this.$loading({
        lock: true,
        text: '正在下载文件...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      this.downloadFile(row.id)
        .then((response) => {
          const blob = new Blob([response])

          // 检查响应类型，确保是二进制数据
          const contentType = response.type || 'application/octet-stream';
          if (contentType.includes('json')) {
            // 如果是JSON响应，可能是错误信息
            return response.text().then(text => {
              try {
                const errorData = JSON.parse(text);
                if (errorData.msg) {
                  this.$modal.msgError(errorData.msg);
                } else {
                  this.$modal.msgError('下载失败：未知错误');
                }
              } catch (e) {
                this.$modal.msgError('下载失败：解析响应失败');
              }
              throw new Error('下载失败');
            });
          }

          // 使用工具函数下载文件
          this.$createDownloadLink(blob, row.fileName)
        })
        .catch((error) => {
          // 检查是否是权限错误（403）
          if (error.response && error.response.status === 403) {
            try {
              // 尝试解析响应内容
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const errorData = JSON.parse(reader.result);
                  if (errorData.needApproval === true) {
                    // 需要申请权限，显示申请弹窗
                    this.$modal.confirm('您没有权限下载此文件，需要申请权限。是否立即申请？').then(() => {
                      // 打开申请弹窗
                      this.approvalRequest.fileId = row.id;
                      this.approvalRequest.open = true;
                    }).catch(() => {});
                  } else if (errorData.needApproval === false) {
                    // 用户未被分配权限，显示提示消息
                    this.$modal.msgError(errorData.msg || '您暂无权限下载，请联系管理员');
                  } else if (errorData.msg) {
                    // 其他错误消息
                    this.$modal.msgError(errorData.msg);
                  }
                } catch (e) {
                  // 解析JSON失败，但仍然是403错误，可能是权限问题
                  console.error('解析响应数据失败:', e);
                  // 显示申请权限弹窗
                  this.$modal.confirm('您可能没有权限下载此文件，需要申请权限。是否立即申请？').then(() => {
                    this.approvalRequest.fileId = row.id;
                    this.approvalRequest.open = true;
                  }).catch(() => {});
                }
              };
              reader.readAsText(error.response.data);
            } catch (e) {
              // 如果是403错误，可能是权限问题，显示申请权限弹窗
              if (error.response && error.response.status === 403) {
                this.$modal.confirm('您可能没有权限下载此文件，需要申请权限。是否立即申请？').then(() => {
                  this.approvalRequest.fileId = row.id;
                  this.approvalRequest.open = true;
                }).catch(() => {});
              } else {
                // 其他错误正常显示
                this.$modal.msgError('下载失败：' + (error.message || '未知错误'));
              }
            }
          } else if (error.response && error.response.status === 403) {
            // 如果是403错误，可能是权限问题，显示申请权限弹窗
            this.$modal.confirm('您可能没有权限下载此文件，需要申请权限。是否立即申请？').then(() => {
              this.approvalRequest.fileId = row.id;
              this.approvalRequest.open = true;
            }).catch(() => {});
          } else if (error.response && error.response.status === 500) {
            // 处理服务器内部错误（文件不存在等）
            try {
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const errorData = JSON.parse(reader.result);
                  if (errorData.msg) {
                    // 显示友好的错误消息
                    this.$modal.msgError(errorData.msg);
                  } else {
                    this.$modal.msgError('下载失败，请稍后重试');
                  }
                } catch (e) {
                  this.$modal.msgError('下载失败，请稍后重试');
                }
              };
              reader.readAsText(error.response.data);
            } catch (e) {
              this.$modal.msgError('下载失败，请稍后重试');
            }
          } else {
            // 其他错误正常显示
            this.$modal.msgError('下载失败：' + (error.message || '未知错误'));
          }
        })
        .finally(() => {
          // 关闭加载效果
          loading.close()
        })
    },
    // 删除按钮操作
    handleDelete(row) {
      this.$modal.confirm('是否确认删除该文件记录？').then(() => {
        this.$modal.confirm('删除后不可恢复，是否继续？').then(() => {
          this.deleteFile(row.id).then((response) => {
            this.$modal.msgSuccess('删除成功')
          })
        })
      })
    },
    // 上传图纸按钮操作
    handleUploadDrawing(row, modelCode) {
      this.drawingUpload.open = true
      this.drawingUpload.bomItem = row
      this.drawingUpload.productModelCode = modelCode
    },

    // 从响应头中提取文件名
    extractFileNameFromHeaders(headers) {
      // 尝试从 Content-Disposition 头中提取文件名
      const contentDisposition = headers['content-disposition'] || headers['Content-Disposition'];
      if (contentDisposition) {
        // 匹配 filename*=utf-8''filename 或 filename="filename" 格式
        const filenameMatch = contentDisposition.match(/filename\*=utf-8''([^;]+)|filename="([^"]+)"|filename=([^;]+)/);
        if (filenameMatch) {
          const filename = filenameMatch[1] || filenameMatch[2] || filenameMatch[3];
          return decodeURIComponent(filename);
        }
      }

      // 如果没有找到，返回默认文件名
      return null;
    },

    // 下载图纸操作（集成审批系统）
    handleDownloadDrawing(drawingFileId) {
      if (!drawingFileId) return

      // 显示全屏加载
      const loading = this.$loading({
        lock: true,
        text: '正在下载图纸...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })

      // 使用新的API获取完整响应（包含headers）
      downloadFileWithHeaders(drawingFileId)
        .then((response) => {
          const blob = new Blob([response.data])

          // 检查响应类型，确保是二进制数据
          const contentType = response.data.type || 'application/octet-stream';
          if (contentType.includes('json')) {
            // 如果是JSON响应，可能是错误信息
            return response.data.text().then(text => {
              try {
                const errorData = JSON.parse(text);
                if (errorData.msg) {
                  this.$modal.msgError(errorData.msg);
                } else {
                  this.$modal.msgError('下载失败：未知错误');
                }
              } catch (e) {
                this.$modal.msgError('下载失败：解析响应失败');
              }
              throw new Error('下载失败');
            });
          }

          // 从响应头中提取正确的文件名
          const fileName = this.extractFileNameFromHeaders(response.headers) || `图纸_${drawingFileId}.pdf`;

          // 使用工具函数下载文件，使用从响应头提取的文件名
          this.$createDownloadLink(blob, fileName)
        })
        .catch((error) => {
          // 检查是否是权限错误（403）
          if (error.response && error.response.status === 403) {
            try {
              // 尝试解析响应内容
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const errorData = JSON.parse(reader.result);
                  if (errorData.needApproval === true) {
                    // 需要申请权限，显示申请弹窗
                    this.$modal.confirm('下载该文件需要申请。是否立即申请？').then(() => {
                      // 打开申请弹窗
                      this.approvalRequest.fileId = drawingFileId;
                      this.approvalRequest.open = true;
                    }).catch(() => {});
                  } else if (errorData.needApproval === false) {
                    // 用户未被分配权限，显示提示消息
                    this.$modal.msgError(errorData.msg || '您暂无权限下载，请联系管理员');
                  } else if (errorData.msg) {
                    // 其他错误消息
                    this.$modal.msgError(errorData.msg);
                  }
                } catch (e) {
                  // 解析JSON失败，但仍然是403错误，可能是权限问题
                  console.error('解析响应数据失败:', e);
                  // 显示申请权限弹窗
                  this.$modal.confirm('下载该文件需要申请。是否立即申请？').then(() => {
                    this.approvalRequest.fileId = drawingFileId;
                    this.approvalRequest.open = true;
                  }).catch(() => {});
                }
              };
              reader.readAsText(error.response.data);
            } catch (e) {
              // 如果是403错误，可能是权限问题，显示申请权限弹窗
              if (error.response && error.response.status === 403) {
                this.$modal.confirm('下载该文件需要申请。是否立即申请？').then(() => {
                  this.approvalRequest.fileId = drawingFileId;
                  this.approvalRequest.open = true;
                }).catch(() => {});
              } else {
                // 其他错误正常显示
                this.$modal.msgError('下载图纸失败：' + (error.message || '未知错误'));
              }
            }
          } else if (error.response && error.response.status === 500) {
            // 处理服务器内部错误（文件不存在等）
            try {
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const errorData = JSON.parse(reader.result);
                  if (errorData.msg) {
                    // 显示友好的错误消息
                    this.$modal.msgError(errorData.msg);
                  } else {
                    this.$modal.msgError('图纸下载失败，请稍后重试');
                  }
                } catch (e) {
                  this.$modal.msgError('图纸下载失败，请稍后重试');
                }
              };
              reader.readAsText(error.response.data);
            } catch (e) {
              this.$modal.msgError('图纸下载失败，请稍后重试');
            }
          } else {
            // 其他错误正常显示
            this.$modal.msgError('下载图纸失败：' + (error.message || '未知错误'));
          }
        })
        .finally(() => {
          // 关闭加载效果
          loading.close()
        })
    },
    // 提交图纸上传
    submitDrawingUpload(formData) {
      // 检查是否为多文件上传
      const isMultiFileUpload = formData.files && formData.files.length > 0;
      
      if (isMultiFileUpload) {
        // 多文件上传
        const submitData = new FormData()
        
        // 添加文件和对应的类型信息
        formData.files.forEach((fileData, index) => {
          submitData.append('files', fileData.file)
          submitData.append(`fileTypes[${index}]`, fileData.fileType)
        })
        
        // 添加表单数据
        submitData.append('bomItemId', formData.form.id)
        submitData.append('drawingNo', formData.form.drawingNo)
        
        // 添加备注（如果有）
        if (formData.form.remark) {
          submitData.append('remark', formData.form.remark)
        }

        // 显示全屏加载
        const loading = this.$loading({
          lock: true,
          text: '正在上传图纸文件...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })

        this.uploadMultipleDrawings(submitData)
          .then((response) => {
            this.$modal.msgSuccess('多文件上传成功')
            this.drawingUpload.open = false
            // 刷新BOM列表
            this.refreshBomData()
          })
          .catch((error) => {
            console.log('多文件图纸上传失败:', error)
          })
          .finally(() => {
            loading.close()
          })
      } else {
        // 单文件上传（向后兼容）
        const submitData = new FormData()
        submitData.append('file', formData.file)
        submitData.append('bomItemId', formData.form.id)
        submitData.append('drawingNo', formData.form.drawingNo)
        submitData.append('subType', formData.form.subType)
        
        // 添加备注（如果有）
        if (formData.form.remark) {
          submitData.append('remark', formData.form.remark)
        }

        // 显示全屏加载
        const loading = this.$loading({
          lock: true,
          text: '正在上传图纸...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })

        this.uploadDrawing(submitData)
          .then((response) => {
            this.$modal.msgSuccess('上传成功')
            this.drawingUpload.open = false
            // 刷新BOM列表
            this.refreshBomData()
          })
          .catch((error) => {
            console.log('图纸上传失败:', error)
          })
          .finally(() => {
            loading.close()
          })
      }
    },

    // 刷新BOM数据
    refreshBomData() {
      // 通过ref访问FileTable组件并刷新BOM数据
      if (this.$refs.fileTable) {
        this.$refs.fileTable.refreshBomData();
      }
    },

    // 处理BOM物料图纸变更历史
    handleBomItemHistory(bomItemId) {
      console.log('index.vue - handleBomItemHistory 被调用，bomItemId:', bomItemId);
      if (!bomItemId) return

      // 设置当前文件为空对象，但包含一个id属性用于标识
      this.setCurrentFile({ id: bomItemId, fileName: 'BOM物料图纸' })

      // 打开历史记录对话框
      this.$nextTick(() => {
        console.log('准备调用handleHistory方法打开历史记录对话框');
        // 通过FileTable组件触发历史记录对话框打开
        this.handleHistory({ id: bomItemId, hasChanges: true })
      })
    },

    // 处理申请成功
    handleApprovalSuccess() {
      this.$modal.msgSuccess('申请已提交，请等待审批')
      // 刷新文件列表
      this.getFileList()
    },
  },
}
</script>

<style>
.el-scrollbar >>> .el-cascader-menu {
  max-height: 100px !important
}
</style>
