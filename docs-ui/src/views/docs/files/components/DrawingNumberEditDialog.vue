<template>
  <el-dialog 
    title="修改图纸编号" 
    :visible="localVisible" 
    width="400px" 
    append-to-body 
    @close="onClose" 
    :close-on-click-modal="false"
  >
    <el-form ref="editForm" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="产品编码">
        <el-input :value="form.materialCode" disabled />
      </el-form-item>
      <el-form-item label="产品名称">
        <el-input :value="form.materialName" disabled />
      </el-form-item>
      <el-form-item label="当前图纸编号">
        <el-input :value="form.currentDrawingNo || '无'" disabled />
      </el-form-item>
      <el-form-item label="新图纸编号" prop="newDrawingNo">
        <el-input 
          v-model="form.newDrawingNo" 
          placeholder="请输入新的图纸编号"
          :disabled="isSubmitting"
        />
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel" :disabled="isSubmitting">取 消</el-button>
      <el-button 
        type="primary" 
        @click="submitEdit" 
        :loading="isSubmitting"
      >
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateMaterialDrawingNumber } from '@/api/files'

export default {
  name: 'DrawingNumberEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bomItem: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localVisible: this.visible,
      isSubmitting: false,
      form: {
        materialCode: '',
        materialName: '',
        currentDrawingNo: '',
        newDrawingNo: ''
      },
      rules: {
        newDrawingNo: [
          { required: true, message: '新图纸编号不能为空', trigger: 'blur' },
          { min: 1, max: 50, message: '图纸编号长度在 1 到 50 个字符', trigger: 'blur' },
          { validator: this.validateDrawingNumber, trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(newVal) {
      this.localVisible = newVal;
      if (newVal && this.bomItem) {
        this.initForm();
      }
    },
    bomItem: {
      handler(val) {
        if (val && this.visible) {
          this.initForm();
        }
      },
      immediate: true
    }
  },
  methods: {
    initForm() {
      this.form = {
        materialCode: this.bomItem.materialCode || '',
        materialName: this.bomItem.materialName || '',
        currentDrawingNo: this.bomItem.drawingNo || '',
        newDrawingNo: this.bomItem.drawingNo || ''
      };
      // 清除验证状态
      this.$nextTick(() => {
        if (this.$refs.editForm) {
          this.$refs.editForm.clearValidate();
        }
      });
    },
    
    // 自定义验证器：检查图纸编号是否与当前相同
    validateDrawingNumber(rule, value, callback) {
      if (!value || value.trim() === '') {
        callback(new Error('新图纸编号不能为空'));
        return;
      }
      
      if (value === this.form.currentDrawingNo) {
        callback(new Error('新图纸编号不能与当前图纸编号相同'));
        return;
      }
      
      callback();
    },
    
    submitEdit() {
      this.$refs.editForm.validate(async (valid) => {
        if (!valid) {
          return;
        }
        
        this.isSubmitting = true;
        
        try {
          await updateMaterialDrawingNumber({
            materialCode: this.form.materialCode,
            currentDrawingNo: this.form.currentDrawingNo,
            newDrawingNo: this.form.newDrawingNo.trim()
          });
          
          this.$modal.msgSuccess('图纸编号修改成功');
          this.$emit('success', {
            materialCode: this.form.materialCode,
            newDrawingNo: this.form.newDrawingNo.trim()
          });
          this.cancel();
          
        } catch (error) {
          console.error('修改图纸编号失败:', error);
          // 错误提示由axios拦截器统一处理，避免重复显示
        } finally {
          this.isSubmitting = false;
        }
      });
    },
    
    cancel() {
      this.localVisible = false;
      this.$emit('update:visible', false);
    },
    
    onClose() {
      this.localVisible = false;
      this.$emit('update:visible', false);
      this.resetForm();
    },
    
    resetForm() {
      this.form = {
        materialCode: '',
        materialName: '',
        currentDrawingNo: '',
        newDrawingNo: ''
      };
      this.isSubmitting = false;
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields();
      }
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 18px;
}
</style>