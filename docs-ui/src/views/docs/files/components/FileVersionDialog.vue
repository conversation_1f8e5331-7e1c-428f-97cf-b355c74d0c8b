<template>
  <el-dialog :visible.sync="localVisible" width="500px" @close="handleClose">
    <template slot="title">
      <div class="dialog-title">
        <span>变更版本 - </span>
        <el-tooltip :content="file ? file.fileName : ''" placement="top" :disabled="!isFileNameTooLong">
          <span class="file-name-ellipsis">{{ file ? file.fileName : '' }}</span>
        </el-tooltip>
      </div>
    </template>
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="机型编号" v-if="file && file.modelCode">
        <el-input v-model="form.modelCode" disabled></el-input>
      </el-form-item>
      <el-form-item label="当前版本" v-if="file && file.version">
        <el-tooltip :content="file.version" placement="top" :disabled="!isVersionTooLong">
            <el-tag class="version-tag-ellipsis">{{ file.version }}</el-tag>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="新版本" prop="newVersion">
        <el-input v-model="form.newVersion" placeholder="请输入新版本号，格式如：1.0 或 ABC-V1.0"></el-input>
      </el-form-item>
      <el-form-item label="变更理由" prop="changeReason">
        <el-input type="textarea" v-model="form.changeReason" placeholder="请输入变更理由"></el-input>
      </el-form-item>
      <!-- BOM文件类型的两个独立上传控件 -->
      <div v-if="isBomFileType" class="upload-section">
        <el-form-item label="BOM文件" class="upload-item">
          <el-upload
            ref="bomUpload"
            :action="'#'"
            :auto-upload="false"
            :limit="1"
            :on-exceed="handleExceed"
            :on-change="handleBomFileChange"
            :file-list="bomFileList">
            <el-button slot="trigger" size="small" type="primary">选择BOM文件</el-button>
            <div slot="tip" class="el-upload__tip">请上传新版本的BOM文件（必选）</div>
          </el-upload>
        </el-form-item>

        <el-form-item label="图纸关系文件" class="upload-item">
          <el-upload
            ref="drawingRelationUpload"
            :action="'#'"
            :auto-upload="false"
            :limit="1"
            :on-exceed="handleExceed"
            :on-change="handleDrawingRelationFileChange"
            :file-list="drawingRelationFileList">
            <el-button slot="trigger" size="small" type="success">选择图纸关系文件</el-button>
            <div slot="tip" class="el-upload__tip">请上传图纸编号关系文件（可选）</div>
          </el-upload>
        </el-form-item>
      </div>

      <!-- 非BOM文件类型的单个上传控件 -->
      <el-form-item v-else label="上传文件">
        <el-upload
          ref="regularUpload"
          :action="'#'"
          :auto-upload="false"
          :limit="5"
          :on-exceed="handleExceed"
          :on-change="handleRegularFileChange"
          :file-list="regularFileList">
          <el-button slot="trigger" size="small" type="primary">选择文件</el-button>
          <div slot="tip" class="el-upload__tip">请上传新版本文件</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="图纸编号" prop="drawingNo" v-if="showDrawingNo">
        <el-input v-model="form.drawingNo" placeholder="请输入图纸编号"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="localVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { compareVersions } from '../utils/versionUtils'

export default {
  name: 'FileVersionDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    file: {
      type: Object,
      default: null
    }
  },
  data() {
    // 自定义校验规则：版本号必须大于当前版本
    const validateVersion = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入版本号'))
      }

      // 检查新版本号格式是否符合 V<数字>.<数字> 结尾
      const versionRegex = /V(\d+\.\d+)$/i
      if (!versionRegex.test(value)) {
        // 同时检查是否是简单的 x.y 格式
        const simpleVersionRegex = /^(\d+\.\d+)$/
        if (!simpleVersionRegex.test(value)) {
          return callback(new Error('版本号格式不正确，应为 V<数字>.<数字> 结尾或 <数字>.<数字>'))
        }
      }

      // 如果有当前版本，则新版本必须大于当前版本
      if (this.file && this.file.version) {
        const comparisonResult = compareVersions(value, this.file.version)

        if (comparisonResult === 0) {
          return callback(new Error('新版本号不能与当前版本号相同'))
        } else if (comparisonResult < 0) {
          return callback(new Error('新版本号必须大于当前版本号'))
        }
        // comparisonResult > 0 时，校验通过
      }

      callback()
    }

    return {
      form: {
        fileId: '',
        modelCode: '',
        productModelId: '',
        newVersion: '',
        changeReason: '',
        drawingNo: ''
      },
      rules: {
        newVersion: [
          { required: true, message: '请输入新版本号', trigger: 'blur' },
          { validator: validateVersion, trigger: 'blur' }
        ],
        changeReason: [
          { required: true, message: '请输入变更理由', trigger: 'blur' }
        ]
        // 移除 files 验证规则，改为手动验证
      },
      // 分开保存不同类型的文件
      bomFileList: [],
      drawingRelationFileList: [],
      regularFileList: [],
      loading: false,
      showDrawingNo: false
    }
  },
  watch: {
    // 监听 visible prop 变化，当对话框打开时重置表单
    visible(val) {
      if (val) {
        this.resetForm()
      }
    },
    // 监听 file prop 变化，更新表单数据
    file(val) {
      if (val) {
        this.form.fileId = val.id
        this.form.modelCode = val.modelCode || ''
        this.form.productModelId = val.productModelId || ''
        // 判断是否显示图纸编号字段
        this.showDrawingNo = val.fileType === 'DRAWING'
      }
    }
  },
  computed: {
    // 使用计算属性来处理对话框的可见性
    localVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      }
    },
    isFileNameTooLong() {
      return this.file && this.file.fileName && this.file.fileName.length > 20
    },
    // 判断版本号是否过长
    isVersionTooLong() {
        const maxLength = 20; // 定义长度阈值
        return this.file && this.file.version && this.file.version.length > maxLength;
    },
    // 判断是否是BOM文件类型
    isBomFileType() {
      return this.file && this.file.fileType === 'BOM'
    }
  },
  methods: {
    resetForm() {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      // 重置所有文件列表
      this.bomFileList = []
      this.drawingRelationFileList = []
      this.regularFileList = []
      this.form = {
        fileId: this.file ? this.file.id : null,
        modelCode: this.file ? this.file.modelCode : '',
        productModelId: this.file ? this.file.productModelId : '',
        newVersion: '',
        changeReason: '',
        drawingNo: ''
      }
      this.loading = false
    },
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    },
    handleExceed() {
      this.$message.warning('最多只能上传5个文件')
    },
    handleBomFileChange(file, fileList) {
      // 处理BOM文件变更
      this.bomFileList = fileList.map(f => {
        if (!f.raw && f.originFileObj) {
          f.raw = f.originFileObj
        }
        return f
      })
    },

    handleDrawingRelationFileChange(file, fileList) {
      // 处理图纸关系文件变更
      this.drawingRelationFileList = fileList.map(f => {
        if (!f.raw && f.originFileObj) {
          f.raw = f.originFileObj
        }
        return f
      })
    },

    handleRegularFileChange(file, fileList) {
      // 处理常规文件变更
      this.regularFileList = fileList.map(f => {
        if (!f.raw && f.originFileObj) {
          f.raw = f.originFileObj
        }
        return f
      })
    },
    submitForm() {
      let files = []

      if (this.isBomFileType) {
        // BOM文件类型的验证和处理
        const bomFiles = this.$refs.bomUpload.uploadFiles
        if (!bomFiles || bomFiles.length === 0) {
          this.$message.error('请上传BOM文件')
          return
        }

        // 获取图纸关系文件（可选）
        const drawingRelationFiles = this.$refs.drawingRelationUpload.uploadFiles

        // 准备文件数组，确保 BOM 文件在第一位
        files = bomFiles
        if (drawingRelationFiles && drawingRelationFiles.length > 0) {
          files = files.concat(drawingRelationFiles)
        }
      } else {
        // 非BOM文件类型的验证和处理
        const regularFiles = this.$refs.regularUpload.uploadFiles
        if (!regularFiles || regularFiles.length === 0) {
          this.$message.error('请上传文件')
          return
        }

        files = regularFiles
      }

      // 表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true

          // 准备表单数据
          const formData = new FormData()
          formData.append('fileId', this.form.fileId)
          formData.append('productModelId', this.form.productModelId)
          formData.append('newVersion', this.form.newVersion)
          formData.append('changeReason', this.form.changeReason)
          formData.append('drawingNo', this.form.drawingNo || '')

          // 添加文件
          files.forEach(file => {
            formData.append('files', file.raw || file)
          })

          // 发送请求
          this.$emit('submit', formData, () => {
            this.loading = false
            this.localVisible = false
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.dialog-title {
  display: flex;
  align-items: center;
}

.file-name-ellipsis {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}

.upload-section {
  padding: 10px;
  margin-bottom: 15px;
}

.upload-item {
  margin-bottom: 15px;
}

.upload-item:last-child {
  margin-bottom: 0;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
  line-height: 1.4;
}

/* Style for ellipsis in version tag - Trying display: block */
.el-form-item .version-tag-ellipsis {
  display: block; /* Changed from inline-block */
  max-width: 200px; /* Adjust max width as needed */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* vertical-align might not be needed or effective with display: block */
  /* vertical-align: baseline; */
}
</style>
