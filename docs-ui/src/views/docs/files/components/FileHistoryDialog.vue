<template>
  <el-dialog :visible.sync="dialogVisible" width="900px" @close="handleClose" custom-class="file-history-dialog">
    <template slot="title">
      <div class="dialog-title">
        <i class="el-icon-time dialog-title-icon"></i>
        <span>版本历史 - </span>
        <el-tooltip :content="file ? file.fileName : ''" placement="top" :disabled="!isFileNameTooLong">
          <span class="file-name-ellipsis">{{ file ? file.fileName : '' }}</span>
        </el-tooltip>
      </div>
    </template>
    <el-table v-loading="loading" :data="historyList" stripe style="width: 100%;">
      <el-table-column label="旧版本" align="center" prop="oldVersion" width="160">
        <template slot-scope="scope">
          <el-tag type="info" size="small" v-if="scope.row.oldVersion">{{ scope.row.oldVersion }}</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="新版本" align="center" prop="newVersion" width="160">
        <template slot-scope="scope">
          <el-tag type="success" size="small" v-if="scope.row.newVersion">{{ scope.row.newVersion }}</el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="变更理由" align="center" prop="changeReason" show-overflow-tooltip>
         <template slot-scope="scope">
          {{ scope.row.changeReason || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="变更人" align="center" prop="changedByName" width="120"/>
      <el-table-column label="变更时间" align="center" prop="changedAt" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.changedAt, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-tooltip content="下载此版本文件" placement="top" v-if="scope.row.fileInfo && scope.row.fileInfo.filePath">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-download"
              @click="handleDownload(scope.row.fileInfo)"
            ></el-button>
          </el-tooltip>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="currentPage"
      :limit.sync="pageSize"
      @pagination="getHistoryList"
      style="margin-top: 20px;"
    />
  </el-dialog>
</template>

<script>
import { parseTime } from '@/utils/ruoyi'
import Pagination from '@/components/Pagination'

export default {
  name: 'FileHistoryDialog',
  components: {
    Pagination
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    file: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      historyList: [],
      loading: false,
      total: 0,
      currentPage: 1,
      pageSize: 10
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.file) {
        this.getHistoryList()
      }
    }
  },
  computed: {
    isFileNameTooLong() {
      return this.file && this.file.fileName && this.file.fileName.length > 20
    }
  },
  methods: {
    parseTime,
    handleClose() {
      this.$emit('update:visible', false)
    },
    getHistoryList(pagination) {
      console.log(pagination)
      console.log(this.file)
      if (!this.file || !this.file.id) {
        return
      }
      console.log(1)
      if (pagination) {
        this.currentPage = pagination.page
        this.pageSize = pagination.limit
      }
      this.loading = true
      // 构建查询参数
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize
      }

      // 检查是否是BOM物料图纸历史
      if (this.file.fileName === 'BOM物料图纸') {
        // BOM物料图纸历史查询
        params.bomItemId = this.file.id
      } else {
        // 普通文件历史查询
        params.fileId = this.file.id
      }

      // 发送请求获取历史记录
      this.$emit('fetch-history', params, (response) => {
        this.historyList = response.rows || []
        this.total = response.total || 0
        this.loading = false
      })
    },
    handleDownload(fileInfo) {
      this.$emit('download', fileInfo)
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.dialog-title {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.dialog-title-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF; /* Element UI 主题蓝 */
}

.file-name-ellipsis {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: middle; /* 垂直居中对齐 */
}

/* 为表格单元格添加一些内边距 */
.el-table td, .el-table th {
  padding: 8px 0;
}

/* 自定义对话框样式，防止内容过于贴近边缘 */
.file-history-dialog .el-dialog__body {
  padding: 15px 20px;
}

/* 鼠标悬停在下载按钮上时显示手型光标 */
.el-table .el-button[icon="el-icon-download"] {
  cursor: pointer;
}
</style>
