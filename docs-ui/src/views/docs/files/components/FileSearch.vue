<template>
  <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px" style="margin: 0;">
    <el-form-item label="产品型号" prop="productModel">
      <el-select v-model="queryParams.productModelId" placeholder="请选择产品型号" clearable>
        <el-option
          v-for="item in productModelOptions"
          :key="item.id"
          :label="item.modelCode"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="文件类型" prop="fileType">
      <el-cascader
        v-model="queryParams.fileType"
        :options="fileTypeOptions"
        :props="{ checkStrictly: false }"
        clearable
        placeholder="请选择文件类型"
      />
    </el-form-item>
    <el-form-item label="文件状态" prop="status">
      <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
        <el-option label="使用" value="ACTIVE" />
        <el-option label="已报废" value="OBSOLETE" />
      </el-select>
    </el-form-item>
    <el-form-item label="物料编号" prop="materialNo">
      <el-input v-model="queryParams.materialNo" placeholder="请输入物料编号" clearable />
    </el-form-item>
    <el-form-item label="图纸编号" prop="drawingNo">
      <el-input v-model="queryParams.drawingNo" placeholder="请输入图纸编号" clearable />
    </el-form-item>
    <div style="margin-left: 30px">
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </div>
  </el-form>
</template>

<script>
export default {
  name: 'FileSearch',
  props: {
    showSearch: {
      type: Boolean,
      default: true
    },
    productModelOptions: {
      type: Array,
      default: () => []
    },
    fileTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productModelId: undefined,
        fileType: undefined,
        status: undefined,
        materialNo: undefined,
        drawingNo: undefined,
        fileId: undefined
      }
    };
  },
  methods: {
    // 搜索按钮操作
    handleQuery() {
      this.$emit('query', JSON.parse(JSON.stringify(this.queryParams)));
    },
    // 重置按钮操作
    resetQuery() {
      this.resetForm("queryForm");
      this.$emit('reset');
    },
    // 表单重置
    resetForm(formName) {
      if (this.$refs[formName]) {
        this.$refs[formName].resetFields();
      }
    }
  }
}
</script>
