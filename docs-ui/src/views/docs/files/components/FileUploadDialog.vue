<template>
  <el-dialog title="上传文件" :visible.sync="dialogVisible" width="500px" append-to-body @close="onClose" :close-on-click-modal="false">
    <el-form ref="uploadForm" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="产品型号" prop="productModel">
        <el-select v-model="form.productModel" placeholder="请选择产品型号" clearable>
          <el-option label="无" value=""></el-option>
          <el-option
            v-for="item in productModelOptions"
            :key="item.id"
            :label="item.modelCode"
            :value="item.modelCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文件类型" prop="fileType">
        <el-cascader
          v-model="form.fileType"
          :options="fileTypeOptions"
          :props="{ checkStrictly: false }"
          @change="handleFileTypeChange"
          clearable
          placeholder="请选择文件类型"
        />
      </el-form-item>
      <!-- 图纸编号，仅当选择图纸类型时显示 -->
      <el-form-item
        v-if="form.fileType && form.fileType[0] === 'DRAWING'"
        label="图纸编号"
        prop="drawingNo">
        <el-input v-model="form.drawingNo" placeholder="请输入图纸编号" />
      </el-form-item>

      <!-- 图纸子类型，仅当选择图纸类型时显示 -->
      <el-form-item
        v-if="form.fileType && form.fileType[0] === 'DRAWING'"
        label="图纸类型"
        prop="drawingSubType">
        <el-select v-model="form.drawingSubType" placeholder="请选择图纸类型" @change="handleDrawingSubTypeChange">
          <el-option label="PDF文档" value="PDF" />
          <el-option label="2D图纸" value="2D" />
          <el-option label="3D图纸" value="3D" />
          <el-option label="2D/3D压缩包" value="2D/3D" />
        </el-select>
      </el-form-item>

      <el-form-item label="版本号" prop="version">
        <el-input v-model="form.version" placeholder="请输入版本号(如:1.0)" />
      </el-form-item>
      <!-- BOM文件类型的两个独立上传控件 -->
      <div v-if="isBomFileType" class="upload-section">
        <el-form-item label="BOM文件" class="upload-item">
          <el-upload
            ref="bomUpload"
            :limit="1"
            :auto-upload="false"
            :on-change="handleBomFileChange"
            :file-list="bomFileList"
            action="#">
            <el-button slot="trigger" size="small" type="primary">选择BOM文件</el-button>
            <div slot="tip" class="el-upload__tip">请上传BOM文件（必选）</div>
          </el-upload>
        </el-form-item>

        <el-form-item label="图纸关系文件" class="upload-item">
          <el-upload
            ref="drawingRelationUpload"
            :limit="1"
            :auto-upload="false"
            :on-change="handleDrawingRelationFileChange"
            :file-list="drawingRelationFileList"
            action="#">
            <el-button slot="trigger" size="small" type="success">选择图纸关系文件</el-button>
            <div slot="tip" class="el-upload__tip">请上传图纸编号关系文件（可选）</div>
          </el-upload>
        </el-form-item>
      </div>
      
      <!-- 非BOM文件类型的单个上传控件 -->
      <el-form-item v-else label="上传文件" prop="file">
        <el-upload
          ref="regularUpload"
          :limit="10"
          :accept="currentAcceptTypes"
          :auto-upload="false"
          :on-change="handleRegularFileChange"
          :before-upload="beforeUpload"
          :file-list="regularFileList"
          multiple
          action="#">
          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
          <div slot="tip" class="el-upload__tip">{{ uploadTipText }}</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitUpload">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'FileUploadDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    productModelOptions: {
      type: Array,
      default: () => []
    },
    fileTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        productModel: undefined,
        fileType: undefined,
        drawingNo: undefined,
        drawingSubType: undefined,
        version: '1.0'
      },
      rules: {
        fileType: [
          { required: true, message: '文件类型不能为空', trigger: 'change' }
        ],
        version: [
          { required: true, message: '版本号不能为空', trigger: 'blur' }
        ]
      },
      // 分开保存不同类型的文件
      bomFileList: [],
      drawingRelationFileList: [],
      regularFileList: [],
      // 文件类型配置
      fileTypeConfig: {
        'PDF': {
          accept: '.pdf',
          extensions: ['pdf'],
          tip: '只能上传PDF格式的图纸文件，无文件大小限制'
        },
        '2D': {
          accept: '*',
          extensions: [],
          tip: '支持上传任意格式的2D图纸文件，无文件大小限制'
        },
        '3D': {
          accept: '*',
          extensions: [],
          tip: '支持上传任意格式的3D图纸文件，无文件大小限制'
        },
        '2D/3D': {
          accept: '.zip,.rar,.7z,.tar,.gz,.bz2',
          extensions: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'],
          tip: '只能上传2D/3D图纸压缩文件格式（zip、rar、7z、tar、gz、bz2），无文件大小限制'
        }
      }
    };
  },
  computed: {
    // 使用计算属性来处理对话框的可见性
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      }
    },
    // 判断是否是BOM文件类型
    isBomFileType() {
      return this.form.fileType && this.form.fileType[0] === 'BOM';
    },
    currentAcceptTypes() {
      if (this.form.fileType && this.form.fileType[0] === 'DRAWING' && this.form.drawingSubType) {
        return this.fileTypeConfig[this.form.drawingSubType]?.accept || '';
      }
      return '';
    },
    uploadTipText() {
      if (this.form.fileType && this.form.fileType[0] === 'DRAWING') {
        if (!this.form.drawingSubType) {
          return '请先选择图纸类型';
        }
        return this.fileTypeConfig[this.form.drawingSubType]?.tip || '';
      }
      return '支持多个文件上传，保留原始文件名';
    }
  },
  methods: {
    handleFileTypeChange() {
      if (this.form.fileType && this.form.fileType[0] === 'DRAWING') {
        // 如果是图纸类型，增加图纸编号和图纸子类型的验证规则
        this.$set(this.rules, 'drawingNo', [
          { required: true, message: '图纸编号不能为空', trigger: 'blur' }
        ]);
        this.$set(this.rules, 'drawingSubType', [
          { required: true, message: '图纸类型不能为空', trigger: 'change' }
        ]);
      } else {
        this.$delete(this.rules, 'drawingNo');
        this.$delete(this.rules, 'drawingSubType');
      }
      // 重置图纸子类型和文件列表
      this.form.drawingSubType = undefined;
      this.regularFileList = [];
      this.$refs.regularUpload && this.$refs.regularUpload.clearFiles();
    },
    handleDrawingSubTypeChange() {
      // 当图纸子类型改变时，清空已选择的文件
      this.regularFileList = [];
      this.$refs.regularUpload && this.$refs.regularUpload.clearFiles();
    },
    handleBomFileChange(file, fileList) {
      // 处理BOM文件变更
      this.bomFileList = fileList;
    },
    
    handleDrawingRelationFileChange(file, fileList) {
      // 处理图纸关系文件变更
      this.drawingRelationFileList = fileList;
    },
    
    handleRegularFileChange(file, fileList) {
      // 处理常规文件变更
      this.regularFileList = fileList;
    },
    beforeUpload(file) {
      if (this.form.fileType && this.form.fileType[0] === 'DRAWING' && this.form.drawingSubType) {
        const config = this.fileTypeConfig[this.form.drawingSubType];
        if (!config) {
          this.$message.error('无效的图纸类型');
          return false;
        }

        // 验证文件扩展名 (仅对有扩展名限制的类型进行验证)
        if (config.extensions && config.extensions.length > 0) {
          const fileName = file.name.toLowerCase();
          const isValidExtension = config.extensions.some(ext => fileName.endsWith('.' + ext));

          if (!isValidExtension) {
            this.$message.error(`只能上传${config.extensions.join('、')}格式的文件`);
            return false;
          }
        }
      }

      return true;
    },
    submitUpload() {
      this.$refs.uploadForm.validate(valid => {
        if (!valid) {
          return;
        }
        
        let files = [];
        
        if (this.isBomFileType) {
          // BOM文件类型的验证和处理
          if (this.bomFileList.length === 0) {
            this.$modal.msgError('请选择要上传的BOM文件');
            return;
          }
          
          // 组合所有文件，确保 BOM 文件在第一位
          files = [...this.bomFileList];
          if (this.drawingRelationFileList.length > 0) {
            files.push(...this.drawingRelationFileList);
          }
        } else {
          // 非BOM文件类型的验证和处理
          if (this.regularFileList.length === 0) {
            this.$modal.msgError('请选择要上传的文件');
            return;
          }
          
          files = [...this.regularFileList];
        }
        
        // 发送表单数据和文件列表
        this.$emit('submit', {
          form: JSON.parse(JSON.stringify(this.form)),
          fileList: files
        });
      });
    },
    cancel() {
      this.$emit('update:visible', false);
    },
    onClose() {
      this.$emit('update:visible', false);
      this.resetForm();
    },
    resetForm() {
      this.form = {
        productModel: undefined,
        fileType: undefined,
        drawingNo: undefined,
        drawingSubType: undefined,
        version: undefined
      };
      // 重置所有文件列表
      this.bomFileList = [];
      this.drawingRelationFileList = [];
      this.regularFileList = [];
      this.$refs.uploadForm && this.$refs.uploadForm.resetFields();
      // 清空所有上传控件的文件
      this.$refs.bomUpload && this.$refs.bomUpload.clearFiles();
      this.$refs.drawingRelationUpload && this.$refs.drawingRelationUpload.clearFiles();
      this.$refs.regularUpload && this.$refs.regularUpload.clearFiles();
    }
  }
}
</script>
