<template>
  <el-dialog title="上传图纸文件" :visible="localVisible" width="500px" append-to-body @close="onClose" :close-on-click-modal="false">
    <el-form ref="drawingUploadForm" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="机型">
        <el-input :value="productModelCode" disabled />
      </el-form-item>
      <el-form-item label="产品编码">
        <el-input :value="form.materialCode" disabled />
      </el-form-item>
      <el-form-item label="产品名称">
        <el-input :value="form.materialName" disabled />
      </el-form-item>
      <el-form-item label="图纸编号" prop="drawingNo">
        <el-input v-model="form.drawingNo" placeholder="请输入图纸编号" />
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input 
          v-model="form.remark" 
          type="textarea" 
          placeholder="请输入备注信息（可选，最多500个字符）"
          :autosize="{ minRows: 2, maxRows: 4 }"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="文件类型" prop="selectedTypes">
        <el-select 
          v-model="form.selectedTypes" 
          multiple 
          placeholder="请选择要上传的文件类型" 
          @change="handleFileTypeSelectionChange"
          style="width: 100%"
        >
          <el-option
            v-for="fileType in fileTypeOptions"
            :key="fileType.value"
            :label="fileType.label"
            :value="fileType.value"
            :disabled="fileType.disabled"
          >
          </el-option>
        </el-select>
        <div class="file-type-hint" style="margin-top: 5px; font-size: 12px; color: #909399;">
          <span v-if="requiredFileTypes.length > 0" style="color: #F56C6C;">
            红色标记为必须重新上传的文件类型（保持版本一致性）
          </span>
          <span v-else>
            可选择多个文件类型，每个类型只能上传一个文件
          </span>
        </div>
      </el-form-item>

      <!-- 动态显示上传按钮，每个选中的文件类型显示一个上传按钮 -->
      <el-form-item 
        v-for="fileType in form.selectedTypes" 
        :key="fileType" 
        :label="fileTypeConfig[fileType].label + '文件'"
        :prop="'file_' + fileType"
      >
        <el-upload
          :ref="'upload_' + fileType"
          :limit="1"
          :accept="fileTypeConfig[fileType].accept"
          :action="uploadUrl"
          :disabled="isUploading"
          :on-change="(file, fileList) => handleFileChange(file, fileList, fileType)"
          :before-upload="(file) => beforeUpload(file, fileType)"
          :auto-upload="false"
          :file-list="fileLists[fileType] || []"
        >
          <el-button size="small" type="primary">选择{{ fileTypeConfig[fileType].label }}文件</el-button>
          <div class="el-upload__tip" slot="tip">{{ fileTypeConfig[fileType].tip }}</div>
        </el-upload>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitUpload">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getMaterialExistingFileTypes } from '@/api/files'

export default {
  name: 'DrawingUploadDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bomItem: {
      type: Object,
      default: () => ({})
    },
    productModelCode: {
      type: String,
      default: ''
    },
    uploadUrl: {
      type: String,
      default: '#'
    },
    isUploading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localVisible: this.visible,
      form: {
        id: undefined,
        fileId: undefined,
        materialName: '',
        materialCode: '',
        drawingNo: '',
        remark: '', // 备注字段
        selectedTypes: ['PDF'] // 默认选中PDF
      },
      rules: {
        drawingNo: [
          { required: true, message: '图纸编号不能为空', trigger: 'blur' }
        ],
        selectedTypes: [
          { required: true, message: '请选择至少一个文件类型', trigger: 'change' }
        ]
      },
      fileLists: {}, // 每个文件类型对应一个文件列表
      requiredFileTypes: [], // 必须重新上传的文件类型
      // 基础文件类型配置
      baseFileTypeConfig: {
        'PDF': {
          label: 'PDF文档',
          accept: '.pdf',
          extensions: ['pdf'],
          tip: '只能上传PDF格式的文件，无文件大小限制'
        },
        '2D': {
          label: '2D图纸',
          accept: '*',
          extensions: [], // 不限制后缀
          tip: '支持上传任意格式的2D图纸文件，无文件大小限制'
        },
        '3D': {
          label: '3D图纸',
          accept: '*',
          extensions: [], // 不限制后缀
          tip: '支持上传任意格式的3D图纸文件，无文件大小限制'
        },
        '2D/3D': {
          label: '2D/3D压缩包',
          accept: '.zip,.rar,.7z,.tar,.gz,.bz2',
          extensions: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'],
          tip: '只能上传压缩文件格式（zip、rar、7z、tar、gz、bz2），无文件大小限制'
        }
      }
    };
  },
  computed: {
    // 检查用户是否有任何文件类型上传权限（移除权限检查，允许所有用户上传）
    hasAnyUploadPermission() {
      return true; // 允许所有用户上传图纸
    },
    
    // 动态生成文件类型选项
    fileTypeOptions() {
      return [
        {
          value: 'PDF',
          label: 'PDF文档',
          disabled: this.requiredFileTypes.includes('PDF')
        },
        {
          value: '2D',
          label: '2D图纸',
          disabled: this.requiredFileTypes.includes('2D')
        },
        {
          value: '3D',
          label: '3D图纸',
          disabled: this.requiredFileTypes.includes('3D')
        },
        {
          value: '2D/3D',
          label: '2D/3D压缩包',
          disabled: this.requiredFileTypes.includes('2D/3D')
        }
      ];
    },
    
    // 获取文件类型配置（兼容原有代码）
    fileTypeConfig() {
      return this.baseFileTypeConfig;
    }
  },
  watch: {
    visible(newVal) {
      this.localVisible = newVal;
      // 当弹窗打开时，重新设置表单数据
      if (newVal && this.bomItem && this.bomItem.id) {
        this.form = {
          id: this.bomItem.id,
          fileId: this.bomItem.fileId,
          materialName: this.bomItem.materialName || '',
          materialCode: this.bomItem.materialCode || '',
          drawingNo: this.bomItem.drawingNo || '',
          remark: this.bomItem.remark || '',
          selectedTypes: ['PDF'] // 先设置默认值，后面会根据已存在文件类型调整
        };
        // 清空文件列表
        this.fileLists = {};
        // 获取已存在的文件类型
        this.loadExistingFileTypes();
        // 重置表单验证状态
        this.$nextTick(() => {
          if (this.$refs.drawingUploadForm) {
            this.$refs.drawingUploadForm.clearValidate();
          }
        });
      }
    },
    bomItem: {
      handler(val) {
        if (val && val.id) {
          this.form = {
            id: val.id,
            fileId: val.fileId,
            materialName: val.materialName || '',
            materialCode: val.materialCode || '',
            drawingNo: val.drawingNo || '',
            remark: val.remark || '', // 备注字段回显
            selectedTypes: ['PDF'] // 重置为默认选中PDF
          };
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取已存在的文件类型
    async loadExistingFileTypes() {
      if (!this.form.materialCode) {
        return;
      }
      
      try {
        const response = await getMaterialExistingFileTypes(this.form.materialCode);
        if (response.code === 200) {
          this.requiredFileTypes = response.data || [];
          
          // 如果有必须重新上传的文件类型，将它们添加到选中列表
          if (this.requiredFileTypes.length > 0) {
            const allRequiredTypes = [...this.requiredFileTypes];
            // 确保至少包含PDF
            if (!allRequiredTypes.includes('PDF')) {
              allRequiredTypes.push('PDF');
            }
            this.form.selectedTypes = allRequiredTypes;
          }
        }
      } catch (error) {
        console.error('获取已存在文件类型失败:', error);
        this.requiredFileTypes = [];
      }
    },
    
    // 验证文件类型选择
    validateFileTypeSelection() {
      if (!this.form.selectedTypes || this.form.selectedTypes.length === 0) {
        this.$modal.msgError('请选择至少一个文件类型');
        return false;
      }
      
      // 检查是否包含所有必须的文件类型
      const missingRequiredTypes = this.requiredFileTypes.filter(
        type => !this.form.selectedTypes.includes(type)
      );
      
      if (missingRequiredTypes.length > 0) {
        this.$modal.msgError(`必须重新上传以下文件类型以保持版本一致性: ${missingRequiredTypes.join(', ')}`);
        return false;
      }
      
      return true;
    },
    // 处理文件类型选择变化
    handleFileTypeSelectionChange(selectedTypes) {
      // 检查是否试图取消选择必须的文件类型
      const missingRequiredTypes = this.requiredFileTypes.filter(
        type => !selectedTypes.includes(type)
      );
      
      if (missingRequiredTypes.length > 0) {
        this.$modal.msgWarning(`以下文件类型必须重新上传以保持版本一致性，不能取消选择: ${missingRequiredTypes.join(', ')}`);
        // 重新添加必须的文件类型
        const updatedTypes = [...new Set([...selectedTypes, ...this.requiredFileTypes])];
        this.$nextTick(() => {
          this.form.selectedTypes = updatedTypes;
        });
        return;
      }
      
      // 清空不再需要的文件类型对应的文件列表
      Object.keys(this.fileLists).forEach(fileType => {
        if (!selectedTypes.includes(fileType)) {
          this.$delete(this.fileLists, fileType);
          // 清空对应的upload组件
          const uploadRef = this.$refs['upload_' + fileType];
          if (uploadRef && uploadRef[0]) {
            uploadRef[0].clearFiles();
          }
        }
      });
    },
    // 处理文件选择变化
    handleFileChange(file, fileList, fileType) {
      this.$set(this.fileLists, fileType, fileList);
    },
    // 文件上传前验证
    beforeUpload(file, fileType) {
      const config = this.fileTypeConfig[fileType];
      if (!config) {
        this.$message.error('无效的文件类型');
        return false;
      }

      // 验证文件扩展名 (仅对有扩展名限制的类型进行验证)
      if (config.extensions && config.extensions.length > 0) {
        const fileName = file.name.toLowerCase();
        const isValidExtension = config.extensions.some(ext => fileName.endsWith('.' + ext));

        if (!isValidExtension) {
          this.$message.error(`${config.label}只能上传${config.extensions.join('、')}格式的文件`);
          return false;
        }
      }

      // 图纸文件不再限制大小，移除文件大小验证

      return true;
    },
    submitUpload() {
      // 验证文件类型选择
      if (!this.validateFileTypeSelection()) {
        return;
      }
      
      this.$refs.drawingUploadForm.validate(valid => {
        if (!valid) {
          return;
        }
        
        // 验证每个选中的文件类型都有对应的文件
        const uploadFiles = [];
        let hasError = false;
        
        for (const fileType of this.form.selectedTypes) {
          const fileList = this.fileLists[fileType];
          if (!fileList || fileList.length === 0) {
            this.$modal.msgError(`请选择${this.fileTypeConfig[fileType].label}文件`);
            hasError = true;
            break;
          }
          uploadFiles.push({
            fileType: fileType,
            file: fileList[0].raw
          });
        }
        
        if (hasError) {
          return;
        }
        
        // 构造多文件上传数据
        const formData = {
          form: JSON.parse(JSON.stringify(this.form)),
          files: uploadFiles
        };
        
        this.$emit('submit', formData);
      });
    },
    cancel() {
      this.localVisible = false;
      this.$emit('update:visible', false);
    },
    onClose() {
      this.localVisible = false;
      this.$emit('update:visible', false);
      this.resetForm();
    },
    resetForm() {
      this.form = {
        id: undefined,
        fileId: undefined,
        materialName: '',
        materialCode: '',
        drawingNo: '',
        remark: '',
        selectedTypes: ['PDF'] // 重置为默认选中PDF
      };
      this.fileLists = {};
      this.requiredFileTypes = []; // 清空必须文件类型
      this.$refs.drawingUploadForm && this.$refs.drawingUploadForm.resetFields();
      
      // 清空所有upload组件
      Object.keys(this.baseFileTypeConfig).forEach(fileType => {
        const uploadRef = this.$refs['upload_' + fileType];
        if (uploadRef && uploadRef[0]) {
          uploadRef[0].clearFiles();
        }
      });
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.el-form-item {
  margin-bottom: 18px;
}

.required-file-type {
  color: #F56C6C !important;
  font-weight: bold !important;
}

.required-badge {
  background-color: #F56C6C !important;
  color: white !important;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  margin-left: 5px;
  display: inline-block;
}

.file-type-hint {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}

/* 深度选择器确保样式能穿透Element UI */
::v-deep .el-select-dropdown__item .required-file-type {
  color: #F56C6C !important;
  font-weight: bold !important;
}

::v-deep .el-select-dropdown__item .required-badge {
  background-color: #F56C6C !important;
  color: white !important;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  margin-left: 5px;
  display: inline-block;
}
</style>
