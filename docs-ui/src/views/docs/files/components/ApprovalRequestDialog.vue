<template>
  <el-dialog
    :title="title"
    :visible="localVisible"
    width="500px"
    :close-on-click-modal="false"
    @closed="resetForm"
    @update:visible="$emit('update:visible', $event)"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="文件名称" prop="fileName">
        <el-input v-model="form.fileName" disabled />
      </el-form-item>
      <el-form-item label="文件类型" prop="fileType">
        <el-input v-model="form.fileType" disabled />
      </el-form-item>
      <el-form-item label="版本" prop="version">
        <el-input v-model="form.version" disabled />
      </el-form-item>
      <el-form-item label="申请理由" prop="reason">
        <el-input
          v-model="form.reason"
          type="textarea"
          :rows="4"
          placeholder="请输入申请理由"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" :loading="loading" @click="submitForm">提交申请</el-button>
      <el-button @click="closeDialog">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getFileDetail } from '@/api/files/index'
import { submitApprovalRequest } from '@/api/files/approval'

export default {
  name: 'ApprovalRequestDialog',
  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 文件ID
    fileId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      title: '文件下载申请',
      loading: false,
      // 表单参数
      form: {
        fileId: null,
        fileName: '',
        fileType: '',
        version: '',
        reason: ''
      },
      // 表单校验
      rules: {
        reason: [
          { required: true, message: '申请理由不能为空', trigger: 'blur' },
          { min: 5, max: 200, message: '申请理由长度在 5 到 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 使用计算属性来处理对话框的可见性
    localVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit('update:visible', value);
      }
    }
  },
  watch: {
    // 监听 localVisible 而不是 visible，确保与实际使用的计算属性一致
    localVisible(val) {
      if (val && this.fileId) {
        this.form.fileId = this.fileId
        this.getFileInfo(this.fileId)
      }
    }
  },
  methods: {
    /** 获取文件信息 */
    getFileInfo(fileId) {
      this.loading = true
      getFileDetail(fileId).then(response => {
        const file = response.data
        this.form.fileName = file.fileName
        this.form.fileType = file.fileType
        this.form.version = file.version
        this.loading = false
      }).catch(error => {
        this.$message.error('获取文件信息失败: ' + error)
        this.loading = false
        this.closeDialog()
      })
    },
    /** 关闭对话框 */
    closeDialog() {
      this.$emit('update:visible', false)
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.loading = true
          submitApprovalRequest(this.form.fileId, this.form.reason).then(() => {
            this.$modal.msgSuccess('申请提交成功')
            this.closeDialog()
            this.$emit('success')
          }).catch(error => {
            // 如果错误已被全局拦截器处理，则不重复显示
            if (!error._handled) {
              this.$modal.msgError('申请提交失败: ' + error)
            }
            console.error('申请提交失败:', error)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    /** 重置表单 */
    resetForm() {
      this.form = {
        fileId: null,
        fileName: '',
        fileType: '',
        version: '',
        reason: ''
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    }
  }
}
</script>
