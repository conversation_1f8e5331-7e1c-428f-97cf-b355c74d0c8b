<template>
  <el-dialog :title="title" :visible="localVisible" width="500px" append-to-body @close="onClose">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="产品型号" prop="modelCode">
        <el-input v-model="form.modelCode" placeholder="请输入产品型号" />
      </el-form-item>
      <el-form-item label="初始状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="ACTIVE">使用</el-radio>
          <el-radio label="OBSOLETE">已报废</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ProductModelDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增机型'
    }
  },
  data() {
    return {
      localVisible: this.visible,
      form: {
        modelCode: undefined,
        status: 'ACTIVE'
      },
      rules: {
        modelCode: [
          { required: true, message: '产品型号不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择初始状态', trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    visible(newVal) {
      this.localVisible = newVal;
    }
  },
  methods: {
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('submit', JSON.parse(JSON.stringify(this.form)));
        }
      });
    },
    cancel() {
      this.localVisible = false;
      this.$emit('update:visible', false);
    },
    onClose() {
      this.localVisible = false;
      this.$emit('update:visible', false);
      this.resetForm();
    },
    resetForm() {
      this.form = {
        modelCode: undefined,
        status: 'ACTIVE'
      };
      this.$refs.form && this.$refs.form.resetFields();
    }
  }
}
</script>
