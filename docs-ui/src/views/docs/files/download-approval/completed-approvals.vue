<template>
  <div class="completed-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文件名称" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入文件名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="requesterName">
        <el-input
          v-model="queryParams.requesterName"
          placeholder="请输入申请人"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 申请列表 -->
    <el-table
      v-loading="loading"
      :data="requestList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请ID" align="center" prop="id" />
      <el-table-column label="文件名称" align="center" prop="fileName" :show-overflow-tooltip="true" />
      <el-table-column label="申请人" align="center" prop="requesterName" />
      <el-table-column label="申请理由" align="center" prop="requestReason" :show-overflow-tooltip="true" />
      <el-table-column label="申请时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="approverName" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.approverName">{{ scope.row.approverNickName || scope.row.approverName }}</span>
          <span v-else-if="scope.row.status === 'CANCELLED'">{{ scope.row.requesterNickName || scope.row.requesterName }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.statusDesc }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['files:download:view']"
          >查看</el-button>
          <!-- <el-button
            v-if="scope.row.status === 'APPROVED'"
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleDownload(scope.row)"
            v-hasPermi="['files:download']"
          >下载</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listCompletedApprovals } from "@/api/files/approval";
import { downloadFile } from "@/api/files/index";
import { isSuperAdmin } from '@/api/files/user-post'
import { mapGetters } from 'vuex'

export default {
  name: "CompletedApprovals",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 申请表格数据
      requestList: [],
      // 状态数据字典
      statusOptions: [
        { value: "APPROVED", label: "已批准" },
        { value: "PENDING_MANAGER_APPROVAL", label: "主管已审批，待经理审批" },
        { value: "REJECTED_BY_SUPERVISOR", label: "主管已拒绝" },
        { value: "REJECTED_BY_MANAGER", label: "经理已拒绝" },
        { value: "CANCELLED", label: "已撤销" }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileName: undefined,
        requesterName: undefined,
        status: undefined
      }
    };
  },
  computed: {
    // 从Vuex获取用户角色信息
    ...mapGetters(['roles']),

    // 判断是否为超级管理员
    isAdmin() {
      return isSuperAdmin()
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询已完成审批列表 */
    getList() {
      this.loading = true;

      // 使用专门的后端接口获取已完成的审批
      listCompletedApprovals(this.queryParams).then(response => {
        if (response && response.rows) {
          this.requestList = response.rows;
          this.total = response.total;
        } else {
          this.requestList = [];
          this.total = 0;
        }
        this.loading = false;
      }).catch(error => {
        console.error('获取已完成审批列表出错:', error);
        this.requestList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查看按钮操作 */
    handleView(row) {
      try {
        // 使用完整路径，确保路由匹配
        window.location.href = '#/docs/files/approval/detail?id=' + row.id;
      } catch (error) {
        console.log('查看申请详情出错:', error);
      }
    },
    /** 下载按钮操作 */
    handleDownload(row) {
      if (!row.fileId) {
        this.$modal.msgError('文件ID不存在');
        return;
      }

      // 显示全屏加载
      const loading = this.$loading({
        lock: true,
        text: '正在下载文件...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      downloadFile(row.fileId)
        .then((response) => {
          const blob = new Blob([response]);

          // 检查响应类型，确保是二进制数据
          const contentType = response.type || 'application/octet-stream';
          if (contentType.includes('json')) {
            // 如果是JSON响应，可能是错误信息
            return response.text().then(text => {
              try {
                const errorData = JSON.parse(text);
                if (errorData.msg) {
                  this.$modal.msgError(errorData.msg);
                } else {
                  this.$modal.msgError('下载失败：未知错误');
                }
              } catch (e) {
                this.$modal.msgError('下载失败：解析响应失败');
              }
              throw new Error('下载失败');
            });
          }

          // 使用工具函数下载文件
          this.$createDownloadLink(blob, row.fileName);
        })
        .catch((error) => {
          console.error('下载文件出错:', error);
          this.$modal.msgError('下载失败：' + (error.message || '未知错误'));
        })
        .finally(() => {
          // 关闭加载效果
          loading.close();
        });
    },
    // 获取状态标签类型
    getStatusType(status) {
      switch (status) {
        case 'APPROVED':
          return 'success';
        case 'PENDING_MANAGER_APPROVAL':
          return 'warning';
        case 'REJECTED_BY_SUPERVISOR':
        case 'REJECTED_BY_MANAGER':
          return 'danger';
        case 'CANCELLED':
          return 'warning';
        default:
          return 'info';
      }
    }
  }
};
</script>

<style>
.completed-container {
  padding: 0;
  position: relative;
  min-height: 600px;
  padding-bottom: 50px; /* 添加底部间距，确保分页组件有足够空间 */
}

.el-table {
  margin-top: 15px;
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa !important;
}

.el-tag {
  border-radius: 4px;
}

/* 确保分页组件有足够的空间 */
.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px;
  padding-bottom: 10px;
}
</style>
