<template>
  <div class="test-container">
    <h2>权限测试页面</h2>
    
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>用户信息</span>
      </div>
      <div>
        <p><strong>用户ID:</strong> {{ userId }}</p>
        <p><strong>用户角色:</strong> {{ roles.join(', ') }}</p>
        <p><strong>岗位编码:</strong> {{ postCodes.join(', ') }}</p>
        <p><strong>是否超级管理员:</strong> {{ isAdmin ? '是' : '否' }}</p>
        <p><strong>是否主管:</strong> {{ isSupervisor ? '是' : '否' }}</p>
        <p><strong>是否经理:</strong> {{ isManager ? '是' : '否' }}</p>
        <p><strong>是否普通员工:</strong> {{ isEmployee ? '是' : '否' }}</p>
      </div>
    </el-card>

    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>权限检查结果</span>
      </div>
      <div>
        <p><strong>需要提交申请:</strong> {{ needsApplication ? '是' : '否' }}</p>
        <p><strong>有审批权限:</strong> {{ canApprove ? '是' : '否' }}</p>
        <p><strong>有查看所有记录权限:</strong> {{ canViewAll ? '是' : '否' }}</p>
        <p><strong>是普通员工:</strong> {{ isRegularEmp ? '是' : '否' }}</p>
      </div>
    </el-card>

    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>标签页显示逻辑</span>
      </div>
      <div>
        <p><strong>应显示"我的申请"标签页:</strong> {{ shouldShowMyRequests ? '是' : '否' }}</p>
        <p><strong>应显示"待我审批"标签页:</strong> {{ shouldShowPendingApprovals ? '是' : '否' }}</p>
        <p><strong>应显示"已完成审批"标签页:</strong> {{ shouldShowCompletedApprovals ? '是' : '否' }}</p>
        <p><strong>默认标签页:</strong> {{ defaultTab }}</p>
      </div>
    </el-card>

    <el-button @click="refreshData" type="primary">刷新数据</el-button>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { hasApprovalPermission, needsToSubmitApplication, isRegularEmployee, isSuperAdmin, hasViewAllPermission } from '@/api/files/user-post'

export default {
  name: 'TestPermissions',
  data() {
    return {
      needsApplication: false,
      canApprove: false,
      canViewAll: false,
      isRegularEmp: false
    }
  },
  computed: {
    ...mapGetters(['roles', 'postCodes', 'isSupervisor', 'isManager', 'isEmployee']),
    
    userId() {
      return this.$store.getters.name || 'Unknown'
    },
    
    isAdmin() {
      return isSuperAdmin()
    },
    
    shouldShowMyRequests() {
      return this.needsApplication
    },
    
    shouldShowPendingApprovals() {
      return this.canApprove
    },
    
    shouldShowCompletedApprovals() {
      return true // 始终显示
    },
    
    defaultTab() {
      if (this.isAdmin) {
        return 'completedApprovals'
      } else if (this.needsApplication && !this.canApprove) {
        return 'myRequests'
      } else if (!this.needsApplication && this.canApprove) {
        return 'pendingApprovals'
      } else if (this.needsApplication && this.canApprove) {
        return 'myRequests'
      } else {
        return 'completedApprovals'
      }
    }
  },
  created() {
    this.refreshData()
  },
  methods: {
    refreshData() {
      // 检查权限
      needsToSubmitApplication().then(result => {
        this.needsApplication = result
        console.log('需要提交申请:', result)
      })

      hasApprovalPermission().then(result => {
        this.canApprove = result
        console.log('有审批权限:', result)
      })

      this.canViewAll = hasViewAllPermission()
      console.log('有查看所有记录权限:', this.canViewAll)

      isRegularEmployee().then(result => {
        this.isRegularEmp = result
        console.log('是普通员工:', result)
      })

      console.log('用户信息:', {
        roles: this.roles,
        postCodes: this.postCodes,
        isSupervisor: this.isSupervisor,
        isManager: this.isManager,
        isEmployee: this.isEmployee,
        isAdmin: this.isAdmin
      })
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
