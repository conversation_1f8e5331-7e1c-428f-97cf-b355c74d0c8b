<template>
  <div class="approvals-container">
    <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="文件名称" prop="fileName">
          <el-input
            v-model="queryParams.fileName"
            placeholder="请输入文件名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="申请人" prop="requesterName">
          <el-input
            v-model="queryParams.requesterName"
            placeholder="请输入申请人"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 申请列表 -->
      <el-table
        v-loading="loading"
        :data="requestList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="申请ID" align="center" prop="id" />
        <el-table-column label="文件名称" align="center" prop="fileName" :show-overflow-tooltip="true" />
        <el-table-column label="申请人" align="center" prop="requesterName" />
        <el-table-column label="申请理由" align="center" prop="requestReason" :show-overflow-tooltip="true" />
        <el-table-column label="申请时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.statusDesc }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['files:download:view']"
            >查看</el-button>
            <el-button
              v-if="canApproveRequest(scope.row)"
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleApprove(scope.row)"
              v-hasPermi="['files:download:approve']"
            >批准</el-button>
            <el-button
              v-if="canApproveRequest(scope.row)"
              size="mini"
              type="text"
              icon="el-icon-close"
              @click="handleReject(scope.row)"
              v-hasPermi="['files:download:reject']"
            >拒绝</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

    <!-- 审批对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="审批意见" prop="comment">
          <el-input v-model="form.comment" type="textarea" placeholder="请输入审批意见" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPendingApprovals, approveRequest, rejectRequest, getRequestDetail } from "@/api/files/approval";
import { mapGetters } from 'vuex';

export default {
  name: "PendingApprovals",
  computed: {
    // 从Vuex获取用户岗位信息
    ...mapGetters(['isSupervisor', 'isManager', 'isEmployee']),
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 申请表格数据
      requestList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {
        requestId: null,
        comment: "",
        action: null // approve or reject
      },
      // 表单校验
      rules: {
        comment: [
          { required: false, message: "审批意见不能为空", trigger: "blur" }
        ]
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileName: undefined,
        requesterName: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询待审批列表 */
    getList() {
      this.loading = true;
      listPendingApprovals(this.queryParams).then(response => {
        this.requestList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.log('获取待审批列表出错:', error);
        this.requestList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查看按钮操作 */
    handleView(row) {
      try {
        // 使用完整路径，确保路由匹配
        window.location.href = '#/docs/files/approval/detail?id=' + row.id;
      } catch (error) {
        console.log('查看申请详情出错:', error);
      }
    },
    /** 批准按钮操作 */
    handleApprove(row) {
      try {
        this.form = {
          requestId: row.id,
          comment: "",
          action: "approve"
        };
        this.title = "批准申请";
        this.open = true;
      } catch (error) {
        console.log('打开批准对话框出错:', error);
      }
    },
    /** 拒绝按钮操作 */
    handleReject(row) {
      try {
        this.form = {
          requestId: row.id,
          comment: "",
          action: "reject"
        };
        this.title = "拒绝申请";
        this.open = true;
      } catch (error) {
        console.log('打开拒绝对话框出错:', error);
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.action === "approve") {
            approveRequest(this.form.requestId, this.form.comment).then(response => {
              this.$modal.msgSuccess("批准成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.log('批准申请出错:', error);
              this.open = false;
            });
          } else if (this.form.action === "reject") {
            rejectRequest(this.form.requestId, this.form.comment).then(response => {
              this.$modal.msgSuccess("拒绝成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.log('拒绝申请出错:', error);
              this.open = false;
            });
          }
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        requestId: null,
        comment: "",
        action: null
      };
      this.resetForm("form");
    },
    // 获取状态标签类型
    getStatusType(status) {
      switch (status) {
        case 'APPROVED':
          return 'success';
        case 'PENDING_SUPERVISOR_APPROVAL':
        case 'PENDING_MANAGER_APPROVAL':
          return 'warning';
        case 'REJECTED_BY_SUPERVISOR':
        case 'REJECTED_BY_MANAGER':
        case 'CANCELLED':
          return 'danger';
        default:
          return 'info';
      }
    },
    // 判断是否可以审批某个申请
    canApproveRequest(row) {
      if (row.status === 'PENDING_SUPERVISOR_APPROVAL') {
        // 待主管审批状态：只有主管可以审批
        return this.isSupervisor;
      } else if (row.status === 'PENDING_MANAGER_APPROVAL') {
        // 待经理审批状态：只有经理可以审批
        return this.isManager;
      }
      return false;
    }
  }
};
</script>

<style>
.approvals-container {
  padding: 0;
}

.el-table {
  margin-top: 15px;
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #f5f7fa !important;
}

.el-dialog {
  border-radius: 8px;
  overflow: hidden;
}

.el-dialog__header {
  background-color: #f5f7fa;
  padding: 15px 20px;
  margin-right: 0;
}

.el-dialog__body {
  padding: 20px;
}

.el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #f0f0f0;
}

.el-tag {
  border-radius: 4px;
}
</style>
