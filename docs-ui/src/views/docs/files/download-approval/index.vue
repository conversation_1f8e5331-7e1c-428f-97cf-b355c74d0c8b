<template>
  <div class="app-container">
    <div class="approval-container">
      <!-- 当有可用标签页时显示标签页 -->
      <el-tabs v-if="hasAvailableTabs" v-model="activeTab" @tab-click="handleTabClick" class="approval-tabs">
        <el-tab-pane v-if="needsApplication" label="我的申请" name="myRequests">
          <my-requests v-if="activeTab === 'myRequests'"></my-requests>
        </el-tab-pane>
        <el-tab-pane v-if="canApprove" label="待我审批" name="pendingApprovals">
          <pending-approvals v-if="activeTab === 'pendingApprovals'"></pending-approvals>
        </el-tab-pane>
        <el-tab-pane label="已完成审批" name="completedApprovals">
          <completed-approvals v-if="activeTab === 'completedApprovals'"></completed-approvals>
        </el-tab-pane>
      </el-tabs>

      <!-- 当没有可用标签页时显示提示信息 -->
      <el-empty v-if="!hasAvailableTabs" description="您当前没有可用的功能">
        <el-button type="primary" @click="goBack">返回</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script>
import MyRequests from './my-requests.vue'
import PendingApprovals from './pending-approvals.vue'
import CompletedApprovals from './completed-approvals.vue'
import { hasApprovalPermission, needsToSubmitApplication, isSuperAdmin, hasViewAllPermission } from '@/api/files/user-post'
import { mapGetters } from 'vuex'

export default {
  name: 'DownloadApproval',
  components: {
    MyRequests,
    PendingApprovals,
    CompletedApprovals
  },
  data() {
    return {
      activeTab: 'myRequests',
      canApprove: false,
      needsApplication: true,
      isAdmin: false
    }
  },
  computed: {
    // 从Vuex获取用户角色信息
    ...mapGetters(['roles']),

    // 判断是否有可用标签页
    hasAvailableTabs() {
      return true // 始终显示至少一个标签页（已完成审批标签页始终可见）
    }
  },
  created() {
    // 检查用户角色并设置相应的标签页
    this.checkUserRole()
  },
  methods: {
    handleTabClick(tab) {
      // 可以在这里添加额外的处理逻辑
      console.log('切换到标签:', tab.name);
    },
    // 检查用户角色并设置相应的标签页
    checkUserRole() {
      // 检查是否为超级管理员
      this.isAdmin = isSuperAdmin()

      // 检查用户是否需要提交申请（普通员工或主管）
      needsToSubmitApplication().then(needsApplication => {
        this.needsApplication = needsApplication

        // 检查用户是否有审批权限（主管或经理）
        hasApprovalPermission().then(hasPermission => {
          this.canApprove = hasPermission

          // 根据用户角色设置默认标签页
          if (this.isAdmin) {
            // 超级管理员默认显示"已完成审批"（可以查看所有记录）
            this.activeTab = 'completedApprovals'
          } else if (this.needsApplication && !this.canApprove) {
            // 普通员工默认显示"我的申请"
            this.activeTab = 'myRequests'
          } else if (!this.needsApplication && this.canApprove) {
            // 经理默认显示"待我审批"
            this.activeTab = 'pendingApprovals'
          } else if (this.needsApplication && this.canApprove) {
            // 主管（既需要申请又有审批权限），默认显示"我的申请"
            this.activeTab = 'myRequests'
          } else {
            // 其他情况，默认显示"已完成审批"
            this.activeTab = 'completedApprovals'
          }

          console.log('用户角色检查结果:', {
            isAdmin: this.isAdmin,
            needsApplication: this.needsApplication,
            canApprove: this.canApprove,
            activeTab: this.activeTab
          })
        }).catch(error => {
          console.error('获取用户审批权限失败:', error)
          this.canApprove = false
          // 出错时默认显示"已完成审批"标签页
          this.activeTab = 'completedApprovals'
        })
      }).catch(error => {
        console.error('获取用户岗位信息失败:', error)
        this.needsApplication = true
        // 出错时默认显示"已完成审批"标签页
        this.activeTab = 'completedApprovals'
      })
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px); /* 设置最小高度为视口高度减去头部 */
}

.approval-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 24px;
  padding-bottom: 60px; /* 增加底部边距 */
  transition: all 0.3s;
  min-height: calc(100vh - 140px); /* 确保内容区域有足够高度 */
}

.approval-tabs {
  margin-top: 10px;
}

/* 美化标签样式 */
.el-tabs__item {
  height: 50px !important;
  line-height: 50px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #606266 !important;
}

.el-tabs__item.is-active {
  color: #409EFF !important;
  font-weight: 600 !important;
}

.el-tabs__active-bar {
  height: 3px !important;
  border-radius: 3px !important;
}
</style>
