<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>文件下载申请</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="form.fileName" disabled />
        </el-form-item>
        <el-form-item label="文件类型" prop="fileType">
          <el-input v-model="form.fileType" disabled />
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="form.version" disabled />
        </el-form-item>
        <el-form-item label="申请理由" prop="reason">
          <el-input v-model="form.reason" type="textarea" :rows="4" placeholder="请输入申请理由" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交申请</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { getFileDetail } from '@/api/files/index'
import { submitApprovalRequest } from '@/api/files/approval'

export default {
  name: 'ApplyDownload',
  data() {
    return {
      // 表单参数
      form: {
        fileId: null,
        fileName: '',
        fileType: '',
        version: '',
        reason: ''
      },
      // 表单校验
      rules: {
        reason: [
          { required: true, message: '申请理由不能为空', trigger: 'blur' },
          { min: 5, max: 200, message: '申请理由长度在 5 到 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    const fileId = this.$route.query.fileId
    if (fileId) {
      this.form.fileId = fileId
      this.getFileInfo(fileId)
    } else {
      this.$message.error('未指定文件ID')
      this.goBack()
    }
  },
  methods: {
    /** 获取文件信息 */
    getFileInfo(fileId) {
      getFileDetail(fileId).then(response => {
        const file = response.data
        this.form.fileName = file.fileName
        this.form.fileType = file.fileType
        this.form.version = file.version
      }).catch(error => {
        this.$message.error('获取文件信息失败: ' + error)
        this.goBack()
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          submitApprovalRequest(this.form.fileId, this.form.reason).then(response => {
            this.$modal.msgSuccess('申请提交成功')
            this.goBack()
          }).catch(error => {
            this.$modal.msgError('申请提交失败: ' + error)
          })
        }
      })
    },
    /** 取消按钮 */
    cancel() {
      this.goBack()
    },
    /** 返回列表 */
    goBack() {
      // 使用完整路径，确保路由匹配
      window.location.href = '#/docs/files'
    }
  }
}
</script>
