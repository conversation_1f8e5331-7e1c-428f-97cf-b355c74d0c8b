<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="header-title">审批详情</h2>
        <el-tag :type="getStatusType(approvalDetail.status)" class="status-tag" effect="dark" size="medium">{{ approvalDetail.statusDesc }}</el-tag>
      </div>
      <div>
        <el-button @click="goBack" icon="el-icon-back" type="default">返回</el-button>
      </div>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <!-- 1. 基本信息 -->
        <el-card class="info-card" shadow="hover">
          <div slot="header" class="card-header">
            <i class="el-icon-document"></i>
            <span>文件基本信息</span>
          </div>

          <!-- 文件名单独占一行 -->
          <div class="file-name-container">
            <h3 class="file-name-title">文件名称：</h3>
            <el-link type="primary" :underline="false" class="filename-text" @click="handleFileClick">{{ approvalDetail.fileName }}</el-link>
          </div>

          <el-descriptions border :column="2">
            <!-- 申请人、申请理由、申请时间放在同一行 -->
            <el-descriptions-item label="申请人">
              <span>{{ approvalDetail.requesterNickName || approvalDetail.requesterName }} ({{ approvalDetail.requesterDepartment || '未知部门' }})</span>
            </el-descriptions-item>
            <el-descriptions-item label="申请时间">
              <span>{{ parseTime(approvalDetail.createTime) }}</span>
            </el-descriptions-item>

            <!-- 主管和经理放在同一行 -->
            <el-descriptions-item label="主管">
              <span v-if="approvalDetail.supervisorName">{{ approvalDetail.supervisorNickName || approvalDetail.supervisorName }} ({{ approvalDetail.supervisorDepartment || '未知部门' }})</span>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label="经理">
              <span v-if="approvalDetail.managerName">{{ approvalDetail.managerNickName || approvalDetail.managerName }} ({{ approvalDetail.managerDepartment || '未知部门' }})</span>
              <span v-else>-</span>
            </el-descriptions-item>

            <!-- 申请理由 -->
            <el-descriptions-item label="申请理由" :span="2">
              <div>{{ approvalDetail.requestReason }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="24">
        <!-- 2. 审批流程 -->
        <el-card class="process-card" shadow="hover">
          <div slot="header">
            <div class="card-header">
              <i class="el-icon-s-operation"></i>
              <span>审批流程</span>
            </div>
          </div>

          <!-- 审批流程步骤条 -->
          <div class="steps-container relative">
            <!-- 如果已撤销，显示撤销标记 -->
            <div v-if="approvalDetail.status === 'CANCELLED'" class="cancel-indicator">
              <el-tag type="warning" size="mini">申请已撤销</el-tag>
            </div>

            <el-steps :active="getActiveStep() - 1" finish-status="success" align-center>
              <el-step
                title="提交申请"
                icon="el-icon-edit-outline"
                :status="getStepStatus(1)"
              >
                <div slot="description">
                  <div v-if="getSubmitTime()" class="step-time">{{ formatTime(getSubmitTime()) }}</div>
                  <div v-if="approvalDetail.requesterName" class="step-user">{{ approvalDetail.requesterName }}</div>
                </div>
              </el-step>

              <!-- 主管审批步骤 - 条件显示 -->
              <el-step
                v-if="shouldShowSupervisorStep"
                title="主管审批"
                icon="el-icon-user"
                :status="getStepStatus(2)"
              >
                <div slot="description">
                  <div v-if="getSupervisorApproveTime()" class="step-time">{{ formatTime(getSupervisorApproveTime()) }}</div>
                  <div v-if="approvalDetail.supervisorName" class="step-user">{{ approvalDetail.supervisorName }}</div>
                </div>
              </el-step>

              <el-step
                title="经理审批"
                icon="el-icon-user-solid"
                :status="getStepStatus(shouldShowSupervisorStep ? 3 : 2)"
              >
                <div slot="description">
                  <div v-if="getManagerApproveTime()" class="step-time">{{ formatTime(getManagerApproveTime()) }}</div>
                  <div v-if="approvalDetail.managerName" class="step-user">{{ approvalDetail.managerName }}</div>
                </div>
              </el-step>

              <el-step
                title="审批完成"
                icon="el-icon-circle-check"
                :status="getStepStatus(shouldShowSupervisorStep ? 4 : 3)"
              >
                <div slot="description">
                  <div v-if="getCompleteTime()" class="step-time">{{ formatTime(getCompleteTime()) }}</div>
                </div>
              </el-step>
            </el-steps>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <el-col :span="12">
        <!-- 3. 审批历史 -->
        <el-card class="history-card" shadow="hover">
          <div slot="header" class="card-header">
            <i class="el-icon-time"></i>
            <span>审批历史</span>
          </div>

          <div class="history-records">
            <div
              v-for="(activity, index) in approvalDetail.actionRecords"
              :key="index"
              class="history-item"
              :class="'history-item-' + getTimelineTagType(activity.actionType)"
            >
              <div class="history-icon-wrapper">
                <i :class="getActionIcon(activity.actionType)" class="history-icon"></i>
              </div>

              <div class="history-content">
                <div class="history-header">
                  <div class="history-title">
                    {{ getActionTypeDisplay(activity) }}
                    <span class="actor-info">
                      {{ activity.actorNickName || activity.actorName || '未知用户' }}
                      <span class="actor-dept">{{ activity.actorDepartment ? `(${activity.actorDepartment})` : '' }}</span>
                    </span>
                  </div>
                  <div class="history-time">
                    {{ formatTime(activity.actionTime) }}
                  </div>
                </div>

                <div v-if="activity.comment" class="history-comment">
                  <template v-if="activity.actionType === 'SUBMIT'">
                    <span class="comment-label">申请理由: </span>{{ formatSubmitComment(activity.comment) }}
                  </template>
                  <template v-else-if="activity.actionType === 'CANCEL_REQUEST'">
                    <span class="comment-label">撤销原因: </span>{{ activity.comment }}
                  </template>
                  <template v-else>
                    <span class="comment-label">审批意见: </span>{{ activity.comment }}
                  </template>
                </div>
              </div>
            </div>

            <div v-if="approvalDetail.actionRecords && approvalDetail.actionRecords.length === 0" class="no-records">
              暂无历史记录
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <!-- 操作按钮区域 -->
        <el-card v-if="showActionButtons" class="action-card" shadow="hover">
          <div slot="header" class="card-header">
            <i class="el-icon-s-tools"></i>
            <span>审批操作</span>
          </div>

          <div class="action-content">
            <div class="action-status" v-if="approvalDetail.status">
              <div class="status-label">当前状态：</div>
              <el-tag :type="getStatusTagType()" effect="plain" size="medium">{{ getStatusDisplay() }}</el-tag>
            </div>

            <div class="action-btn-group">
              <!-- 批准按钮 -->
              <el-button
                v-if="canApprove"
                type="success"
                icon="el-icon-check"
                @click="handleApprove"
                class="action-button approve-btn"
                size="small"
              >批准</el-button>

              <!-- 驳回按钮 -->
              <el-button
                v-if="canReject"
                type="danger"
                icon="el-icon-close"
                @click="handleReject"
                class="action-button reject-btn"
                size="small"
              >驳回</el-button>

              <!-- 撤销按钮 -->
              <el-button
                v-if="canCancel"
                type="warning"
                icon="el-icon-back"
                @click="handleCancel"
                class="action-button cancel-btn"
                size="small"
              >撤销</el-button>

              <!-- 下载按钮 -->
              <el-button
                v-if="canDownload"
                type="primary"
                icon="el-icon-download"
                @click="handleDownload"
                v-hasPermi="['files:download']"
                class="action-button download-btn"
                size="small"
              >下载</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 审批对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="审批意见" prop="comment">
          <el-input v-model="form.comment" type="textarea" :autosize="{minRows: 4, maxRows: 8}" placeholder="请输入审批意见"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRequestDetail, approveRequest, rejectRequest, cancelRequest } from "@/api/files/approval";
import { downloadFile } from "@/api/files/index";
import { mapGetters } from 'vuex';

export default {
  name: "ApprovalDetail",
  computed: {
    // 从Vuex获取用户岗位信息
    ...mapGetters(['isSupervisor', 'isManager', 'isEmployee']),

    // 当前用户是否为主管（直接从Vuex获取）
    isCurrentUserSupervisor() {
      return this.isSupervisor;
    },

    // 申请人是否为主管
    isRequesterSupervisor() {
      // 确保审批详情数据已加载
      if (!this.approvalDetail || !this.approvalDetail.status) {
        return false;
      }

      // 方法1：检查supervisorId是否为空，如果为空说明跳过了主管审批
      if (this.approvalDetail.supervisorId === null || this.approvalDetail.supervisorId === undefined) {
        return true;
      }

      // 方法2：检查是否有主管审批记录，如果没有且直接到经理审批，说明申请人是主管
      if (this.approvalDetail.actionRecords && this.approvalDetail.actionRecords.length > 0) {
        const hasSubmit = this.approvalDetail.actionRecords.some(record => record.actionType === 'SUBMIT');
        const hasSupervisorAction = this.approvalDetail.actionRecords.some(record =>
          record.actionType === 'SUPERVISOR_APPROVE' || record.actionType === 'SUPERVISOR_REJECT'
        );

        // 如果有提交记录但没有主管审批记录，且当前状态是经理审批相关，说明申请人是主管
        if (hasSubmit && !hasSupervisorAction) {
          const managerRelatedStatuses = ['PENDING_MANAGER_APPROVAL', 'APPROVED', 'REJECTED_BY_MANAGER'];
          if (managerRelatedStatuses.includes(this.approvalDetail.status)) {
            return true;
          }
        }
      }

      return false;
    },

    // 是否需要显示主管审批步骤（大大简化的逻辑）
    shouldShowSupervisorStep() {
      // 如果当前用户是主管且申请人也是主管，则不显示主管审批步骤
      return !(this.isCurrentUserSupervisor && this.isRequesterSupervisor);
    },

    // 是否显示操作按钮
    showActionButtons() {
      return this.canApprove || this.canReject || this.canCancel || this.canDownload;
    },
    // 是否可以批准
    canApprove() {
      // 根据用户角色和申请状态判断审批权限
      if (this.approvalDetail.status === "PENDING_SUPERVISOR_APPROVAL") {
        // 待主管审批状态：只有主管可以审批
        return this.isSupervisor;
      } else if (this.approvalDetail.status === "PENDING_MANAGER_APPROVAL") {
        // 待经理审批状态：只有经理可以审批
        return this.isManager;
      }
      
      return false;
    },
    // 是否可以拒绝
    canReject() {
      return this.canApprove;
    },
    // 是否可以撤销
    canCancel() {
      // 只有处于待审批状态且是申请人本人才能撤销
      return (
        (this.approvalDetail.status === "PENDING_SUPERVISOR_APPROVAL" ||
         this.approvalDetail.status === "PENDING_MANAGER_APPROVAL") &&
        this.approvalDetail.requesterId === this.currentUser.userId
      );
    },
    // 是否可以下载
    canDownload() {
      return (
        this.approvalDetail.status === "APPROVED" &&
        (this.approvalDetail.requesterId === this.currentUser.userId ||
         this.currentUser.roles.includes("admin"))
      );
    },

    // 是否显示主管状态
    showSupervisorStatus() {
      return this.approvalDetail.status !== "PENDING_SUPERVISOR_APPROVAL";
    },

    // 是否显示经理状态
    showManagerStatus() {
      return ["APPROVED", "REJECTED_BY_MANAGER", "PENDING_MANAGER_APPROVAL"].includes(this.approvalDetail.status);
    },
  },
  data() {
    return {
      // 申请详情
      approvalDetail: {
        id: null,
        fileId: null,
        fileName: "",
        requesterId: null,
        requesterName: "",
        requesterNickName: "",
        requesterDepartment: "",
        requestReason: "",
        status: "",
        statusDesc: "",
        supervisorId: null,
        supervisorName: "",
        supervisorNickName: "",
        supervisorDepartment: "",
        managerId: null,
        managerName: "",
        managerNickName: "",
        managerDepartment: "",
        createTime: null,
        updateTime: null,
        actionRecords: []
      },
      // 当前用户信息（简化，主要从Vuex获取）
      currentUser: {
        userId: null,
        roles: []
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {
        requestId: null,
        comment: "",
        action: null // approve or reject
      },
      // 表单校验
      rules: {
        comment: [
          { required: false, message: "审批意见不能为空", trigger: "blur" }
        ]
      }
    };
  },

  created() {
    const id = this.$route.query.id;
    if (id) {
      this.getDetail(id);
      this.getCurrentUser();
    } else {
      this.$message.error("未指定申请ID");
      this.goBack();
    }
  },
  methods: {
    // 处理文件点击事件，查看文件详情
    handleFileClick() {
      if (!this.approvalDetail.fileId) {
        this.$message.warning('无效的文件ID');
        return;
      }

      // 跳转到文件列表页并传递文件ID和高亮标记
      this.$router.push({
        path: '/docs/files',
        query: {
          fileId: this.approvalDetail.fileId,
          highlight: 'true'
        }
      });
    },

    // 获取步骤状态（success、process、error或wait）
    getStepStatus(step) {
      const status = this.approvalDetail.status;

      // 已完成的步骤检查
      if (step === 1) { // 提交申请步骤
        return 'success'; // 提交步骤始终视为完成
      }

      if (step === 2) { // 主管审批步骤
        if (status === 'PENDING_SUPERVISOR_APPROVAL') {
          return 'process'; // 正在待主管审批
        }
        if (status === 'REJECTED_BY_SUPERVISOR') {
          return 'error'; // 主管驳回
        }
        if (['APPROVED_BY_SUPERVISOR', 'PENDING_MANAGER_APPROVAL', 'APPROVED_BY_MANAGER', 'APPROVED'].includes(status)) {
          return 'success'; // 主管已审批
        }
        return 'wait'; // 其他状态下等待
      }

      if (step === 3) { // 经理审批步骤
        if (status === 'PENDING_MANAGER_APPROVAL') {
          return 'process'; // 正在待经理审批
        }
        if (status === 'REJECTED_BY_MANAGER') {
          return 'error'; // 经理驳回
        }
        if (['APPROVED_BY_MANAGER', 'APPROVED'].includes(status)) {
          return 'success'; // 经理已审批
        }
        return 'wait'; // 其他状态下等待
      }

      if (step === 4) { // 完成步骤
        if (status === 'APPROVED') {
          return 'success'; // 已完成
        }
        return 'wait'; // 其他状态下等待
      }

      return 'wait'; // 默认待机
    },
    /** 获取申请详情 */
    getDetail(id) {
      getRequestDetail(id).then(response => {
        this.approvalDetail = response.data;
      }).catch(error => {
        this.$message.error("获取申请详情失败: " + error);
        this.goBack();
      });
    },
    /** 获取当前用户信息 */
    getCurrentUser() {
      // 简化：只获取基本用户信息，岗位信息从Vuex获取
      this.currentUser.userId = this.$store.getters.id;
      this.currentUser.roles = this.$store.getters.roles;
    },
    /** 批准按钮操作 */
    handleApprove() {
      this.reset();
      this.form.requestId = this.approvalDetail.id;
      this.form.action = "approve";
      this.title = "批准申请";
      this.open = true;
    },
    /** 拒绝按钮操作 */
    handleReject() {
      this.reset();
      this.form.requestId = this.approvalDetail.id;
      this.form.action = "reject";
      this.title = "拒绝申请";
      this.open = true;
    },
    /** 撤销按钮操作 */
    handleCancel() {
      const requestId = this.approvalDetail.id;
      this.$modal.confirm('是否确认撤销该申请?').then(() => {
        return cancelRequest(requestId);
      }).then(() => {
        this.getDetail(requestId);
        this.$modal.msgSuccess("撤销成功");
      }).catch(() => {});
    },
    /** 下载按钮操作 */
    handleDownload() {
      if (!this.approvalDetail.fileId) {
        this.$modal.msgError('文件ID不存在');
        return;
      }

      // 显示全屏加载
      const loading = this.$loading({
        lock: true,
        text: '正在下载文件...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      downloadFile(this.approvalDetail.fileId)
        .then((response) => {
          const blob = new Blob([response]);

          // 检查响应类型，确保是二进制数据
          const contentType = response.type || 'application/octet-stream';
          if (contentType.includes('json')) {
            // 如果是JSON响应，可能是错误信息
            return response.text().then(text => {
              try {
                const errorData = JSON.parse(text);
                if (errorData.msg) {
                  this.$modal.msgError(errorData.msg);
                } else {
                  this.$modal.msgError('下载失败：未知错误');
                }
              } catch (e) {
                this.$modal.msgError('下载失败：解析响应失败');
              }
              throw new Error('下载失败');
            });
          }

          // 使用工具函数下载文件
          this.$createDownloadLink(blob, this.approvalDetail.fileName);
        })
        .catch((error) => {
          console.error('下载文件出错:', error);
          this.$modal.msgError('下载失败：' + (error.message || '未知错误'));
        })
        .finally(() => {
          // 关闭加载效果
          loading.close();
        });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.action === "approve") {
            approveRequest(this.form.requestId, this.form.comment).then(() => {
              this.$modal.msgSuccess("批准成功");
              this.open = false;
              this.getDetail(this.form.requestId);
            });
          } else if (this.form.action === "reject") {
            rejectRequest(this.form.requestId, this.form.comment).then(() => {
              this.$modal.msgSuccess("拒绝成功");
              this.open = false;
              this.getDetail(this.form.requestId);
            });
          }
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        requestId: null,
        comment: "",
        action: null
      };
      this.resetForm("form");
    },
    /** 返回按钮操作 */
    goBack() {
      this.$router.go(-1);
    },
    // 获取状态标签类型
    getStatusType(status) {
      switch (status) {
        case 'APPROVED':
          return 'success';
        case 'PENDING_SUPERVISOR_APPROVAL':
        case 'PENDING_MANAGER_APPROVAL':
          return 'warning';
        case 'REJECTED_BY_SUPERVISOR':
        case 'REJECTED_BY_MANAGER':
        case 'CANCELLED':
          return 'danger';
        default:
          return 'info';
      }
    },
    // 获取操作图标类型
    getActionIconType(actionType) {
      if (!actionType) return '';

      switch (actionType) {
        case 'SUBMIT':
          return 'primary';
        case 'SUPERVISOR_APPROVE':
        case 'MANAGER_APPROVE':
          return 'success';
        case 'SUPERVISOR_REJECT':
        case 'MANAGER_REJECT':
          return 'danger';
        case 'CANCEL_REQUEST':
          return 'warning';
        default:
          return 'info';
      }
    },

    // 获取操作图标
    getActionIcon(actionType) {
      switch (actionType) {
        case 'APPROVE_BY_SUPERVISOR':
        case 'APPROVE_BY_MANAGER':
          return 'el-icon-check';
        case 'REJECT_BY_SUPERVISOR':
        case 'REJECT_BY_MANAGER':
          return 'el-icon-close';
        case 'CANCEL_REQUEST':
          return 'el-icon-back';
        case 'SUBMIT':
          return 'el-icon-edit-outline';
        case 'COMPLETE':
          return 'el-icon-circle-check';
        default:
          return 'el-icon-info';
      }
    },

    // 获取时间线标签类型
    getTimelineTagType(actionType) {
      switch (actionType) {
        case 'APPROVE_BY_SUPERVISOR':
        case 'APPROVE_BY_MANAGER':
        case 'COMPLETE':
          return 'success';
        case 'REJECT_BY_SUPERVISOR':
        case 'REJECT_BY_MANAGER':
          return 'danger';
        case 'CANCEL_REQUEST':
          return 'warning';
        default:
          return 'info';
      }
    },
    // 获取时间线卡片的样式类名
    getTimelineCardClass(actionType) {
      switch (actionType) {
        case 'APPROVE_BY_SUPERVISOR':
        case 'APPROVE_BY_MANAGER':
        case 'COMPLETE':
          return 'timeline-card-approve';
        case 'REJECT_BY_SUPERVISOR':
        case 'REJECT_BY_MANAGER':
          return 'timeline-card-reject';
        case 'CANCEL_REQUEST':
          return 'timeline-card-cancel';
        default:
          return 'timeline-card-default';
      }
    },
    // 获取操作类型
    getActionType(activity) {
      if (!activity || !activity.actionType) return '';
      return activity.actionType;
    },

    // 获取当前活动的步骤
    getActiveStep() {
      if (!this.approvalDetail || !this.approvalDetail.status) return 1;

      const status = this.approvalDetail.status;

      // 如果不显示主管步骤（主管申请），调整步骤编号
      if (!this.shouldShowSupervisorStep) {
        if (['SUBMITTED', 'PENDING_MANAGER_APPROVAL'].includes(status)) {
          return 2; // 经理审批步骤
        } else if (['APPROVED_BY_MANAGER', 'APPROVED', 'COMPLETED'].includes(status)) {
          return 3; // 完成步骤
        } else if (['REJECTED_BY_MANAGER', 'CANCELLED'].includes(status)) {
          return 1; // 已驳回或取消，回到提交步骤
        }
        return 1; // 默认为提交步骤
      }

      // 显示主管步骤的情况（普通员工申请）
      if (['SUBMITTED', 'PENDING_SUPERVISOR_APPROVAL'].includes(status)) {
        return 2; // 主管审批步骤
      } else if (['APPROVED_BY_SUPERVISOR', 'PENDING_MANAGER_APPROVAL'].includes(status)) {
        return 3; // 经理审批步骤
      } else if (['APPROVED_BY_MANAGER', 'APPROVED', 'COMPLETED'].includes(status)) {
        return 4; // 完成步骤
      } else if (['REJECTED_BY_SUPERVISOR', 'REJECTED_BY_MANAGER', 'CANCELLED'].includes(status)) {
        return 1; // 已驳回或取消，回到提交步骤
      }

      return 1; // 默认为提交步骤
    },

    // 获取提交时间
    getSubmitTime() {
      if (!this.approvalDetail || !this.approvalDetail.actionRecords) {
        return null;
      }

      const submitAction = this.approvalDetail.actionRecords.find(record =>
        record.actionType === 'SUBMIT'
      );

      return submitAction ? submitAction.actionTime : null;
    },

    // 获取主管审批时间
    getSupervisorApproveTime() {
      if (!this.approvalDetail || !this.approvalDetail.actionRecords) {
        return null;
      }

      const supervisorAction = this.approvalDetail.actionRecords.find(record =>
        ['APPROVE_BY_SUPERVISOR', 'REJECT_BY_SUPERVISOR'].includes(record.actionType)
      );

      return supervisorAction ? supervisorAction.actionTime : null;
    },

    // 获取经理审批时间
    getManagerApproveTime() {
      if (!this.approvalDetail || !this.approvalDetail.actionRecords) {
        return null;
      }

      const managerAction = this.approvalDetail.actionRecords.find(record =>
        ['APPROVE_BY_MANAGER', 'REJECT_BY_MANAGER'].includes(record.actionType)
      );

      return managerAction ? managerAction.actionTime : null;
    },

    // 获取完成时间
    getCompleteTime() {
      if (!this.approvalDetail || !this.approvalDetail.actionRecords) {
        return null;
      }

      const completeAction = this.approvalDetail.actionRecords.find(record =>
        record.actionType === 'COMPLETE'
      );

      return completeAction ? completeAction.actionTime : null;
    },

    // 获取时间线图标
    getTimelineIcon(activity) {
      if (!activity || !activity.actionType) return '';
      switch (activity.actionType) {
        case 'SUBMIT':
          return 'el-icon-edit-outline';
        case 'SUPERVISOR_APPROVE':
        case 'MANAGER_APPROVE':
          return 'el-icon-check';
        case 'SUPERVISOR_REJECT':
        case 'MANAGER_REJECT':
          return 'el-icon-close';
        case 'CANCEL_REQUEST':
          return 'el-icon-back';
        default:
          return '';
      }
    },

    // 获取操作标签类型
    getActionTagType(activity) {
      if (!activity || !activity.actionType) return '';

      // 根据操作类型返回对应的标签类型
      switch (activity.actionType) {
        case 'SUBMIT':
          return 'info';
        case 'SUPERVISOR_APPROVE':
        case 'MANAGER_APPROVE':
          return 'success';
        case 'SUPERVISOR_REJECT':
        case 'MANAGER_REJECT':
          return 'danger';
        case 'CANCEL_REQUEST':
          return 'warning';
        default:
          return 'info';
      }
    },

    // 获取主管状态文本
    getSupervisorStatusText() {
      if (!this.approvalDetail.status) return '';

      switch (this.approvalDetail.status) {
        case 'PENDING_SUPERVISOR_APPROVAL':
          return '待审批';
        case 'PENDING_MANAGER_APPROVAL':
        case 'APPROVED':
          return '已批准';
        case 'REJECTED_BY_SUPERVISOR':
          return '已拒绝';
        case 'CANCELLED':
          return '已撤销';
        default:
          return '';
      }
    },

    // 获取主管状态类型
    getSupervisorStatusType() {
      if (!this.approvalDetail.status) return '';

      switch (this.approvalDetail.status) {
        case 'PENDING_SUPERVISOR_APPROVAL':
          return 'warning';
        case 'PENDING_MANAGER_APPROVAL':
        case 'APPROVED':
          return 'success';
        case 'REJECTED_BY_SUPERVISOR':
          return 'danger';
        case 'CANCELLED':
          return 'info';
        default:
          return 'info';
      }
    },

    // 获取主管状态标签类型
    getSupervisorStatusTagType() {
      if (!this.approvalDetail.status) return '';

      switch (this.approvalDetail.status) {
        case 'PENDING_SUPERVISOR_APPROVAL':
          return 'primary'; // 蓝色
        case 'PENDING_MANAGER_APPROVAL':
        case 'APPROVED':
          return 'success'; // 绿色
        case 'REJECTED_BY_SUPERVISOR':
          return 'danger'; // 红色
        case 'CANCELLED':
          return 'warning'; // 黄色
        default:
          return 'info';
      }
    },

    // 获取经理状态文本
    getManagerStatusText() {
      if (!this.approvalDetail.status) return '';

      switch (this.approvalDetail.status) {
        case 'PENDING_MANAGER_APPROVAL':
          return '待审批';
        case 'APPROVED':
          return '已批准';
        case 'REJECTED_BY_MANAGER':
          return '已拒绝';
        default:
          return '';
      }
    },

    // 获取经理状态类型
    getManagerStatusType() {
      if (!this.approvalDetail.status) return '';

      switch (this.approvalDetail.status) {
        case 'PENDING_MANAGER_APPROVAL':
          return 'warning';
        case 'APPROVED':
          return 'success';
        case 'REJECTED_BY_MANAGER':
          return 'danger';
        default:
          return 'info';
      }
    },

    // 获取经理状态标签类型
    getManagerStatusTagType() {
      if (!this.approvalDetail.status) return '';

      switch (this.approvalDetail.status) {
        case 'PENDING_MANAGER_APPROVAL':
          return 'primary'; // 蓝色
        case 'APPROVED':
          return 'success'; // 绿色
        case 'REJECTED_BY_MANAGER':
          return 'danger'; // 红色
        default:
          return 'info';
      }
    },

    // 判断是否被拒绝或取消
    isRejectedOrCancelled() {
      return ['REJECTED_BY_SUPERVISOR', 'REJECTED_BY_MANAGER', 'CANCELLED'].includes(this.approvalDetail.status);
    },

    // 获取提交时间
    getSubmitTime() {
      if (!this.approvalDetail.actionRecords || this.approvalDetail.actionRecords.length === 0) return null;

      const submitRecord = this.approvalDetail.actionRecords.find(
        record => record.actionType && record.actionType === 'SUBMIT'
      );

      return submitRecord ? submitRecord.actionTime : null;
    },

    // 获取主管审批时间
    getSupervisorApproveTime() {
      if (!this.approvalDetail.actionRecords || this.approvalDetail.actionRecords.length === 0) return null;

      const approveRecord = this.approvalDetail.actionRecords.find(
        record => record.actionType &&
        (record.actionType === 'SUPERVISOR_APPROVE' || record.actionType === 'SUPERVISOR_REJECT')
      );

      return approveRecord ? approveRecord.actionTime : null;
    },

    // 获取经理审批时间
    getManagerApproveTime() {
      if (!this.approvalDetail.actionRecords || this.approvalDetail.actionRecords.length === 0) return null;

      const approveRecord = this.approvalDetail.actionRecords.find(
        record => record.actionType &&
        (record.actionType === 'MANAGER_APPROVE' || record.actionType === 'MANAGER_REJECT')
      );

      return approveRecord ? approveRecord.actionTime : null;
    },

    // 获取完成时间
    getCompleteTime() {
      if (!this.approvalDetail.actionRecords || this.approvalDetail.actionRecords.length === 0) return null;

      // 如果状态是已批准，返回经理批准的时间
      if (this.approvalDetail.status === 'APPROVED') {
        return this.getManagerApproveTime();
      }

      // 如果状态是经理拒绝，返回经理拒绝的时间
      if (this.approvalDetail.status === 'REJECTED_BY_MANAGER') {
        const rejectRecord = this.approvalDetail.actionRecords.find(
          record => record.actionType && record.actionType === 'MANAGER_REJECT'
        );
        return rejectRecord ? rejectRecord.actionTime : null;
      }

      // 如果状态是主管拒绝，返回主管拒绝的时间
      if (this.approvalDetail.status === 'REJECTED_BY_SUPERVISOR') {
        const rejectRecord = this.approvalDetail.actionRecords.find(
          record => record.actionType && record.actionType === 'SUPERVISOR_REJECT'
        );
        return rejectRecord ? rejectRecord.actionTime : null;
      }

      // 如果状态是已撤销，返回撤销的时间
      if (this.approvalDetail.status === 'CANCELLED') {
        const cancelRecord = this.approvalDetail.actionRecords.find(
          record => record.actionType && record.actionType === 'CANCEL_REQUEST'
        );
        return cancelRecord ? cancelRecord.actionTime : null;
      }

      return null;
    },

    // 格式化提交评论，去除前缀
    formatSubmitComment(comment) {
      if (!comment) return '无申请理由';

      // 去除"提交文件下载申请: "前缀
      if (comment.includes('提交文件下载申请:')) {
        return comment.replace('提交文件下载申请: ', '');
      }

      return comment;
    },





    // 获取步骤描述
    formatStepTime(timestamp) {
      if (!timestamp) return '';
      return this.parseTime(timestamp, '{m}-{d} {h}:{i}');
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return '无时间记录';
      return this.parseTime(time, '{y}-{m}-{d} {h}:{i}');
    },

    // 格式化时间为相对时间（多久以前）
    formatTimeAgo(time) {
      if (!time) return '';
      const now = new Date().getTime();
      const timestamp = new Date(time).getTime();
      const diff = (now - timestamp) / 1000; // 差值（秒）

      if (diff < 60) {
        return '刚刚';
      } else if (diff < 3600) {
        return Math.floor(diff / 60) + ' 分钟前';
      } else if (diff < 86400) {
        return Math.floor(diff / 3600) + ' 小时前';
      } else if (diff < 2592000) {
        return Math.floor(diff / 86400) + ' 天前';
      } else if (diff < 31536000) {
        return Math.floor(diff / 2592000) + ' 个月前';
      } else {
        return Math.floor(diff / 31536000) + ' 年前';
      }
    },

    // 获取主管状态
    getSupervisorStatus() {
      if (!this.approvalDetail.status) return '';
      return this.getSupervisorStatusText();
    },

    // 获取经理状态
    getManagerStatus() {
      if (!this.approvalDetail.status) return '';
      return this.getManagerStatusText();
    },

    // 判断主管步骤是否完成
    isSupervisorStepDone() {
      if (!this.approvalDetail.status) return false;

      return this.approvalDetail.status !== 'PENDING_SUPERVISOR_APPROVAL';
    },

    // 判断经理步骤是否完成
    isManagerStepDone() {
      if (!this.approvalDetail.status) return false;

      return ['APPROVED', 'REJECTED_BY_MANAGER'].includes(this.approvalDetail.status);
    },

    // 获取操作类型显示
    getActionTypeDisplay(activity) {
      if (!activity || !activity.actionType) return '未知操作';
      console.log('activity.actionType:', activity.actionType)
      switch (activity.actionType) {
        case 'SUBMIT':
          return '提交申请';
        case 'SUPERVISOR_APPROVE':
          return '主管批准';
        case 'SUPERVISOR_REJECT':
          return '主管拒绝';
        case 'MANAGER_APPROVE':
          return '经理批准';
        case 'MANAGER_REJECT':
          return '经理拒绝';
        case 'CANCEL_REQUEST':
          return '申请人撤销';
        default:
          return activity.actionType;
      }
    },

    // 获取操作颜色
    getActionColor(actionType) {
      if (!actionType) return '#909399';

      switch (actionType) {
        case 'SUBMIT':
          return '#409EFF';
        case 'SUPERVISOR_APPROVE':
        case 'MANAGER_APPROVE':
          return '#67C23A';
        case 'SUPERVISOR_REJECT':
        case 'MANAGER_REJECT':
          return '#F56C6C';
        case 'CANCEL_REQUEST':
          return '#E6A23C';
        default:
          return '#909399';
      }
    },

    // 获取时间线项目颜色（按照要求的颜色方案）
    getTimelineItemColor(actionType) {
      if (!actionType) return '#909399';

      switch (actionType) {
        case 'SUBMIT':
        case 'PENDING_SUPERVISOR_APPROVAL':
        case 'PENDING_MANAGER_APPROVAL':
          return '#909399'; // 提交和审批中使用灰色，只高亮不用特定颜色
        case 'SUPERVISOR_APPROVE':
        case 'MANAGER_APPROVE':
        case 'APPROVED':
          return '#67C23A'; // 完成/批准-绿色
        case 'SUPERVISOR_REJECT':
        case 'MANAGER_REJECT':
        case 'REJECTED_BY_SUPERVISOR':
        case 'REJECTED_BY_MANAGER':
          return '#F56C6C'; // 驳回-红色
        case 'CANCEL_REQUEST':
        case 'CANCELLED':
          return '#E6A23C'; // 撤销-黄色
        default:
          return '#909399';
      }
    },

    // 获取时间线卡片类名
    getTimelineCardClass(actionType) {
      if (!actionType) return '';

      switch (actionType) {
        case 'SUBMIT':
        case 'PENDING_SUPERVISOR_APPROVAL':
        case 'PENDING_MANAGER_APPROVAL':
          return 'timeline-card-default'; // 提交和审批中使用默认样式卡片
        case 'SUPERVISOR_APPROVE':
        case 'MANAGER_APPROVE':
        case 'APPROVED':
          return 'timeline-card-approve'; // 完成/批准-绿色卡片
        case 'SUPERVISOR_REJECT':
        case 'MANAGER_REJECT':
        case 'REJECTED_BY_SUPERVISOR':
        case 'REJECTED_BY_MANAGER':
          return 'timeline-card-reject'; // 驳回-红色卡片
        case 'CANCEL_REQUEST':
        case 'CANCELLED':
          return 'timeline-card-cancel'; // 撤销-黄色卡片
        default:
          return 'timeline-card-default';
      }
    },

    // 获取时间线标签类型
    getTimelineTagType(actionType) {
      if (!actionType) return 'info';

      switch (actionType) {
        case 'SUBMIT':
        case 'PENDING_SUPERVISOR_APPROVAL':
        case 'PENDING_MANAGER_APPROVAL':
          return 'info'; // 提交和审批中使用默认样式，不用特殊颜色
        case 'SUPERVISOR_APPROVE':
        case 'MANAGER_APPROVE':
        case 'APPROVED':
          return 'success'; // 批准/完成-绿色
        case 'SUPERVISOR_REJECT':
        case 'MANAGER_REJECT':
        case 'REJECTED_BY_SUPERVISOR':
        case 'REJECTED_BY_MANAGER':
          return 'danger'; // 驳回-红色
        case 'CANCEL_REQUEST':
        case 'CANCELLED':
          return 'warning'; // 撤销-黄色
        default:
          return 'info';
      }
    },

    // 获取状态标签类型
    getStatusType(status) {
      if (!status) return '';

      switch (status) {
        // 完成状态使用绿色
        case 'APPROVED':
          return 'success';
        // 提交和审批中状态只高亮，使用默认灰色
        case 'PENDING_SUPERVISOR_APPROVAL':
        case 'PENDING_MANAGER_APPROVAL':
          return 'info';
        // 驳回状态使用红色
        case 'REJECTED_BY_SUPERVISOR':
        case 'REJECTED_BY_MANAGER':
          return 'danger';
        // 撤销状态使用黄色
        case 'CANCELLED':
          return 'warning';
        default:
          return 'info';
      }
    },

    // 获取当前状态标签类型
    getStatusTagType() {
      if (!this.approvalDetail || !this.approvalDetail.status) return 'info';
      return this.getStatusType(this.approvalDetail.status);
    },

    // 获取状态显示文本
    getStatusDisplay() {
      if (!this.approvalDetail || !this.approvalDetail.status) return '未知状态';

      // 如果服务器返回了状态描述，直接使用
      if (this.approvalDetail.statusDesc) {
        return this.approvalDetail.statusDesc;
      }

      // 用于翻译状态码为中文描述
      const statusMap = {
        'PENDING_SUPERVISOR_APPROVAL': '待主管审批',
        'APPROVED_BY_SUPERVISOR': '主管已审批',
        'REJECTED_BY_SUPERVISOR': '主管已驳回',
        'PENDING_MANAGER_APPROVAL': '待经理审批',
        'APPROVED_BY_MANAGER': '经理已审批',
        'REJECTED_BY_MANAGER': '经理已驳回',
        'APPROVED': '已批准',
        'CANCELLED': '已撤销',
        'SUBMITTED': '已提交'
      };

      return statusMap[this.approvalDetail.status] || '未知状态';
    },

    // 获取时间格式化
    formatTimeAgo(timestamp) {
      if (!timestamp) return '';
      const now = new Date();
      const date = new Date(timestamp);
      const diff = Math.floor((now - date) / 1000);

      if (diff < 60) return '刚刚';
      if (diff < 3600) return Math.floor(diff / 60) + '分钟前';
      if (diff < 86400) return Math.floor(diff / 3600) + '小时前';
      if (diff < 2592000) return Math.floor(diff / 86400) + '天前';
      if (diff < 31536000) return Math.floor(diff / 2592000) + '个月前';
      return Math.floor(diff / 31536000) + '年前';
    },

    // 检查主管步骤是否完成
    isSupervisorStepDone() {
      if (!this.approvalDetail.status) return false;

      return this.approvalDetail.status !== 'PENDING_SUPERVISOR_APPROVAL';
    },

    // 检查经理步骤是否完成
    isManagerStepDone() {
      if (!this.approvalDetail.status) return false;

      return ['APPROVED', 'REJECTED_BY_MANAGER'].includes(this.approvalDetail.status);
    },

    // 检查主管步骤是否当前活动
    isSupervisorActive() {
      return this.approvalDetail.status === 'PENDING_SUPERVISOR_APPROVAL';
    },

    // 检查经理步骤是否当前活动
    isManagerActive() {
      return this.approvalDetail.status === 'PENDING_MANAGER_APPROVAL';
    },

    // 获取主管头像样式
    getSupervisorAvatarStyle() {
      if (!this.approvalDetail.supervisorId) {
        return { backgroundColor: '#f0f0f0', color: '#c0c4cc' };
      }

      if (this.isSupervisorActive()) {
        return { backgroundColor: '#e6a23c1a', color: '#e6a23c' };
      }

      const status = this.getSupervisorStatusType();
      switch (status) {
        case 'success':
          return { backgroundColor: '#67c23a1a', color: '#67C23A' };
        case 'danger':
          return { backgroundColor: '#f56c6c1a', color: '#F56C6C' };
        default:
          return { backgroundColor: '#e6f1fc', color: '#409EFF' };
      }
    },

    // 获取经理头像样式
    getManagerAvatarStyle() {
      if (!this.approvalDetail.managerId) {
        return { backgroundColor: '#f0f0f0', color: '#c0c4cc' };
      }

      if (this.isManagerActive()) {
        return { backgroundColor: '#e6a23c1a', color: '#e6a23c' };
      }

      const status = this.getManagerStatusType();
      switch (status) {
        case 'success':
          return { backgroundColor: '#67c23a1a', color: '#67C23A' };
        case 'danger':
          return { backgroundColor: '#f56c6c1a', color: '#F56C6C' };
        default:
          return { backgroundColor: '#e6f1fc', color: '#409EFF' };
      }
    },
  }
};
</script>

<style scoped>
/* 全局样式 */
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.mt-20 {
  margin-top: 20px;
}

/* 步骤容器样式 */
.steps-container {
  position: relative;
  padding: 20px 0;
}

/* 撤销标记样式 */
.cancel-indicator {
  position: absolute;
  top: 50%;
  left: 23%;
  transform: translateY(-50%);
  z-index: 10;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-title {
  margin: 0;
  margin-right: 15px;
  font-size: 18px;
  color: #303133;
  font-weight: 600;
}

.status-tag {
  font-weight: 500;
}

/* 卡片通用样式 */
.info-card,
.process-card,
.history-card,
.action-card {
  margin-bottom: 0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  border-radius: 6px;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header i {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

/* 文件信息样式 */
.id-text {
  color: #606266;
  font-weight: 500;
}

.file-name-container {
  padding: 15px 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #EBEEF5;
  background-color: #fafafa;
}

.file-name-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin: 0;
  margin-right: 10px;
}

.filename-text {
  font-size: 15px;
  font-weight: 500;
  word-break: break-all;
  max-width: 100%;
  display: inline-block;
}

.reason-box {
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #e6e8eb;
  min-height: 40px;
  white-space: pre-wrap;
  line-height: 1.6;
  color: #606266;
}

/* 步骤条样式 */
.el-steps {
  padding: 20px 10px;
}

.step-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.step-user {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

/* 审批历史样式 */
.history-records {
  padding: 10px 0;
}

.history-item {
  display: flex;
  position: relative;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
}

.history-item:last-child {
  border-bottom: none;
}

.history-icon-wrapper {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f2f6fc;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.history-icon {
  font-size: 20px;
  color: #909399;
}

.history-item-success .history-icon-wrapper {
  background-color: #f0f9eb;
}

.history-item-success .history-icon {
  color: #67c23a;
}

.history-item-danger .history-icon-wrapper {
  background-color: #fef0f0;
}

.history-item-danger .history-icon {
  color: #f56c6c;
}

.history-item-warning .history-icon-wrapper {
  background-color: #fdf6ec;
}

.history-item-warning .history-icon {
  color: #e6a23c;
}

.history-content {
  flex: 1;
}

.history-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.history-title {
  font-size: 15px;
  font-weight: 500;
  color: #303133;
}

.actor-info {
  display: inline-block;
  margin-left: 8px;
  font-size: 13px;
  font-weight: normal;
  color: #606266;
}

.actor-dept {
  color: #909399;
}

.history-time {
  font-size: 13px;
  color: #909399;
}

.history-comment {
  margin-top: 8px;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.5;
  color: #606266;
  border-left: 3px solid #dcdfe6;
}

.comment-label {
  font-weight: 500;
  color: #303133;
}

.no-records {
  padding: 20px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

/* 审批状态颜色 */
/* 提交和审批中 - 默认/高亮 */
.timeline-card-default {
  border-left-color: #909399;
}

/* 完成/批准 - 绿色 */
.timeline-card-approve {
  border-left-color: #67C23A;
}

.timeline-card-approve .comment-text {
  border-left-color: #67C23A;
}

/* 驳回 - 红色 */
.timeline-card-reject {
  border-left-color: #F56C6C;
}

.timeline-card-reject .comment-text {
  border-left-color: #F56C6C;
}

/* 撤销 - 黄色 */
.timeline-card-cancel {
  border-left-color: #E6A23C;
}

.timeline-card-cancel .comment-text {
  border-left-color: #E6A23C;
}

/* 操作按钮区域 */
.action-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-status {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.status-label {
  font-weight: 500;
  margin-right: 8px;
  color: #606266;
}

.action-btn-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 10px;
  padding: 15px 10px;
}

.action-button {
  flex: 1;
  min-width: 70px;
  height: 32px;
  font-size: 12px;
  font-weight: 500;
  padding: 0 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.action-button i {
  margin-right: 4px;
  font-size: 14px;
}

.approve-btn {
  background-color: #67c23a;
  border-color: #67c23a;
}

.reject-btn {
  background-color: #f56c6c;
  border-color: #f56c6c;
}

.cancel-btn {
  background-color: #e6a23c;
  border-color: #e6a23c;
}

.download-btn {
  background-color: #409eff;
  border-color: #409eff;
}

.action-tips {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
  color: #67c23a;
  font-size: 13px;
  line-height: 1.5;
}

.action-tips i {
  font-size: 16px;
  margin-right: 8px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .el-step__title {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .page-header button {
    margin-top: 10px;
    align-self: flex-end;
  }

  .header-left {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-title {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .el-col-16, .el-col-8 {
    width: 100%;
  }

  .action-card {
    margin-top: 20px;
  }

  .action-btn-group {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
  }

  .action-button {
    width: calc(50% - 5px);
  }
}
</style>

{{ ... }}
