<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span><i class="el-icon-setting"></i> 图纸权限配置管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshConfig">
          <i class="el-icon-refresh"></i> 刷新配置
        </el-button>
      </div>

      <!-- 配置状态 -->
      <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="24">
          <el-alert
            :title="configStatus.enabled ? '图纸权限检查已启用' : '图纸权限检查已禁用'"
            :type="configStatus.enabled ? 'success' : 'warning'"
            :closable="false"
            show-icon>
            <template slot="default">
              <p>当前系统共配置了 <strong>{{ configStatus.totalMappings }}</strong> 种图纸类型的权限映射</p>
            </template>
          </el-alert>
        </el-col>
      </el-row>

      <!-- 配置详情 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="never" class="config-card">
            <div slot="header">
              <i class="el-icon-document"></i> 权限映射配置
            </div>
            <el-table :data="mappingTableData" border style="width: 100%" size="small">
              <el-table-column prop="subType" label="图纸类型" width="120" align="center">
                <template slot-scope="scope">
                  <el-tag :type="getSubTypeTagType(scope.row.subType)" size="small">
                    {{ scope.row.subType }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="permissionCode" label="权限代码" min-width="200">
                <template slot-scope="scope">
                  <code class="permission-code">{{ scope.row.permissionCode }}</code>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card shadow="never" class="config-card">
            <div slot="header">
              <i class="el-icon-info"></i> 系统配置
            </div>
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="权限检查">
                <el-tag :type="configStatus.enabled ? 'success' : 'danger'" size="small">
                  <i :class="configStatus.enabled ? 'el-icon-check' : 'el-icon-close'"></i>
                  {{ configStatus.enabled ? '已启用' : '已禁用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="大小写敏感">
                <el-tag :type="configStatus.caseSensitive ? 'warning' : 'success'" size="small">
                  <i :class="configStatus.caseSensitive ? 'el-icon-warning' : 'el-icon-check'"></i>
                  {{ configStatus.caseSensitive ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="记录权限日志">
                <el-tag :type="configStatus.logPermissionChecks ? 'success' : 'info'" size="small">
                  <i :class="configStatus.logPermissionChecks ? 'el-icon-view' : 'el-icon-view'"></i>
                  {{ configStatus.logPermissionChecks ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="允许未知类型">
                <el-tag :type="configStatus.allowUnknownSubtype ? 'success' : 'danger'" size="small">
                  <i :class="configStatus.allowUnknownSubtype ? 'el-icon-check' : 'el-icon-close'"></i>
                  {{ configStatus.allowUnknownSubtype ? '是' : '否' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="未知类型权限">
                <code v-if="configStatus.unknownSubtypePermission" class="permission-code">
                  {{ configStatus.unknownSubtypePermission }}
                </code>
                <span v-else class="text-muted">无</span>
              </el-descriptions-item>
              <el-descriptions-item label="映射总数">
                <el-tag type="info" size="small">
                  <i class="el-icon-document-copy"></i>
                  {{ configStatus.totalMappings }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>

      <!-- 权限测试 -->
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card shadow="never" class="config-card">
            <div slot="header">
              <i class="el-icon-cpu"></i> 权限映射测试
            </div>
            <el-form :inline="true" :model="testForm" class="demo-form-inline">
              <el-form-item label="图纸类型">
                <el-input 
                  v-model="testForm.subType" 
                  placeholder="请输入图纸类型，如：PDF、2D、3D等" 
                  style="width: 250px;"
                  clearable>
                  <i slot="prefix" class="el-icon-document"></i>
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="testPermission" :loading="testLoading">
                  <i class="el-icon-search"></i> 测试权限
                </el-button>
                <el-button @click="clearTest">
                  <i class="el-icon-refresh-left"></i> 清除
                </el-button>
              </el-form-item>
            </el-form>

            <div v-if="testResult" style="margin-top: 15px;">
              <el-alert
                :title="testResult.hasMapping ? '✓ 找到权限映射' : '✗ 未找到权限映射'"
                :type="testResult.hasMapping ? 'success' : 'warning'"
                :closable="false"
                show-icon>
                <template slot="default">
                  <div class="test-result">
                    <p><strong>图纸类型:</strong> <el-tag size="mini">{{ testResult.subType }}</el-tag></p>
                    <p><strong>权限代码:</strong> 
                      <code v-if="testResult.permissionCode" class="permission-code">{{ testResult.permissionCode }}</code>
                      <span v-else class="text-muted">无</span>
                    </p>
                    <p><strong>是否有映射:</strong> 
                      <el-tag :type="testResult.hasMapping ? 'success' : 'danger'" size="mini">
                        {{ testResult.hasMapping ? '是' : '否' }}
                      </el-tag>
                    </p>
                    <div v-if="!testResult.caseSensitive && (testResult.upperCaseTest || testResult.lowerCaseTest)">
                      <p><strong>大写测试:</strong> 
                        <code v-if="testResult.upperCaseTest" class="permission-code">{{ testResult.upperCaseTest }}</code>
                        <span v-else class="text-muted">无</span>
                      </p>
                      <p><strong>小写测试:</strong> 
                        <code v-if="testResult.lowerCaseTest" class="permission-code">{{ testResult.lowerCaseTest }}</code>
                        <span v-else class="text-muted">无</span>
                      </p>
                    </div>
                  </div>
                </template>
              </el-alert>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getDrawingPermissionConfig, getDrawingPermissionStatus, testDrawingPermission } from '@/api/system/drawing/config'

export default {
  name: 'DrawingPermissionConfig',
  data() {
    return {
      configStatus: {
        enabled: false,
        caseSensitive: false,
        logPermissionChecks: false,
        allowUnknownSubtype: true,
        unknownSubtypePermission: '',
        totalMappings: 0
      },
      mappingTableData: [],
      testForm: {
        subType: ''
      },
      testResult: null,
      testLoading: false
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    async loadConfig() {
      try {
        // 加载配置状态
        const statusResponse = await getDrawingPermissionStatus()
        this.configStatus = statusResponse.data

        // 加载配置详情
        const configResponse = await getDrawingPermissionConfig()
        const config = configResponse.data
        
        // 转换映射数据为表格格式
        this.mappingTableData = Object.entries(config.subtypeMappings).map(([subType, permissionCode]) => ({
          subType,
          permissionCode
        }))
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$modal.msgError('加载配置失败')
      }
    },
    async refreshConfig() {
      await this.loadConfig()
      this.$modal.msgSuccess('配置已刷新')
    },
    async testPermission() {
      if (!this.testForm.subType.trim()) {
        this.$modal.msgError('请输入图纸类型')
        return
      }

      this.testLoading = true
      try {
        const response = await testDrawingPermission({ subType: this.testForm.subType })
        this.testResult = response.data
      } catch (error) {
        console.error('测试权限失败:', error)
        this.$modal.msgError('测试权限失败')
      } finally {
        this.testLoading = false
      }
    },
    clearTest() {
      this.testForm.subType = ''
      this.testResult = null
    },
    getSubTypeTagType(subType) {
      const typeMap = {
        'PDF': 'danger',
        '2D': 'warning', 
        '3D': 'success',
        '2D/3D': 'info'
      }
      return typeMap[subType] || 'primary'
    }
  }
}
</script>

<style scoped>
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both
}

.box-card {
  margin: 20px;
}

.config-card {
  height: 100%;
}

.permission-code {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #e6a23c;
  border: 1px solid #ebeef5;
}

.text-muted {
  color: #909399;
  font-style: italic;
}

.test-result p {
  margin: 8px 0;
}

.test-result strong {
  display: inline-block;
  width: 100px;
  color: #606266;
}
</style>
