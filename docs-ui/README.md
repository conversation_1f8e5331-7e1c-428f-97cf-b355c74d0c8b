# 前端构建说明

## 前端部署方式

本项目前后端采用统一部署流程，前端静态文件通过以下方式部署：

1. **开发阶段**: 使用 `npm run dev` 进行本地开发
2. **构建阶段**: 使用 `npm run build` 生成 `dist` 目录
3. **部署阶段**: 将 `dist` 目录内容部署到 Nginx 服务器中

## CI/CD 中的前端部署

在CI/CD流程中，前端部署遵循以下步骤：

1. 构建流程会将整个 `docs-ui` 目录复制到服务器
2. 服务器会直接使用预构建的 `dist` 目录作为静态资源
3. 这些静态资源会通过卷挂载到 Nginx 容器中

## 如何手动更新前端

如需手动更新前端，可以按照以下步骤操作：

```bash
# 进入前端目录
cd docs-ui

# 安装依赖
npm install

# 构建前端
npm run build

# 将构建产物复制到服务器
scp -r dist/ user@server:/data/docs-manage/docs-ui/
```

## 注意事项

1. 确保构建前已安装所有依赖
2. 确保服务器上 Nginx 配置正确，指向静态资源目录
3. 如需修改部署方式，请更新 `docker-compose.yml` 中的卷挂载配置

## 开发

```bash
# 克隆项目
git clone https://gitee.com/y_project/RuoYi-Vue

# 进入项目目录
cd ruoyi-ui

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```