# 构建阶段
FROM node:16 as build-stage

WORKDIR /app

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制项目文件
COPY . .

# 构建项目
RUN npm run build:prod

# 生产阶段
FROM nginx:stable-alpine as production-stage

# 创建SSL证书目录
RUN mkdir -p /etc/nginx/ssl

# 复制构建结果到nginx目录
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露80和443端口
EXPOSE 80 443

# 运行nginx
CMD ["nginx", "-g", "daemon off;"]