services:
  docs-manage:
    image: docs-manage:latest
    container_name: docs-manage
    restart: always
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_DRUID_MASTER_URL=************************************************************************************************************************************************
      - SPRING_DATASOURCE_DRUID_MASTER_USERNAME=root
      - SPRING_DATASOURCE_DRUID_MASTER_PASSWORD=123456
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_DATABASE=2
    volumes:
      - ./images:/app/data/images
      - ./logs:/app/data/logs
    depends_on:
      - mysql
      - redis
    networks:
      - docs-network

  nginx:
    image: nginx:alpine
    container_name: docs-ui
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docs-ui/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - /data/docker/nginx/ssl/_.anynet.it.crt:/etc/nginx/ssl/_.anynet.it.crt:ro
      - /data/docker/nginx/ssl/_.anynet.it.key:/etc/nginx/ssl/_.anynet.it.key:ro
      - ./docs-ui/dist:/usr/share/nginx/html:ro
    depends_on:
      - docs-manage
    networks:
      - docs-network

  mysql:
    image: mysql:8.0
    container_name: docs-mysql
    restart: always
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=file_manage
      - TZ=Asia/Shanghai
      - MYSQL_ROOT_HOST=%
    command:
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_general_ci
      - --default-authentication-plugin=mysql_native_password
      - --lower_case_table_names=1
      - --max_allowed_packet=128M
      - --net_read_timeout=3600
      - --net_write_timeout=3600
      - --wait_timeout=3600
      - --interactive_timeout=3600
      - --connect_timeout=60
      - --innodb_buffer_pool_size=256M
      - --bind-address=0.0.0.0
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - docs-network
    healthcheck: # 确保 healthcheck 与 image, ports 等同级
      test: ["CMD", "mysqladmin" ,"ping", "-h", "localhost", "-P", "3306", "-uroot", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
  redis:
    image: redis:6.2
    container_name: docs-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - docs-network

networks:
  docs-network:
    name: docs-network
    driver: bridge

volumes:
  mysql_data:
  redis_data: