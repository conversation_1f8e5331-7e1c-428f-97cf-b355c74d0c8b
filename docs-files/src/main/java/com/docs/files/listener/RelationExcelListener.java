package com.docs.files.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.BiConsumer;

/**
 * 物料图纸关系Excel解析监听器 (优化版)
 */
@Slf4j
@Getter
public class RelationExcelListener extends AnalysisEventListener<Map<Integer, String>> {

    // --- 常量定义 ---
    private static final int HEADER_ROW_INDEX = 1; // 列名行索引

    // 期望的列名及其对应的字段Setter
    private static final Map<String, BiConsumer<MaterialDrawingMap, String>> EXPECTED_COLUMNS = new HashMap<>();
    static {
        EXPECTED_COLUMNS.put("物料编码", MaterialDrawingMap::setMaterialCode);
        EXPECTED_COLUMNS.put("图纸编号", MaterialDrawingMap::setDrawingNo);
    }

    // 必须存在的列名关键字
    private static final Set<String> REQUIRED_COLUMN_KEYWORDS_MATERIAL =
        new HashSet<>(Arrays.asList("物料编码", "物料代码", "编码"));
    private static final Set<String> REQUIRED_COLUMN_KEYWORDS_DRAWING =
        new HashSet<>(Arrays.asList("图纸编号", "图纸号", "图号"));

    private static final String ESSENTIAL_FIELD_MATERIAL_CODE = "materialCode"; // 内部字段名

    // --- 实例变量 ---
    private final List<MaterialDrawingMap> relations = new ArrayList<>();
    private Map<Integer, BiConsumer<MaterialDrawingMap, String>> columnIndexToSetterMap; // 列索引 -> Setter映射
    private int currentRowIndex = 0;
    private boolean headerProcessed = false;

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        currentRowIndex = context.readRowHolder().getRowIndex() + 1; // rowIndex从0开始，+1使其从1开始

        // 假定第一行为标题行
        if (!headerProcessed && currentRowIndex >= HEADER_ROW_INDEX) {
            parseAndValidateHeaderRow(data);
            headerProcessed = true; // 标记标题行已处理
            return; // 处理完标题行后，不处理该行的数据
        }

        // 处理数据行 (在标题行之后)
        if (headerProcessed && currentRowIndex > HEADER_ROW_INDEX) {
            // 确保列映射已成功初始化
            if (columnIndexToSetterMap == null) {
                 log.error("Column mapping not initialized when processing data row {}", currentRowIndex);
                 throw new ServiceException("无法处理数据行，列映射未初始化");
            }
            parseDataRow(data).ifPresent(relations::add);
        }
        // 标题行之前的数据将被忽略
    }

    /**
     * 解析列名行并生成列索引到Setter的映射
     */
    private void parseAndValidateHeaderRow(Map<Integer, String> headerData) {
        columnIndexToSetterMap = new HashMap<>();
        boolean foundMaterialCodeColumn = false;
        boolean foundDrawingNoColumn = false;

        for (Map.Entry<Integer, String> entry : headerData.entrySet()) {
            String headerText = StringUtils.trimToNull(entry.getValue());
            if (headerText == null) {
                continue;
            }

            BiConsumer<MaterialDrawingMap, String> setter = EXPECTED_COLUMNS.get(headerText);
            if (setter != null) {
                columnIndexToSetterMap.put(entry.getKey(), setter);
                // 检查是否找到了必要列
                if (REQUIRED_COLUMN_KEYWORDS_MATERIAL.contains(headerText)) {
                    foundMaterialCodeColumn = true;
                }
                if (REQUIRED_COLUMN_KEYWORDS_DRAWING.contains(headerText)) {
                    foundDrawingNoColumn = true;
                }
            } else {
                log.debug("Header '{}' at index {} does not map to a known Relation field.", headerText, entry.getKey());
            }
        }

        // 验证必要列是否存在
        if (!foundMaterialCodeColumn) {
            throw new ServiceException("Excel文件缺少必要的列: 物料编码/物料代码/编码");
        }
        if (!foundDrawingNoColumn) {
            throw new ServiceException("Excel文件缺少必要的列: 图纸编号/图纸号/图号");
        }
        log.info("Parsed relation header row successfully. Column index to setter map size: {}", columnIndexToSetterMap.size());
    }

    /**
     * 解析数据行
     * @return Optional<MaterialDrawingMap> 如果行有效则包含对象，否则为空
     */
    private Optional<MaterialDrawingMap> parseDataRow(Map<Integer, String> rowData) {
        MaterialDrawingMap relation = new MaterialDrawingMap();
        boolean hasEssentialValue = false;

        for (Map.Entry<Integer, BiConsumer<MaterialDrawingMap, String>> entry : columnIndexToSetterMap.entrySet()) {
            Integer columnIndex = entry.getKey();
            BiConsumer<MaterialDrawingMap, String> setter = entry.getValue();
            String cellValue = StringUtils.trimToNull(rowData.get(columnIndex));

            if (cellValue != null) {
                try {
                    setter.accept(relation, cellValue);
                    // 检查是否设置了物料编码
                    if (relation.getMaterialCode() != null && !hasEssentialValue) {
                         hasEssentialValue = true;
                    }
                } catch (Exception e) {
                    log.warn("Failed to set value '{}' for column index {} in row {}. Error: {}",
                             cellValue, columnIndex, currentRowIndex, e.getMessage());
                }
            }
        }

        // 只添加有物料编码的记录
        if (!hasEssentialValue || StringUtils.isBlank(relation.getMaterialCode())) {
            log.debug("Skipping row {} because essential field '{}' is missing or blank.",
                      currentRowIndex, ESSENTIAL_FIELD_MATERIAL_CODE);
            return Optional.empty();
        }

        return Optional.of(relation);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 检查列映射是否已成功初始化
        if (!headerProcessed || columnIndexToSetterMap == null) {
            log.error("Failed to initialize column mapping for Relation Excel. Header row might be missing or invalid.");
            // 如果允许空文件或无标题行，这里的逻辑可能需要调整
            throw new ServiceException("未找到有效的Excel列标题行");
        }

        if (relations.isEmpty() && currentRowIndex > HEADER_ROW_INDEX) {
            // 如果处理了标题行但没有解析到任何有效数据行
             log.warn("No valid data rows found in the relation Excel file after header processing.");
            // 根据业务需求决定是否抛出异常
            // throw new ServiceException("Excel文件中没有有效的物料图纸关系数据");
        }

        log.info("Finished parsing Relation Excel. Total relations parsed: {}", relations.size());
    }
}