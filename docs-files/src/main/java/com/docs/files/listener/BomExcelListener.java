package com.docs.files.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.tables.pojos.BomItems;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;

/**
 * BOM Excel解析监听器 (优化版)
 * 提高可读性、健壮性和可维护性
 */
@Slf4j
@Getter
public class BomExcelListener extends AnalysisEventListener<Map<Integer, String>> {

    // --- 常量定义 ---
    private static final int HEADER_ROW_1_INDEX = 1; // 主标题行
    private static final int HEADER_ROW_2_INDEX = 2; // 子标题行
    private static final int COLUMN_NAME_ROW_INDEX = 3; // 列名行

    private static final String HEADER_BOM_HEADER_KEY = "bomHeader";
    private static final String HEADER_BOM_SUBHEADER_KEY = "bomSubheader";

    // 列名关键字与BomItems字段Setter的映射
    private static final Map<String, BiConsumer<BomItems, String>> COLUMN_MAPPINGS = new HashMap<>();
    static {
        COLUMN_MAPPINGS.put("itemNo", BomItems::setItemNo); // 序号
        // depth 和 quantity 的 setter 需要特殊处理，在 parseDataRow 中进行
        // COLUMN_MAPPINGS.put("depth", (item, val) -> parseAndSetInteger(val, item::setDepth, "深度")); // 深度, 层次
        COLUMN_MAPPINGS.put("materialCode", BomItems::setMaterialCode); // 物料编码, 料号
        COLUMN_MAPPINGS.put("materialName", BomItems::setMaterialName); // 物料名称, 名称
        COLUMN_MAPPINGS.put("specification", BomItems::setSpecification); // 型号, 规格
        COLUMN_MAPPINGS.put("supplier", BomItems::setSupplier); // 供应商, 品牌, 厂家
        COLUMN_MAPPINGS.put("unit", BomItems::setUnit); // 单位
        // COLUMN_MAPPINGS.put("quantity", (item, val) -> parseAndSetBigDecimal(val, item::setQuantity, "数量")); // 数量
        COLUMN_MAPPINGS.put("productionProperty", BomItems::setProductionProperty); // 生产属性, 属性
        COLUMN_MAPPINGS.put("category", BomItems::setCategory); // 类别
        COLUMN_MAPPINGS.put("remark", BomItems::setRemark); // 备注
    }

    // 关键字与内部字段名的映射 (用于识别Excel列)
    private static final Map<String, String> KEYWORD_TO_FIELD_MAP = new HashMap<>();
    static {
        KEYWORD_TO_FIELD_MAP.put("序号", "itemNo");
        KEYWORD_TO_FIELD_MAP.put("深度", "depth");
        KEYWORD_TO_FIELD_MAP.put("物料编码", "materialCode");
        KEYWORD_TO_FIELD_MAP.put("物料名称", "materialName");
        KEYWORD_TO_FIELD_MAP.put("型号规格", "specification");
        KEYWORD_TO_FIELD_MAP.put("供应商/品牌", "supplier");
        KEYWORD_TO_FIELD_MAP.put("单位", "unit");
        KEYWORD_TO_FIELD_MAP.put("数量", "quantity");
        KEYWORD_TO_FIELD_MAP.put("生产属性", "productionProperty");
        KEYWORD_TO_FIELD_MAP.put("类别", "category");
        KEYWORD_TO_FIELD_MAP.put("备注", "remark");
    }

    private static final String ESSENTIAL_COLUMN = "materialName"; // 必须存在的列对应的内部字段名

    // --- 实例变量 ---
    private final Map<String, String> headers = new HashMap<>(2); // 主副标题
    private final List<BomItems> bomItems = new ArrayList<>();
    private final Integer fileId;
    private final String bomType;
    private Map<Integer, BiConsumer<BomItems, String>> columnIndexToSetterMap; // 列索引 -> Setter映射
    private Map<String, Integer> fieldNameToColumnIndexMap; // 字段名 -> 列索引映射
    private int currentRowIndex = 0; // EasyExcel从0开始计数，我们调整为1开始便于理解

    public BomExcelListener(Integer fileId, String bomType) {
        this.fileId = Objects.requireNonNull(fileId, "File ID cannot be null");
        this.bomType = Objects.requireNonNull(bomType, "BOM Type cannot be null");
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        currentRowIndex = context.readRowHolder().getRowIndex() + 1; // rowIndex从0开始，+1使其从1开始

        // 使用switch表达式处理不同行 (Java 14+) 或 if-else if
        // 这里使用 if-else if 保证兼容性
        if (currentRowIndex == HEADER_ROW_1_INDEX) {
            extractHeader(data, HEADER_BOM_HEADER_KEY);
        } else if (currentRowIndex == HEADER_ROW_2_INDEX) {
            extractHeader(data, HEADER_BOM_SUBHEADER_KEY);
        } else if (currentRowIndex == COLUMN_NAME_ROW_INDEX) {
            parseAndValidateHeaderRow(data);
        } else if (currentRowIndex > COLUMN_NAME_ROW_INDEX) {
            // 确保列映射已成功初始化
            if (columnIndexToSetterMap == null) {
                // 通常不应该发生，因为doAfterAllAnalysed会检查，但增加防御性编程
                 log.error("Column mapping not initialized when processing data row {}", currentRowIndex);
                 throw new ServiceException("无法处理数据行，列映射未初始化");
            }
            parseDataRow(data).ifPresent(bomItems::add);
        }
        // 其他行（如标题行之前的空行）将被忽略
    }

    /**
     * 提取主/副标题
     */
    private void extractHeader(Map<Integer, String> data, String headerKey) {
        // 通常标题在第一列
        Optional.ofNullable(data.get(0))
                .filter(StringUtils::isNotBlank)
                .ifPresent(headerValue -> headers.put(headerKey, headerValue.trim()));
    }

    /**
     * 解析列名行并生成列索引到Setter的映射
     */
    private void parseAndValidateHeaderRow(Map<Integer, String> headerData) {
        columnIndexToSetterMap = new HashMap<>();
        fieldNameToColumnIndexMap = new HashMap<>(); // 初始化新的映射
        boolean foundEssentialColumn = false;

        for (Map.Entry<Integer, String> entry : headerData.entrySet()) {
            String headerText = StringUtils.trimToNull(entry.getValue());
            if (headerText == null) {
                continue;
            }

            // 查找关键字匹配的字段名
            String fieldName = KEYWORD_TO_FIELD_MAP.get(headerText);
            if (fieldName != null) {
                 // 获取对应的Setter方法
                 BiConsumer<BomItems, String> setter = COLUMN_MAPPINGS.get(fieldName);
                 if (setter != null) {
                     Integer columnIndex = entry.getKey();
                     columnIndexToSetterMap.put(columnIndex, setter);
                     fieldNameToColumnIndexMap.put(fieldName, columnIndex); // 填充新的映射
                     if (ESSENTIAL_COLUMN.equals(fieldName)) {
                         foundEssentialColumn = true;
                     }
                 } else {
                      // 如果是 depth 或 quantity，只记录列索引，不在 COLUMN_MAPPINGS 中查找 setter
                      if ("depth".equals(fieldName) || "quantity".equals(fieldName)) {
                          fieldNameToColumnIndexMap.put(fieldName, entry.getKey());
                          // 对于 depth，也需要检查是否为必要列（如果业务需要）
                          // if (ESSENTIAL_COLUMN.equals(fieldName)) { foundEssentialColumn = true; }
                      } else {
                          log.warn("No setter found for field name '{}' derived from header '{}'", fieldName, headerText);
                      }
                 }
            } else {
                 log.debug("Header '{}' at index {} does not map to a known BOM field.", headerText, entry.getKey());
            }
        }

        // 验证必要列是否存在
        // 检查 fieldNameToColumnIndexMap 是否包含 ESSENTIAL_COLUMN
        if (!fieldNameToColumnIndexMap.containsKey(ESSENTIAL_COLUMN)) {
            log.error("Essential column '{}' not found in header row.", ESSENTIAL_COLUMN);
            throw new ServiceException("Excel缺少必要的列: 物料名称");
        }
        log.info("Parsed header row successfully. Column index to setter map size: {}, Field name to column index map size: {}",
                 columnIndexToSetterMap.size(), fieldNameToColumnIndexMap.size());
    }


    /**
     * 解析数据行
     * @return Optional<BomItems> 如果行有效则包含BomItems，否则为空
     */
    private Optional<BomItems> parseDataRow(Map<Integer, String> rowData) {
        BomItems bomItem = new BomItems();
        bomItem.setFileId(fileId);
        bomItem.setBomType(bomType);
        boolean hasEssentialValue = false; // 跟踪是否设置了必要字段的值，例如物料名称

        for (Map.Entry<Integer, BiConsumer<BomItems, String>> entry : columnIndexToSetterMap.entrySet()) {
            Integer columnIndex = entry.getKey();
            BiConsumer<BomItems, String> setter = entry.getValue();
            String cellValue = StringUtils.trimToNull(rowData.get(columnIndex));

            if (cellValue != null) {
                try {
                    setter.accept(bomItem, cellValue);
                    // 检查是否是必要字段的值被设置了
                    if (bomItem.getMaterialName() != null && !hasEssentialValue) {
                        hasEssentialValue = true;
                    }
                } catch (Exception e) {
                     log.warn("Failed to set value '{}' for column index {} in row {}. Error: {}",
                              cellValue, columnIndex, currentRowIndex, e.getMessage());
                }
            }
        }

        // 特殊处理需要类型转换的字段
        handleSpecialTypeFields(rowData, bomItem);

        // 如果必要字段（如物料名称）没有有效值，则认为此行无效
        if (!hasEssentialValue || StringUtils.isBlank(bomItem.getMaterialName())) {
             log.debug("Skipping row {} because essential column '{}' is missing or blank.", currentRowIndex, ESSENTIAL_COLUMN);
             return Optional.empty();
        }

        return Optional.of(bomItem);
    }

    /**
     * 处理需要特殊类型转换的字段
     */
    private void handleSpecialTypeFields(Map<Integer, String> rowData, BomItems bomItem) {
        // 处理 depth
        Optional.ofNullable(fieldNameToColumnIndexMap.get("depth")) // 使用新的映射获取索引
                .map(rowData::get)
                .filter(StringUtils::isNotBlank)
                .ifPresent(val -> {
                    try {
                        bomItem.setDepth(Integer.parseInt(val));
                    } catch (NumberFormatException e) {
                        log.warn("Invalid integer value '{}' for depth in row {}. Skipping field.", val, currentRowIndex);
                    }
                });

        // 处理 quantity
        Optional.ofNullable(fieldNameToColumnIndexMap.get("quantity")) // 使用新的映射获取索引
                .map(rowData::get)
                .filter(StringUtils::isNotBlank)
                .ifPresent(val -> {
                    try {
                        bomItem.setQuantity(new BigDecimal(val));
                    } catch (NumberFormatException e) {
                        log.warn("Invalid decimal value '{}' for quantity in row {}. Skipping field.", val, currentRowIndex);
                    }
                });
    }

    /**
     * 根据内部字段名查找对应的列索引
     * @deprecated 不再需要，直接使用 fieldNameToColumnIndexMap
     */
    @Deprecated
    private Integer getSetterIndex(String fieldName) {
        // return fieldNameToColumnIndexMap.get(fieldName);
        throw new UnsupportedOperationException("getSetterIndex is deprecated, use fieldNameToColumnIndexMap directly.");
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 检查列映射是否已成功初始化
        if (columnIndexToSetterMap == null) {
            // 如果columnIndexToSetterMap仍然为null，说明可能没有找到有效的列名行
            log.error("Failed to initialize column mapping. Header row might be missing or invalid.");
            throw new ServiceException("未找到有效的Excel列标题行");
        }
        // 可以在这里添加其他最终验证逻辑
        log.info("Finished parsing BOM Excel. Total items parsed: {}. File ID: {}, BOM Type: {}",
                 bomItems.size(), fileId, bomType);
    }
}