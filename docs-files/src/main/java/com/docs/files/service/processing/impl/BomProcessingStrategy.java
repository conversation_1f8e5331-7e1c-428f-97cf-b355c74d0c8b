package com.docs.files.service.processing.impl;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.vo.BomItemsVO;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.files.service.BomItemService;
import com.docs.files.service.MaterialDrawingMapService;
import com.docs.files.service.MaterialDrawingRelationsService;
import com.docs.files.service.processing.FileProcessingStrategy;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.util.FilePathUtil;
import com.docs.files.util.FileProcessorHelper;
import com.docs.files.service.strategy.validator.FileValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * BOM文件处理策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BomProcessingStrategy implements FileProcessingStrategy {
    private final FileValidator bomFileValidator;
    private final FileProcessorHelper fileProcessorHelper;
    private final BomItemService bomItemService;
    private final MaterialDrawingRelationsService materialDrawingRelationsService;
    private final MaterialDrawingMapService materialDrawingMapService;
    private final FilePathUtil filePathUtil;

    @Override
    public String getFileType() {
        return BOM_FILE_TYPE;
    }

    @Override
    public boolean validateRequest(FileUploadRequest request) {
        try {
            bomFileValidator.validate(request);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean validateFile(MultipartFile file) {
        return file != null && !file.isEmpty();
    }

    @Override
    public Files checkVersionExists(FileUploadRequest request) {
        return fileProcessorHelper.selectByVersion(getFileType(), request.getVersion(), request.getFileName());
    }

    @Override
    public String buildTargetDirectory(FileUploadRequest request) {
        return filePathUtil.buildTargetDirectory(getFileType(), request.getVersion());
    }

    @Override
    public AjaxResult postProcess(Files fileInfo, Path[] filePaths, ProductModels model, FileUploadRequest request) {
        try {
            Path bomFilePath = filePaths[0];
            Path relationFilePath = filePaths[1];

            // 处理BOM数据
            bomItemService.parseBomExcelAndSave(fileInfo, bomFilePath.toString(), request.getSubType());

            // 解析物料-图纸关系文件
            List<MaterialDrawingMap> relations = materialDrawingRelationsService.parseRelationExcel(relationFilePath.toString(), fileInfo.getId());

            // 将关系数据转换为Map，用于更新BomItems
            Map<String,MaterialDrawingMap> relationsMap = relations.stream()
                    .collect(Collectors.toMap(MaterialDrawingMap::getMaterialCode, Function.identity(), (existing, replacement) -> existing));

            List<BomItemsVO> bomItems = bomItemService.selectByFileId(fileInfo.getId());

            bomItems.forEach(bomItem -> {
                MaterialDrawingMap relation = relationsMap.get(bomItem.getMaterialCode());
                if (relation != null) {
                    relation.setMaterialName(bomItem.getMaterialName());
                    relation.setSpecification(bomItem.getSpecification());
                }
            });
            if(model != null){
                bomItems.forEach(bomItem -> bomItem.setModelId(model.getId()));
            }
            // 保存物料-图纸映射关系到数据库
            int successCount = materialDrawingMapService.batchUpsertFromRelations(relations);
            log.info("批量处理物料-图纸映射完成，成功率: {}/{}", successCount, relations.size());

            bomItemService.updateBomItems(bomItems);

            return AjaxResult.success("BOM文件及关系上传成功");
        } catch (Exception e) {
            log.error("解析BOM相关数据失败 for file ID {}: {}", fileInfo.getId(), e.getMessage(), e);
            throw new ServiceException("解析BOM相关数据失败: " + e.getMessage());
        }
    }
}
