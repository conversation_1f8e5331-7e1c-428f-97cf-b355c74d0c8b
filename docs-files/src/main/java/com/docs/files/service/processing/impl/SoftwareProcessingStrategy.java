package com.docs.files.service.processing.impl;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.files.service.processing.FileProcessingStrategy;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.util.FilePathUtil;
import com.docs.files.util.FileProcessorHelper;
import com.docs.files.service.strategy.validator.FileValidator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;

/**
 * 软件文件处理策略
 */
@Component
@RequiredArgsConstructor
public class SoftwareProcessingStrategy implements FileProcessingStrategy {
    private final FileValidator softwareFileValidator;
    private final FileProcessorHelper fileProcessorHelper;
    private final FilePathUtil filePathUtil;
    
    @Override
    public String getFileType() {
        return SOFTWARE_FILE_TYPE;
    }
    
    @Override
    public boolean validateRequest(FileUploadRequest request) {
        try {
            softwareFileValidator.validate(request);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public boolean validateFile(MultipartFile file) {
        return file != null && !file.isEmpty();
    }
    
    @Override
    public Files checkVersionExists(FileUploadRequest request) {
        // 以fileType+version+fileName为唯一性校验
        return fileProcessorHelper.selectByVersion(getFileType(), request.getVersion(), request.getFileName());
    }
    
    @Override
    public String buildTargetDirectory(FileUploadRequest request) {
        return filePathUtil.buildTargetDirectory(getFileType(), request.getVersion());
    }
    
    @Override
    public AjaxResult postProcess(Files fileInfo, Path[] filePaths, ProductModels model, FileUploadRequest request) {
        // 软件文件没有特殊的后处理逻辑
        return AjaxResult.success("软件文件上传成功");
    }
}
