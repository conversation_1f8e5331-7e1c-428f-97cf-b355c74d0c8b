package com.docs.files.service;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.entity.FilesEntity;
import com.docs.common.core.domain.model.FileChangeRequest;
import com.docs.common.core.domain.vo.FileListVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface FilesService {

    /**
     * 查询文件列表
     */
    List<FileListVO> selectFilesList(FilesEntity filesEntity);

    /**
     * 上传文件
     */
    AjaxResult uploadFile(MultipartFile file, String productModel, String fileType,
                          String subType, String drawingNo, String version);

    /**
     * 上传多文件
     */
    AjaxResult uploadFiles(MultipartFile[] files, String productModel, String fileType,
                           String subType, String drawingNo, String version);

    /**
     * 变更文件版本
     */
    AjaxResult changeFileVersion(FileChangeRequest request);

    /**
     * 变更文件版本（多文件）
     */
    AjaxResult changeFileVersionMultiple(FileChangeRequest request);
    
    /**
     * 变更文件版本（带版本号校验）
     * 新版本号必须大于当前版本号
     */
    AjaxResult changeFileVersionWithValidation(FileChangeRequest request);

    /**
     * 下载文件
     */
    void downloadFile(Integer fileId, HttpServletResponse response) throws IOException;

    /**
     * 删除文件记录
     */
    int deleteFile(Integer fileId);

}
