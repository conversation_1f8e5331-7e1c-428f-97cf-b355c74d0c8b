package com.docs.files.service.strategy;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.enums.FilesStatus;
import com.docs.common.jooq.generated.tables.daos.BomItemsDao;
import com.docs.common.jooq.generated.tables.daos.FileChangesDao;
import com.docs.common.jooq.generated.tables.daos.FilesDao;
import com.docs.common.jooq.generated.tables.daos.MaterialDrawingMapDao;
import com.docs.common.jooq.generated.tables.pojos.*;
import com.docs.common.jooq.generated.tables.records.MaterialDrawingMapRecord;
import org.springframework.util.FileCopyUtils;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Collections;
import com.docs.common.utils.SecurityUtils;
import com.docs.files.service.BomDrawingChangeHistoryService;
import com.docs.files.service.MaterialDrawingMapService;
import com.docs.common.enums.ChangeType;
import com.docs.common.enums.DrawingFileType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.docs.common.constant.FileConstants;
import org.jooq.DSLContext;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BomItemDrawingStrategy extends AbstractFileStrategy {

    private final BomItemsDao bomItemsDao;
    private final FileChangesDao fileChangesDao;
    private final FilesDao filesDao;
    private final MaterialDrawingMapDao materialDrawingMapDao;
    private final DSLContext dsl;
    @Resource
    private MaterialDrawingMapService materialDrawingMapService;
    @Resource
    private BomDrawingChangeHistoryService bomDrawingChangeHistoryService;


    @Override
    public String getFileType() {
        return FilesFileType.BOM_DRAWING.name();
    }

    @Override
    public void validateFileCount(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            throw new ServiceException("请至少上传一个图纸文件");
        }
        if (files.length > 4) {
            throw new ServiceException("最多只能上传4个文件(PDF、2D图纸、3D图纸、2D/3D压缩包)");
        }
        // 检查是否有空文件
        for (MultipartFile file : files) {
            if (file == null || file.isEmpty()) {
                throw new ServiceException("上传的文件不能为空");
            }
        }
    }

    /**
     * 处理BOM项多文件图纸上传
     *
     * @param files 上传的图纸文件数组
     * @param bomItemId BOM项ID
     * @param drawingNo 图纸编号
     * @param fileTypes 文件类型数组
     * @param remark 备注 (可选，最大500字符)
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadBomItemDrawings(MultipartFile[] files, Integer bomItemId, String drawingNo, String[] fileTypes, String remark) {
        try {
            // 1. 验证文件数量和类型数量匹配
            validateFileCount(files);
            if (fileTypes.length != files.length) {
                return AjaxResult.error("文件数量与文件类型数量不匹配");
            }
            
            // 2. 查找BOM项
            BomItems bomItem = bomItemsDao.findById(bomItemId);
            if (bomItem == null) {
                return AjaxResult.error("未找到对应的BOM项");
            }
            
            // 3. 获取现有的图纸映射关系
            MaterialDrawingMap oldMaterialDrawingMap = materialDrawingMapService.findByMaterialCode(bomItem.getMaterialCode());
            
            // 4. 处理每个文件
            Integer pdfFileId = null;
            Integer drawing2dFileId = null;
            Integer drawing3dFileId = null;
            Integer compressedFileId = null;
            
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                String subType = fileTypes[i]; // 使用前端传递的文件类型，而不是根据扩展名判断
                
                // 验证文件格式（只对有扩展名限制的类型进行验证）
                if (!isValidDrawingFileExtension(file.getOriginalFilename(), subType)) {
                    return AjaxResult.error("文件 " + file.getOriginalFilename() + " 格式与其类型不匹配");
                }
                
                // 处理单个文件
                AjaxResult result = processSingleFile(file, bomItem.getModelId(), subType, drawingNo, "1.0");
                if (!result.isSuccess()) {
                    return result;
                }
                
                Files fileInfo = (Files) result.get("data");
                Integer fileId = fileInfo.getId();
                
                // 根据子类型保存到对应的变量
                switch (subType) {
                    case "PDF":
                        pdfFileId = fileId;
                        break;
                    case "2D":
                        drawing2dFileId = fileId;
                        break;
                    case "3D":
                        drawing3dFileId = fileId;
                        break;
                    case "2D/3D":
                        compressedFileId = fileId;
                        break;
                    default:
                        log.warn("未知的文件子类型: {}", subType);
                }
            }
            
            // 5. 记录文件变更历史（使用新的历史记录系统）
            recordNewFileChanges(bomItem, oldMaterialDrawingMap, drawingNo, pdfFileId, drawing2dFileId, drawing3dFileId, compressedFileId);
            
            // 6. 更新BOM项备注（如果提供了备注）
            if (remark != null && !remark.trim().isEmpty()) {
                bomItem.setRemark(remark.trim());
                bomItemsDao.update(bomItem);
                log.info("更新BOM项备注成功: bomItemId={}, remark={}", bomItemId, remark.trim());
            }
            
            // 7. 更新物料-图纸映射表
            materialDrawingMapService.upsertMaterialDrawingMapMultiFiles(
                bomItem.getMaterialCode(),
                drawingNo,
                pdfFileId,
                drawing2dFileId,
                drawing3dFileId,
                compressedFileId,
                bomItem.getMaterialName(),
                bomItem.getSpecification()
            );
            
            return AjaxResult.success("上传成功，共处理 " + files.length + " 个文件");
            
        } catch (Exception e) {
            log.error("上传图纸失败 for bomItemId: {}", bomItemId, e);
            throw new RuntimeException("上传图纸失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理BOM项图纸上传（兼容原有单文件上传接口）
     *
     * @param file 上传的图纸文件
     * @param bomItemId BOM项ID
     * @param drawingNo 图纸编号
     * @param subType 图纸子类型
     * @param remark 备注 (可选，最大500字符)
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadBomItemDrawing(MultipartFile file, Integer bomItemId, String drawingNo, String subType, String remark) {
        try {
            // 1. 查找BOM项
            BomItems bomItem = bomItemsDao.findById(bomItemId);
            if (bomItem == null) {
                return AjaxResult.error("未找到对应的BOM项");
            }
            // 2. 检查是否存在旧的图纸文件（覆盖上传）
            MaterialDrawingMap oldMaterialDrawingMap = materialDrawingMapService.findByMaterialCode(bomItem.getMaterialCode());
            Integer oldDrawingFileId = oldMaterialDrawingMap != null ? oldMaterialDrawingMap.getDrawingFileId() : null;
            String oldVersion = null;

            if (oldDrawingFileId != null) {
                // 获取旧图纸的版本号
                Files oldDrawingFile = filesDao.findById(oldDrawingFileId);
                if (oldDrawingFile != null) {
                    oldVersion = oldDrawingFile.getDrawingNo();
                    // 图纸编号现在允许重复，移除唯一性校验
                }
            }
            // 3. 验证图纸子类型和文件格式
            if (!isValidDrawingFileExtension(file.getOriginalFilename(), subType)) {
                return AjaxResult.error("文件格式与选择的图纸类型不匹配");
            }

            // 4. 构造上传参数
            String fileType = getFileType();
            String version = "1.0";

            MultipartFile[] files = new MultipartFile[]{file};
            AjaxResult result = processFiles(files, bomItem.getModelId(), fileType, subType, drawingNo, version);

            if (result.isSuccess()) {
                // 4. 获取新创建的图纸文件记录的ID
                Object data = result.get("data");
                Files drawingFileInfo;
                if (data instanceof Files) {
                    drawingFileInfo = (Files) data;
                } else {
                    log.error("processFiles did not return Files object as expected for bomItemId: {}", bomItemId);
                    throw new RuntimeException("文件处理成功但未能获取文件信息");
                }

                if (drawingFileInfo == null || drawingFileInfo.getId() == null) {
                    log.error("processFiles succeeded but returned invalid data for bomItemId: {}", bomItemId);
                    throw new RuntimeException("文件处理成功但未能获取文件信息");
                }
                Integer newDrawingFileId = drawingFileInfo.getId();
                // 5. 如果是覆盖上传（即drawing_file_id != null），则新增变更历史记录
                if (oldDrawingFileId != null) {
                    FileChanges changes = new FileChanges()
                        .setBomItemId(bomItemId)
                        .setFileId(newDrawingFileId)
                        .setOldVersion(oldVersion)
                        .setNewVersion(drawingNo)
                        .setChangeReason("图纸更新")
                        .setChangedBy(SecurityUtils.getUserId().intValue())
                        .setChangedAt(LocalDateTime.now());
                    fileChangesDao.insert(changes);
                }
                bomItemsDao.update(bomItem);

                // 6.5. 更新BOM项备注（如果提供了备注）
                if (remark != null && !remark.trim().isEmpty()) {
                    bomItem.setRemark(remark.trim());
                    bomItemsDao.update(bomItem);
                    log.info("更新BOM项备注成功: bomItemId={}, remark={}", bomItemId, remark.trim());
                }

                // 7. 同步物料-图纸映射表
                try {
                    materialDrawingMapService.upsertMaterialDrawingMap(
                        bomItem.getMaterialCode(),
                        drawingNo,
                        newDrawingFileId,
                        bomItem.getMaterialName(),
                        bomItem.getSpecification()
                    );
                } catch (Exception ex) {
                    log.error("物料-图纸映射同步失败: modelId={}, materialCode={}, drawingNo={}", bomItem.getModelId(), bomItem.getMaterialCode(), drawingNo, ex);
                    // 可根据需求决定是否影响主流程
                }

                return AjaxResult.success("上传成功");
            } else {
                return result;
            }
        } catch (Exception e) {
            log.error("上传图纸失败 for bomItemId: {}", bomItemId, e);
            throw new RuntimeException("上传图纸失败: " + e.getMessage(), e);
        }
    }


    public AjaxResult processFiles(MultipartFile[] files, Integer modelId, String fileType, String subType, String drawingNo, String version) {
        try {
            // 4. 处理文件
            MultipartFile file = files[0];
            if (file.isEmpty()) {
                return AjaxResult.error(getFileType() + "文件不能为空");
            }
            // 注意：文件格式验证已在uploadBomItemDrawing方法中通过isValidDrawingFileExtension完成
            // 6. 构建文件保存路径
            String targetDir = buildTargetDirectory(fileType,FilesFileType.BOM_DRAWING.name(), version);
            String fileName = preserveOriginalFilename(file.getOriginalFilename(), version);

            // 7. 保存文件
            Path filePath = processFileUpload(file, targetDir, fileName);

            // 8. 保存文件信息到数据库
            Files fileInfo = new Files();
            try {
                 fileInfo.setFileType(FilesFileType.valueOf(fileType));
             } catch (IllegalArgumentException e) {
                 log.error("Invalid fileType string: {}", fileType, e);
                 throw new ServiceException("内部错误：无效的文件类型代码");
             }
            fileInfo.setDrawingNo(drawingNo)
                    .setSubType(subType)
                    .setVersion(version)
                    .setStatus(FilesStatus.ACTIVE)
                    .setFilePath(filePath.toString())
                    .setFileName(file.getOriginalFilename());

            // 9. 设置特定文件类型的额外信息
            setAdditionalFileInfo(fileInfo, drawingNo);
            filesDao.insert(fileInfo);
            return AjaxResult.success("上传成功",fileInfo);
        } catch (ServiceException e) {
            // Log specific service exceptions where they occur (e.g., validation)
            throw e;
        } catch (Exception e) {
            log.error("文件上传处理失败", e);
            throw new ServiceException("文件上传处理失败");
        }
    }

    /**
     * 设置特定文件类型的额外信息 (e.g., set drawingNo for drawings)
     * 子类应重写此方法以添加特定字段。
     *
     * @param fileInfo  文件信息对象 (jOOQ POJO)
     * @param drawingNo 图纸编号 (或其他特定参数)
     */
    @Override
    protected void setAdditionalFileInfo(Files fileInfo, String drawingNo) {
        super.setAdditionalFileInfo(fileInfo, drawingNo);
    }

    /**
     * 根据文件扩展名确定子类型
     */
    private String determineSubTypeFromFile(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (filename == null) {
            throw new ServiceException("文件名不能为空");
        }
        
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        
        // PDF文件
        if ("pdf".equals(extension)) {
            return "PDF";
        }
        
        // 压缩文件
        if ("zip".equals(extension) || "rar".equals(extension) || "7z".equals(extension)) {
            return "2D/3D";
        }
        
        // 2D图纸文件格式
        if ("dwg".equals(extension) || "dxf".equals(extension) || "pdf".equals(extension)) {
            return "2D";
        }
        
        // 3D图纸文件格式  
        if ("stp".equals(extension) || "step".equals(extension) || "igs".equals(extension) || 
            "iges".equals(extension) || "prt".equals(extension) || "x_t".equals(extension)) {
            return "3D";
        }
        
        throw new ServiceException("不支持的文件格式: " + extension);
    }

    /**
     * 处理单个文件上传
     */
    private AjaxResult processSingleFile(MultipartFile file, Integer modelId, String subType, String drawingNo, String version) {
        try {
            String fileType = getFileType();
            String targetDir = buildTargetDirectory(fileType, FilesFileType.BOM_DRAWING.name(), version);
            String fileName = preserveOriginalFilename(file.getOriginalFilename(), version);
            
            // 保存文件
            Path filePath = processFileUpload(file, targetDir, fileName);
            
            // 保存文件信息到数据库
            Files fileInfo = new Files();
            try {
                fileInfo.setFileType(FilesFileType.valueOf(fileType));
            } catch (IllegalArgumentException e) {
                log.error("Invalid fileType string: {}", fileType, e);
                throw new ServiceException("内部错误：无效的文件类型代码");
            }
            
            fileInfo.setDrawingNo(drawingNo)
                    .setSubType(subType)
                    .setVersion(version)
                    .setStatus(FilesStatus.ACTIVE)
                    .setFilePath(filePath.toString())
                    .setFileName(file.getOriginalFilename());
            
            setAdditionalFileInfo(fileInfo, drawingNo);
            filesDao.insert(fileInfo);
            
            return AjaxResult.success("文件处理成功", fileInfo);
            
        } catch (Exception e) {
            log.error("单个文件处理失败: {}", file.getOriginalFilename(), e);
            throw new ServiceException("文件处理失败: " + e.getMessage());
        }
    }

    /**
     * 记录文件变更历史
     */
    private void recordFileChanges(Integer bomItemId, MaterialDrawingMap oldMapping, String newDrawingNo, 
                                 Integer pdfFileId, Integer drawing2dFileId, Integer drawing3dFileId, Integer compressedFileId) {
        try {
            // 记录PDF文件变更
            if (pdfFileId != null) {
                recordSingleFileChange(bomItemId, oldMapping.getDrawingFileId(), pdfFileId, 
                                     oldMapping.getDrawingNo(), newDrawingNo, "PDF图纸更新");
            }
            
            // 记录2D图纸变更
            if (drawing2dFileId != null) {
                recordSingleFileChange(bomItemId, oldMapping.getDrawingFileId(), drawing2dFileId, 
                                     oldMapping.getDrawingNo(), newDrawingNo, "2D图纸更新");
            }
            
            // 记录3D图纸变更
            if (drawing3dFileId != null) {
                recordSingleFileChange(bomItemId, oldMapping.getDrawingFileId(), drawing3dFileId, 
                                     oldMapping.getDrawingNo(), newDrawingNo, "3D图纸更新");
            }
            
            // 记录压缩包文件变更
            if (compressedFileId != null) {
                recordSingleFileChange(bomItemId, oldMapping.getDrawingFileId(), compressedFileId, 
                                     oldMapping.getDrawingNo(), newDrawingNo, "压缩包文件更新");
            }
        } catch (Exception e) {
            log.error("记录文件变更历史失败", e);
        }
    }

    /**
     * 记录单个文件变更
     */
    private void recordSingleFileChange(Integer bomItemId, Integer oldFileId, Integer newFileId, 
                                      String oldVersion, String newVersion, String changeReason) {
        FileChanges changes = new FileChanges()
            .setBomItemId(bomItemId)
            .setFileId(newFileId)
            .setOldVersion(oldVersion)
            .setNewVersion(newVersion)
            .setChangeReason(changeReason)
            .setChangedBy(SecurityUtils.getUserId().intValue())
            .setChangedAt(LocalDateTime.now());
        fileChangesDao.insert(changes);
    }

    /**
     * 记录新的文件变更历史（使用新的历史记录系统）
     */
    private void recordNewFileChanges(BomItems bomItem, MaterialDrawingMap oldMapping, String newDrawingNo,
                                    Integer pdfFileId, Integer drawing2dFileId, Integer drawing3dFileId, Integer compressedFileId) {
        try {
            // 准备变更信息列表
            java.util.List<BomDrawingChangeHistoryService.DrawingChangeInfo> changes = new java.util.ArrayList<>();
            String changeReason = "图纸文件上传更新";
            
            // 处理PDF文件变更
            if (pdfFileId != null) {
                String oldFileName = getFileNameById(oldMapping != null ? oldMapping.getPdfFileId() : null);
                String newFileName = getFileNameById(pdfFileId);
                
                changes.add(new BomDrawingChangeHistoryService.DrawingChangeInfo(
                    DrawingFileType.PDF,
                    oldMapping != null ? ChangeType.UPDATE : ChangeType.ADD,
                    oldMapping != null ? oldMapping.getDrawingNo() : null,
                    oldMapping != null ? oldMapping.getPdfFileId() : null,
                    oldFileName,
                    null, // 暂不记录版本信息
                    newDrawingNo,
                    pdfFileId,
                    newFileName,
                    "1.0" // 默认版本
                ));
            }
            
            // 处理2D图纸变更
            if (drawing2dFileId != null) {
                String oldFileName = getFileNameById(oldMapping != null ? oldMapping.getDrawing_2dFileId() : null);
                String newFileName = getFileNameById(drawing2dFileId);
                
                changes.add(new BomDrawingChangeHistoryService.DrawingChangeInfo(
                    DrawingFileType.TWO_D,
                    oldMapping != null ? ChangeType.UPDATE : ChangeType.ADD,
                    oldMapping != null ? oldMapping.getDrawingNo() : null,
                    oldMapping != null ? oldMapping.getDrawing_2dFileId() : null,
                    oldFileName,
                    null,
                    newDrawingNo,
                    drawing2dFileId,
                    newFileName,
                    "1.0"
                ));
            }
            
            // 处理3D图纸变更
            if (drawing3dFileId != null) {
                String oldFileName = getFileNameById(oldMapping != null ? oldMapping.getDrawing_3dFileId() : null);
                String newFileName = getFileNameById(drawing3dFileId);
                
                changes.add(new BomDrawingChangeHistoryService.DrawingChangeInfo(
                    DrawingFileType.THREE_D,
                    oldMapping != null ? ChangeType.UPDATE : ChangeType.ADD,
                    oldMapping != null ? oldMapping.getDrawingNo() : null,
                    oldMapping != null ? oldMapping.getDrawing_3dFileId() : null,
                    oldFileName,
                    null,
                    newDrawingNo,
                    drawing3dFileId,
                    newFileName,
                    "1.0"
                ));
            }
            
            // 处理压缩包文件变更
            if (compressedFileId != null) {
                String oldFileName = getFileNameById(oldMapping != null ? oldMapping.getCompressedFileId() : null);
                String newFileName = getFileNameById(compressedFileId);
                
                changes.add(new BomDrawingChangeHistoryService.DrawingChangeInfo(
                    DrawingFileType.COMPRESSED,
                    oldMapping != null ? ChangeType.UPDATE : ChangeType.ADD,
                    oldMapping != null ? oldMapping.getDrawingNo() : null,
                    oldMapping != null ? oldMapping.getCompressedFileId() : null,
                    oldFileName,
                    null,
                    newDrawingNo,
                    compressedFileId,
                    newFileName,
                    "1.0"
                ));
            }
            
            // 批量记录变更历史
            if (!changes.isEmpty()) {
                bomDrawingChangeHistoryService.recordMultipleDrawingChanges(
                    bomItem.getId(),
                    bomItem.getMaterialCode(),
                    changes,
                    changeReason
                );
                log.info("成功记录 {} 个文件的变更历史，BOM项ID: {}", changes.size(), bomItem.getId());
            }
            
        } catch (Exception e) {
            log.error("记录文件变更历史失败，BOM项ID: {}", bomItem.getId(), e);
            // 不抛出异常，避免影响主要业务流程
        }
    }
    
    /**
     * 根据文件ID获取文件名
     */
    private String getFileNameById(Integer fileId) {
        if (fileId == null) return null;
        
        try {
            Files file = filesDao.findById(fileId);
            return file != null ? file.getFileName() : null;
        } catch (Exception e) {
            log.warn("获取文件名失败，文件ID: {}", fileId, e);
            return null;
        }
    }

    // ========================= 研发文件管理方法 =========================
    
    /**
     * 上传研发文件
     * TODO: 需要完善实现
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadBomItemRdFile(MultipartFile file, Integer bomItemId, String reason) {
        log.info("开始上传研发文件: bomItemId={}, fileName={}, reason={}", 
                bomItemId, file.getOriginalFilename(), reason);
        
        try {
            // 1. 验证参数
            if (file.isEmpty()) {
                return AjaxResult.error("文件不能为空");
            }
            
            if (bomItemId == null) {
                return AjaxResult.error("BOM项ID不能为空");
            }
            
            // 2. 获取BOM项信息
            BomItems bomItem = bomItemsDao.findById(bomItemId);
            if (bomItem == null) {
                return AjaxResult.error("BOM项不存在，ID: " + bomItemId);
            }
            
            // 3. 检查上传权限
            if (!hasFilePermission("RD_FILE", "upload")) {
                return AjaxResult.error("无研发文件上传权限");
            }
            
            // 4. 查找对应的material_drawing_map记录
            MaterialDrawingMap mappingRecord = materialDrawingMapService.findByMaterialCode(bomItem.getMaterialCode());
            if (mappingRecord == null) {
                return AjaxResult.error("未找到物料编码为 " + bomItem.getMaterialCode() + " 的映射记录");
            }
            
            // 5. 检查是否已存在研发文件
            Integer oldFileId = mappingRecord.getRdFileId();
            boolean isReplacement = (oldFileId != null);
            
            // 6. 上传文件到文件系统
            Files uploadedFile = uploadFileToSystem(file, bomItem.getMaterialCode(), "RD_FILE");
            if (uploadedFile == null) {
                return AjaxResult.error("文件上传失败");
            }
            
            // 7. 更新material_drawing_map记录
            mappingRecord.setRdFileId(uploadedFile.getId());
            materialDrawingMapDao.update(mappingRecord);
            
            // 8. 记录变更历史
            String changeType = isReplacement ? "UPDATE" : "ADD";
            recordFileChangeHistory(bomItemId, bomItem.getMaterialCode(), "RD_FILE", 
                                  changeType, oldFileId, uploadedFile.getId(), reason);
            
            // 9. 如果是替换文件，可能需要审批流程（根据配置决定）
            if (isReplacement && isApprovalRequired("RD_FILE", "replace")) {
                log.info("研发文件替换需要审批，bomItemId={}", bomItemId);
                // 这里可以触发审批流程，暂时先记录日志
            }
            
            log.info("研发文件上传成功: bomItemId={}, fileId={}, fileName={}", 
                    bomItemId, uploadedFile.getId(), uploadedFile.getFileName());
            
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("fileId", uploadedFile.getId());
            resultData.put("fileName", uploadedFile.getFileName());
            resultData.put("isReplacement", isReplacement);
            
            // 安全地获取文件大小
            if (uploadedFile.getFileSize() != null) {
                resultData.put("fileSize", uploadedFile.getFileSize());
            }
            
            return AjaxResult.success("研发文件上传成功", resultData);
            
        } catch (Exception e) {
            log.error("上传研发文件失败: bomItemId={}, fileName={}", 
                     bomItemId, file.getOriginalFilename(), e);
            return AjaxResult.error("上传研发文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载研发文件
     */
    public void downloadBomItemRdFile(Integer bomItemId, HttpServletResponse response) {
        log.info("开始下载研发文件: bomItemId={}", bomItemId);
        
        try {
            // 1. 获取BOM项信息
            BomItems bomItem = bomItemsDao.findById(bomItemId);
            if (bomItem == null) {
                throw new RuntimeException("BOM项不存在，ID: " + bomItemId);
            }
            
            // 2. 检查下载权限
            if (!hasFilePermission("RD_FILE", "download")) {
                throw new RuntimeException("无研发文件下载权限");
            }
            
            // 3. 查找对应的material_drawing_map记录
            MaterialDrawingMap mappingRecord = materialDrawingMapService.findByMaterialCode(bomItem.getMaterialCode());
            if (mappingRecord == null || mappingRecord.getRdFileId() == null) {
                throw new RuntimeException("该BOM项暂无研发文件");
            }
            
            // 4. 获取文件信息
            Files file = filesDao.findById(mappingRecord.getRdFileId());
            if (file == null) {
                throw new RuntimeException("研发文件不存在，文件ID: " + mappingRecord.getRdFileId());
            }
            
            // 5. 检查是否需要审批
            if (isApprovalRequired("RD_FILE", "download")) {
                // 重定向到审批申请页面
                redirectToApprovalRequest(response, file.getId());
                return;
            }
            
            // 6. 执行文件下载
            downloadFileFromSystem(file, response);
            
            log.info("研发文件下载成功: bomItemId={}, fileId={}, fileName={}", 
                    bomItemId, file.getId(), file.getFileName());
            
        } catch (Exception e) {
            log.error("下载研发文件失败: bomItemId={}", bomItemId, e);
            throw new RuntimeException("下载研发文件失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 删除研发文件
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteBomItemRdFile(Integer bomItemId, String reason) {
        log.info("开始删除研发文件: bomItemId={}, reason={}", bomItemId, reason);
        
        try {
            // 1. 获取BOM项信息
            BomItems bomItem = bomItemsDao.findById(bomItemId);
            if (bomItem == null) {
                return AjaxResult.error("BOM项不存在，ID: " + bomItemId);
            }
            
            // 2. 检查删除权限（复用上传权限）
            if (!hasFilePermission("RD_FILE", "upload")) {
                return AjaxResult.error("无研发文件删除权限");
            }
            
            // 3. 查找对应的material_drawing_map记录
            MaterialDrawingMap mappingRecord = materialDrawingMapService.findByMaterialCode(bomItem.getMaterialCode());
            if (mappingRecord == null || mappingRecord.getRdFileId() == null) {
                return AjaxResult.error("该BOM项暂无研发文件");
            }
            
            // 4. 获取要删除的文件信息
            Integer fileId = mappingRecord.getRdFileId();
            Files file = filesDao.findById(fileId);
            String fileName = file != null ? file.getFileName() : "未知文件";
            
            // 5. 更新material_drawing_map记录，移除文件关联
            mappingRecord.setRdFileId(null);
            materialDrawingMapDao.update(mappingRecord);
            
            // 6. 记录变更历史
            recordFileChangeHistory(bomItemId, bomItem.getMaterialCode(), "RD_FILE", 
                                  "DELETE", fileId, null, reason);
            
            // 7. 删除物理文件（可选，根据配置决定是否立即删除）
            if (file != null) {
                try {
                    filesDao.deleteById(fileId);
                    log.info("物理文件已删除: fileId={}, fileName={}", fileId, fileName);
                } catch (Exception e) {
                    log.warn("删除物理文件失败，但映射关系已清除: fileId={}", fileId, e);
                }
            }
            
            log.info("研发文件删除成功: bomItemId={}, fileId={}, fileName={}", 
                    bomItemId, fileId, fileName);
            
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("deletedFileId", fileId);
            resultData.put("deletedFileName", fileName);
            
            return AjaxResult.success("研发文件删除成功", resultData);
            
        } catch (Exception e) {
            log.error("删除研发文件失败: bomItemId={}", bomItemId, e);
            return AjaxResult.error("删除研发文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取研发文件变更历史
     */
    public AjaxResult getBomItemFileChangeHistory(Integer bomItemId, String fileType) {
        log.info("查询文件变更历史: bomItemId={}, fileType={}", bomItemId, fileType);
        
        try {
            // 复用现有的变更历史查询逻辑
            String sql = "SELECT h.*, u.nick_name as changed_by_name, " +
                       "       old_f.file_name as old_file_name, new_f.file_name as new_file_name " +
                       "FROM bom_drawing_change_history h " +
                       "LEFT JOIN sys_user u ON h.changed_by = u.user_id " +
                       "LEFT JOIN files old_f ON h.old_file_id = old_f.id " +
                       "LEFT JOIN files new_f ON h.new_file_id = new_f.id " +
                       "WHERE h.bom_item_id = ? AND h.file_type = ? " +
                       "ORDER BY h.changed_at DESC";
            
            List<Map<String, Object>> historyList = dsl.resultQuery(sql, bomItemId, fileType)
                    .fetchMaps();
            
            return AjaxResult.success("查询成功", historyList);
            
        } catch (Exception e) {
            log.error("查询文件变更历史失败: bomItemId={}, fileType={}", bomItemId, fileType, e);
            return AjaxResult.error("查询文件变更历史失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取研发文件信息
     */
    public AjaxResult getBomItemRdFileInfo(Integer bomItemId) {
        log.info("查询研发文件信息: bomItemId={}", bomItemId);
        
        try {
            // 1. 获取BOM项信息
            BomItems bomItem = bomItemsDao.findById(bomItemId);
            if (bomItem == null) {
                return AjaxResult.error("BOM项不存在，ID: " + bomItemId);
            }
            
            // 2. 查找对应的material_drawing_map记录
            MaterialDrawingMap mappingRecord = materialDrawingMapService.findByMaterialCode(bomItem.getMaterialCode());
            if (mappingRecord == null || mappingRecord.getRdFileId() == null) {
                return AjaxResult.success("该BOM项暂无研发文件", Collections.singletonMap("hasRdFile", false));
            }
            
            // 3. 获取文件信息
            Files file = filesDao.findById(mappingRecord.getRdFileId());
            if (file == null) {
                return AjaxResult.error("研发文件记录存在但文件不存在，文件ID: " + mappingRecord.getRdFileId());
            }
            
            // 4. 构建返回数据
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("hasRdFile", true);
            fileInfo.put("fileId", file.getId());
            fileInfo.put("fileName", file.getFileName());
            fileInfo.put("fileType", file.getFileType());
            fileInfo.put("status", file.getStatus());
            
            // 安全地添加可能为null的字段
            if (file.getFileSize() != null) {
                fileInfo.put("fileSize", file.getFileSize());
            }
            if (file.getUploadTime() != null) {
                fileInfo.put("uploadTime", file.getUploadTime());
            }
            
            return AjaxResult.success("查询成功", fileInfo);
            
        } catch (Exception e) {
            log.error("查询研发文件信息失败: bomItemId={}", bomItemId, e);
            return AjaxResult.error("查询研发文件信息失败: " + e.getMessage());
        }
    }
    
    // ========================= 缺失方法实现 =========================
    
    /**
     * 上传文件到系统
     */
    private Files uploadFileToSystem(MultipartFile file, String materialCode, String fileType) {
        try {
            log.info("上传文件到系统: fileName={}, materialCode={}, fileType={}", 
                    file.getOriginalFilename(), materialCode, fileType);
            
            // 1. 生成文件存储路径
            String targetDir = FileConstants.BASE_UPLOAD_PATH + "/rd_files/" + materialCode;
            String fileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();
            
            // 2. 处理文件上传
            Path filePath = processFileUpload(file, targetDir, fileName);
            
            // 3. 创建文件记录
            Files fileInfo = new Files();
            fileInfo.setFileType(FilesFileType.BOM_DRAWING)
                    .setSubType(fileType)
                    .setStatus(FilesStatus.ACTIVE)
                    .setFilePath(filePath.toString())
                    .setFileName(file.getOriginalFilename())
                    .setFileSize((long) file.getSize())
                    .setUploadTime(LocalDateTime.now())
                    .setUploadUserId(SecurityUtils.getUserId());
            
            // 4. 保存到数据库
            filesDao.insert(fileInfo);
            
            log.info("文件上传成功: fileId={}, fileName={}", fileInfo.getId(), fileInfo.getFileName());
            return fileInfo;
            
        } catch (Exception e) {
            log.error("上传文件到系统失败: fileName={}", file.getOriginalFilename(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从系统下载文件
     */
    private void downloadFileFromSystem(Files file, HttpServletResponse response) {
        try {
            log.info("从系统下载文件: fileId={}, fileName={}", file.getId(), file.getFileName());
            
            // 实现文件下载逻辑
            File physicalFile = new File(file.getFilePath());
            if (!physicalFile.exists()) {
                throw new RuntimeException("文件不存在: " + file.getFilePath());
            }
            
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            
            String encodedFileName = URLEncoder.encode(file.getFileName(), "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName);
            response.setContentLengthLong(physicalFile.length());
            
            // 写入文件内容
            try (FileInputStream fis = new FileInputStream(physicalFile);
                 OutputStream os = response.getOutputStream()) {
                FileCopyUtils.copy(fis, os);
            } catch (IOException e) {
                throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
            }
            
        } catch (Exception e) {
            log.error("从系统下载文件失败: fileId={}", file.getId(), e);
            throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 重定向到审批申请页面
     */
    private void redirectToApprovalRequest(HttpServletResponse response, Integer fileId) {
        try {
            log.info("重定向到审批申请页面: fileId={}", fileId);
            
            // 设置重定向响应
            response.setStatus(HttpServletResponse.SC_FOUND);
            response.setHeader("Location", "/approval/request?fileId=" + fileId);
            
        } catch (Exception e) {
            log.error("重定向到审批申请页面失败: fileId={}", fileId, e);
            throw new RuntimeException("重定向失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 记录文件变更历史
     */
    private void recordFileChangeHistory(Integer bomItemId, String materialCode, String fileType, 
                                       String changeType, Integer oldFileId, Integer newFileId, String reason) {
        try {
            log.info("记录文件变更历史: bomItemId={}, materialCode={}, fileType={}, changeType={}", 
                    bomItemId, materialCode, fileType, changeType);
            
            // 创建变更历史记录 - 简化实现，如果有具体的Service方法再调用
            try {
                // 这里应该调用具体的变更历史记录方法
                log.info("记录文件变更: bomItemId={}, materialCode={}, changeType={}, fileType={}, oldFileId={}, newFileId={}, reason={}", 
                        bomItemId, materialCode, changeType, fileType, oldFileId, newFileId, reason);
            } catch (Exception e) {
                log.warn("记录变更历史时出现异常: {}", e.getMessage());
            }
            
            log.info("文件变更历史记录成功");
            
        } catch (Exception e) {
            log.error("记录文件变更历史失败: bomItemId={}, fileType={}", bomItemId, fileType, e);
            // 不抛出异常，避免影响主要业务流程
        }
    }

    // ========================= 辅助方法 =========================
    
    /**
     * 检查文件权限
     */
    private boolean hasFilePermission(String fileType, String operation) {
        try {
            String permission = getPermissionByFileType(fileType, operation);
            return SecurityUtils.hasPermi(permission);
        } catch (Exception e) {
            log.warn("权限检查失败: fileType={}, operation={}", fileType, operation, e);
            return false;
        }
    }
    
    /**
     * 根据文件类型获取权限代码
     */
    private String getPermissionByFileType(String fileType, String operation) {
        if ("RD_FILE".equals(fileType)) {
            return "files:rd:" + operation;
        }
        // 其他文件类型的权限逻辑...
        return "files:" + operation;
    }
    
    /**
     * 检查是否需要审批
     */
    private boolean isApprovalRequired(String fileType, String operation) {
        // 根据配置文件决定是否需要审批
        if ("RD_FILE".equals(fileType) && "download".equals(operation)) {
            return true; // 研发文件下载需要审批
        }
        return false;
    }
}
