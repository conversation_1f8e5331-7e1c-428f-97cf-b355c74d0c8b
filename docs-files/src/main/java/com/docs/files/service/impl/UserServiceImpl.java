package com.docs.files.service.impl;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import org.jooq.DSLContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.docs.common.core.domain.entity.SysDept;
import com.docs.common.core.domain.entity.SysUser;
import com.docs.common.core.domain.entity.UserInfo;
import com.docs.files.service.UserService;
import com.docs.system.service.ISysDeptService;
import com.docs.system.service.ISysUserService;

import lombok.RequiredArgsConstructor;

/**
 * 用户相关服务实现类
 */
@Service("newUserService")
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    private final DSLContext dsl;
    private final ISysUserService sysUserService;
    private final ISysDeptService sysDeptService;
    
    @Override
    public Long findUserByPostCode(Long deptId, String postCode) {
        logger.info("根据岗位编码查找用户，部门ID: {}, 岗位编码: {}", deptId, postCode);
        
        if (deptId == null || postCode == null || postCode.isEmpty()) {
            return null;
        }
        
        // 使用原生SQL进行查询，jOOQ将处理参数绑定和结果映射
        // 查询特定部门中拥有特定岗位的用户
        String sql = ""
                + "SELECT u.user_id FROM sys_user u "
                + "JOIN sys_user_post up ON u.user_id = up.user_id "
                + "JOIN sys_post p ON up.post_id = p.post_id "
                + "WHERE u.dept_id = ? AND p.post_code = ? AND u.status = '0' "
                + "LIMIT 1";
        
        Long userId = dsl.resultQuery(sql, deptId, postCode)
                .fetchOneInto(Long.class);
        
        if (userId != null) {
            logger.info("已找到用户 {} 有岗位编码 {} 在部门 {}", userId, postCode, deptId);
            return userId;
        }
        
        // 不再向上级部门查找，每个部门必须有自己的主管/经理
        // 这确保了审批流程的组织结构清晰性
        
        logger.warn("在部门 {} 中未找到具有岗位编码 {} 的用户", deptId, postCode);
        return null;
    }

    @Override
    public Long findUserIdByUsername(String username) {
        logger.info("根据用户名查找用户ID: {}", username);
        
        if (username == null || username.isEmpty()) {
            return null;
        }
        
        // 使用SysUserService查询用户
        try {
            SysUser user = sysUserService.selectUserByUserName(username);
            if (user != null) {
                logger.info("找到用户名为 {} 的用户，ID为: {}", username, user.getUserId());
                return user.getUserId();
            }
        } catch (Exception e) {
            logger.error("查询用户名 {} 对应ID时出错", username, e);
        }
        
        logger.warn("未找到用户名为 {} 的用户", username);
        return null;
    }
    
    @Override
    public Long findApproverByRole(Long requesterId, String role) {
        logger.info("为申请人ID {} 查找{}角色", requesterId, role);
        
        // 1. 获取申请人所在部门
        SysUser user = sysUserService.selectUserById(requesterId);
        if (user == null) {
            logger.error("找不到申请人信息，申请人ID: {}", requesterId);
            throw new RuntimeException("找不到申请人信息");
        }
        
        Long deptId = user.getDeptId();
        if (deptId == null) {
            logger.error("申请人未分配部门，申请人ID: {}", requesterId);
            throw new RuntimeException("申请人未分配部门");
        }
        
        // 2. 根据部门和角色编码查找审批人
        Long approverId = findUserByPostCode(deptId, role);
        if (approverId != null) {
            return approverId;
        }
        
        // 3. 如果未找到具有对应角色的用户，抛出详细的异常信息
        logger.error("部门岗位配置错误：部门 {} 缺少 {} 岗位的用户", deptId, role);
        
        // 获取部门名称以提供更友好的错误信息
        String deptName = "未知部门";
        try {
            SysDept dept = sysDeptService.selectDeptById(deptId);
            if (dept != null) {
                deptName = dept.getDeptName();
            }
        } catch (Exception e) {
            logger.warn("获取部门名称失败", e);
        }
        
        String roleDesc = "supervisor".equals(role) ? "主管" : "manager".equals(role) ? "经理" : role;
        throw new RuntimeException(String.format("部门'%s'缺少%s岗位的用户，请联系管理员为该部门分配相应岗位人员", deptName, roleDesc));
    }
    
    @Override
    public UserInfo getUserInfoById(Long userId) {
        logger.info("获取用户基本信息，用户ID: {}", userId);
        
        if (userId == null) {
            return null;
        }
        
        try {
            // 使用系统服务查询用户信息
            SysUser user = sysUserService.selectUserById(userId);
            if (user == null) {
                logger.warn("未找到用户，用户ID: {}", userId);
                return null;
            }
            
            // 构建UserInfo对象
            UserInfo userInfo = UserInfo.builder()
                    .userId(user.getUserId())
                    .userName(user.getUserName())
                    .deptId(user.getDeptId())
                    .build();
            
            // 如果用户有部门，补充部门名称
            if (user.getDeptId() != null) {
                SysDept dept = sysDeptService.selectDeptById(user.getDeptId());
                if (dept != null) {
                    userInfo.setDeptName(dept.getDeptName());
                }
            }
            
            return userInfo;
        } catch (Exception e) {
            logger.error("获取用户信息出错，用户ID: {}", userId, e);
            return null;
        }
    }
    
    @Override
    public Map<Long, UserInfo> getUserInfoByIds(Collection<Long> userIds) {
        logger.info("批量获取用户信息，用户数量: {}", userIds != null ? userIds.size() : 0);
        
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }
        
        try {
            // 由于ISysUserService没有批量查询方法，我们循环单个查询
            Map<Long, UserInfo> result = new HashMap<>();
            Map<Long, SysDept> deptCache = new HashMap<>(); // 缓存已查询的部门信息
            
            for (Long userId : userIds) {
                if (userId == null) {
                    continue;
                }
                
                // 查询用户信息
                SysUser user = sysUserService.selectUserById(userId);
                if (user == null) {
                    logger.warn("未找到用户，用户ID: {}", userId);
                    continue;
                }
                
                // 构建UserInfo
                UserInfo userInfo = UserInfo.builder()
                        .userId(user.getUserId())
                        .userName(user.getUserName())
                        .deptId(user.getDeptId())
                        .build();
                
                // 如果用户有部门，且部门信息还没有缓存，则查询部门信息
                if (user.getDeptId() != null) {
                    SysDept dept;
                    if (deptCache.containsKey(user.getDeptId())) {
                        dept = deptCache.get(user.getDeptId());
                    } else {
                        dept = sysDeptService.selectDeptById(user.getDeptId());
                        if (dept != null) {
                            deptCache.put(dept.getDeptId(), dept);
                        }
                    }
                    
                    if (dept != null) {
                        userInfo.setDeptName(dept.getDeptName());
                    }
                }
                
                result.put(userId, userInfo);
            }
            
            return result;
        } catch (Exception e) {
            logger.error("批量获取用户信息出错", e);
            return new HashMap<>();
        }
    }
}
