package com.docs.files.service.processing.impl;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.files.service.processing.FileProcessingStrategy;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.util.FilePathUtil;
import com.docs.files.util.FileProcessorHelper;
import com.docs.files.service.strategy.validator.FileValidator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;

/**
 * 图纸文件处理策略
 */
@Component
@RequiredArgsConstructor
public class DrawingProcessingStrategy implements FileProcessingStrategy {
    private final FileValidator drawingFileValidator;
    private final FileProcessorHelper fileProcessorHelper;
    private final FilePathUtil filePathUtil;
    
    @Override
    public String getFileType() {
        return DRAWING_FILE_TYPE;
    }
    
    @Override
    public boolean validateRequest(FileUploadRequest request) {
        try {
            drawingFileValidator.validate(request);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public boolean validateFile(MultipartFile file) {
        return file != null && !file.isEmpty();
    }
    
    @Override
    public Files checkVersionExists(FileUploadRequest request) {
        return fileProcessorHelper.selectByDrawingVersionByNo(request.getDrawingNo(), request.getVersion());
    }
    
    @Override
    public String buildTargetDirectory( FileUploadRequest request) {
        return filePathUtil.buildTargetDirectory(getFileType(), request.getVersion());
    }
    
    @Override
    public AjaxResult postProcess(Files fileInfo, Path[] filePaths, ProductModels model, FileUploadRequest request) {
        // 图纸文件没有特殊的后处理逻辑
        return AjaxResult.success("图纸文件上传成功");
    }
}
