package com.docs.files.service;

import com.docs.common.jooq.generated.tables.pojos.ProductModels;

import java.util.List;

public interface ProductModelsService {
    
    /**
     * 查询机型列表
     */
    List<ProductModels> selectProductModelsList(ProductModels productModel);

    /**
     * 检查机型是否存在
     */
    boolean checkModelExists(String modelCode);

    /**
     * 新增机型
     */
    int insertProductModel(ProductModels productModel);

    /**
     * 根据机型代码查询机型信息
     */
    ProductModels selectByModelCode(String modelCode);

    ProductModels selectByPrimaryKey(Integer productModelId);
}
