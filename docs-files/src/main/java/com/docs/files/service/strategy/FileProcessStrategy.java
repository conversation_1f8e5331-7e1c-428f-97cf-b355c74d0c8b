package com.docs.files.service.strategy;

import com.docs.common.core.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件处理策略接口
 */
public interface FileProcessStrategy {
    /**
     * 获取支持的文件类型
     *
     * @return 文件类型
     */
    String getSupportedFileType();

    /**
     * 处理文件上传
     *
     * @param files 上传的文件数组
     * @param productModel 产品型号
     * @param fileType 文件类型
     * @param subType 子类型
     * @param drawingNo 图纸编号
     * @param version 版本号
     * @return 处理结果
     */
    AjaxResult processFiles(MultipartFile[] files, String productModel, String fileType,
                          String subType, String drawingNo, String version);
} 