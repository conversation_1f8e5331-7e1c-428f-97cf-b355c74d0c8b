package com.docs.files.service.strategy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 文件上传请求参数对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileUploadRequest {
    /**
     * 上传的文件数组
     */
    private MultipartFile[] files;
    
    /**
     * 产品型号
     */
    private String productModel;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 子类型
     */
    private String subType;
    
    /**
     * 图纸编号
     */
    private String drawingNo;
    
    /**
     * 版本号
     */
    private String version;

    /**
     * 文件名
     */
    @JsonIgnore
    private String fileName;
}
