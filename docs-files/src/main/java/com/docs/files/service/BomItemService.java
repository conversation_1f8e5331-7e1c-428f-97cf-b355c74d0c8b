package com.docs.files.service;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.entity.BomItem;
import com.docs.common.core.domain.vo.BomItemsVO;
import com.docs.common.jooq.generated.tables.pojos.BomItems;
import com.docs.common.jooq.generated.tables.pojos.Files;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;

/**
 * BOM数据服务接口
 */
public interface BomItemService {


    /**
     * 根据文件ID查询BOM数据
     */
    List<BomItemsVO> selectByFileId(Integer fileId);

    /**
     * 批量根据文件ID查询BOM数据
     *
     * @param fileIds 文件ID集合
     * @return BomItemsVO列表
     */
    List<BomItemsVO> selectByFileIds(Set<Integer> fileIds);

    /**
     * 查询BOM数据列表
     */
    List<BomItems> selectBomItemList(BomItem bomItem);

    /**
     * 从Excel文件解析BOM数据并保存
     * @param files 文件
     * @param filePath 文件路径
     * @param bomType 文件类型（电子BOM/结构BOM）
     * @return 解析结果
     */
    int parseBomExcelAndSave(Files files, String filePath, String bomType);

    /**
     * 更新BOM数据
     * @param bomItems BOM数据列表
     */
    void updateBomItems(List<BomItemsVO> bomItems);

    /**
     * 删除BOM数据
     * @param fileId 文件ID
     */
    void deleteBomitemsByFileId(Integer fileId);

    /**
     * 上传BOM项图纸
     *
     * @param file 图纸文件
     * @param bomItemId BOM项ID
     * @param drawingNo 图纸编号
     * @param subType 图纸子类型
     * @param remark 备注 (可选，最大500字符)
     * @return 操作结果
     */
    AjaxResult uploadDrawing(MultipartFile file, Integer bomItemId, String drawingNo, String subType, String remark);

    /**
     * 上传BOM项多文件图纸
     *
     * @param files 图纸文件数组
     * @param bomItemId BOM项ID
     * @param drawingNo 图纸编号
     * @param fileTypes 文件类型数组
     * @param remark 备注 (可选，最大500字符)
     * @return 操作结果
     */
    AjaxResult uploadMultipleDrawings(MultipartFile[] files, Integer bomItemId, String drawingNo, String[] fileTypes, String remark);

    // ========================= 研发文件管理接口 =========================
    
    /**
     * 上传研发文件
     *
     * @param file 研发文件
     * @param bomItemId BOM项ID
     * @param reason 上传原因 (可选)
     * @return 操作结果
     */
    AjaxResult uploadRdFile(MultipartFile file, Integer bomItemId, String reason);

    /**
     * 下载研发文件
     *
     * @param bomItemId BOM项ID
     * @param response HTTP响应对象
     */
    void downloadRdFile(Integer bomItemId, javax.servlet.http.HttpServletResponse response);

    /**
     * 删除研发文件
     *
     * @param bomItemId BOM项ID
     * @param reason 删除原因 (可选)
     * @return 操作结果
     */
    AjaxResult deleteRdFile(Integer bomItemId, String reason);

    /**
     * 获取研发文件变更历史
     *
     * @param bomItemId BOM项ID
     * @return 变更历史列表
     */
    AjaxResult getRdFileChangeHistory(Integer bomItemId);

    /**
     * 获取研发文件信息
     *
     * @param bomItemId BOM项ID
     * @return 研发文件信息
     */
    AjaxResult getRdFileInfo(Integer bomItemId);
}