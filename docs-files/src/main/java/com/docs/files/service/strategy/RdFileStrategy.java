package com.docs.files.service.strategy;

import com.docs.common.constant.FileConstants;
import com.docs.common.core.domain.AjaxResult;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.enums.FilesStatus;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 研发文件处理策略，处理研发文件的上传
 * 研发文件是独立于图纸的文件类型，支持所有文件格式
 */
@Slf4j
@Component
public class RdFileStrategy extends AbstractFileStrategy {

    private final String fileType = FilesFileType.RD_FILE.name();

    /**
     * 获取支持的文件类型
     *
     * @return 文件类型
     */
    @Override
    public String getSupportedFileType() {
        return fileType;
    }

    /**
     * 获取文件类型
     *
     * @return 文件类型
     */
    @Override
    public String getFileType() {
        return fileType;
    }

    /**
     * 验证文件数量
     *
     * @param files 上传的文件数组
     */
    @Override
    public void validateFileCount(MultipartFile[] files) {
        if (files == null || files.length != 1) {
            throw new ServiceException("请上传一个研发文件");
        }
    }

    /**
     * 验证文件格式
     * 研发文件支持所有文件格式，不做限制
     *
     * @param file 上传的文件
     */
    public void validateFileFormat(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }
        
        // 研发文件支持所有格式，只检查文件是否为空
        if (file.getSize() == 0) {
            throw new ServiceException("上传文件不能为空文件");
        }
        log.info("研发文件格式验证通过: {}, 大小: {}KB", 
                file.getOriginalFilename(), file.getSize() / 1024);
    }

    /**
     * 处理文件上传
     *
     * @param files        上传的文件数组
     * @param productModel 产品型号
     * @param fileType     文件类型
     * @param subType      子类型（研发文件可以为空）
     * @param drawingNo    图纸编号（研发文件不需要）
     * @param version      版本号
     * @return 处理结果
     */
    @Override
    public AjaxResult processFiles(MultipartFile[] files, String productModel, String fileType,
                                 String subType, String drawingNo, String version) {
        checkModelPermission(productModel);
        try {
            validateFileCount(files);
            MultipartFile file = files[0];
            validateFileFormat(file);

            ProductModels model = productModelsService.selectByModelCode(productModel);
            if (model == null) {
                throw new ServiceException("产品型号不存在");
            }

            // 构建文件路径
            String targetDir = buildTargetDirectory(fileType, subType, version);
            String fileName = preserveOriginalFilename(file.getOriginalFilename(), version);

            // 保存文件
            Path filePath = processFileUpload(file, targetDir, fileName);

            // 创建文件记录
            Files fileRecord = new Files();
            fileRecord.setProductModelId(model.getId());
            fileRecord.setFileType(FilesFileType.RD_FILE);
            fileRecord.setVersion(version);
            fileRecord.setStatus(FilesStatus.ACTIVE);
            fileRecord.setFilePath(filePath.toString());
            fileRecord.setFileName(file.getOriginalFilename());

            // 保存到数据库
            filesDao.insert(fileRecord);

            log.info("研发文件上传成功: 产品型号={}, 文件名={}, 版本={}",
                    model.getModelCode(), file.getOriginalFilename(), version);

            return AjaxResult.success("研发文件上传成功", fileRecord);

        } catch (ServiceException e) {
            log.error("研发文件上传失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("研发文件上传异常", e);
            throw new ServiceException("研发文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 重写目标目录构建方法，研发文件使用独立的目录结构
     */
    @Override
    protected String buildTargetDirectory(String fileType, String subType, String version) {
        // 研发文件使用独立的目录结构: rd-files/version
        String safeSubType = StringUtils.isEmpty(subType) ? "general" : subType;
        return Paths.get(FileConstants.BASE_UPLOAD_PATH, currentDate, "rd-files", safeSubType, "V" + version).toString();
    }
}
