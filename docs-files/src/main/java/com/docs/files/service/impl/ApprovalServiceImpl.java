package com.docs.files.service.impl;

import com.docs.common.constant.UserConstants;
import com.docs.common.enums.ApprovalActionType;
import com.docs.common.enums.ApprovalEvent;
import com.docs.common.enums.ApprovalStatus;
import com.docs.common.core.domain.entity.ApprovalActionRecordVO;
import com.docs.common.core.domain.entity.ApprovalRequestVO;
import com.docs.common.core.domain.entity.SysUser;
import com.docs.common.core.domain.entity.UserInfo;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.tables.records.ApprovalActionRecordsRecord;
import com.docs.common.jooq.generated.tables.records.ApprovalRequestDetailsRecord;
import com.docs.common.jooq.generated.tables.records.DownloadApprovalRequestsRecord;
import com.docs.files.service.ApprovalService;
import com.docs.files.service.UserService;
import com.docs.common.utils.JooqPaginationUtils;
import com.docs.common.core.page.TableDataInfo;
import com.docs.system.domain.SysPost;
import com.docs.system.service.ISysUserService;
import org.jooq.Condition;
import org.jooq.SelectConditionStep;

import lombok.RequiredArgsConstructor;

import static com.docs.common.jooq.generated.Tables.APPROVAL_ACTION_RECORDS;
import static com.docs.common.jooq.generated.Tables.APPROVAL_REQUEST_DETAILS;
import static com.docs.common.jooq.generated.Tables.DOWNLOAD_APPROVAL_REQUESTS;

import org.jooq.DSLContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineEventResult;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachinePersister;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ApprovalServiceImpl implements ApprovalService {

    private static final Logger logger = LoggerFactory.getLogger(ApprovalServiceImpl.class);

    private final DSLContext dsl;
    private final StateMachineFactory<ApprovalStatus, ApprovalEvent> stateMachineFactory;
    private final StateMachinePersister<ApprovalStatus, ApprovalEvent, String> persister;
    // 这些服务不再直接使用，而是通过UserService间接使用
    private final UserService userService;
    private final ISysUserService sysUserService;

    @Override
    @Transactional
    public Long submitRequest(Long fileId, Long requesterId, String reason) {
        logger.info("提交文件下载审批请求，文件ID: {}, 申请人ID: {}", fileId, requesterId);

        // 1. 验证参数
        Objects.requireNonNull(fileId, "文件ID不能为空");
        Objects.requireNonNull(requesterId, "申请人ID不能为空");
        if (reason == null || reason.trim().isEmpty()) {
            throw new IllegalArgumentException("申请理由不能为空");
        }

        // 2. 检查申请人的岗位，确定审批流程
        SysUser requester = sysUserService.selectUserById(requesterId);
        if (requester == null) {
            logger.error("未找到申请人信息，用户ID: {}", requesterId);
            throw new RuntimeException("未找到申请人信息，请联系系统管理员");
        }

        // 获取申请人的岗位信息
        boolean isRequesterSupervisor = false;
        try {
            String sql = "SELECT p.post_code FROM sys_post p " +
                         "JOIN sys_user_post up ON p.post_id = up.post_id " +
                         "WHERE up.user_id = ?";
            dsl.resultQuery(sql, requesterId)
               .fetch()
               .forEach(record -> {
                   String postCode = record.get("post_code", String.class);
                   if (UserConstants.POST_SUPERVISOR.equals(postCode)) {
                       // 使用反射或其他方式设置标志，这里简化处理
                   }
               });
        } catch (Exception e) {
            logger.error("获取申请人岗位信息失败", e);
        }

        // 检查申请人是否为主管
        List<Long> requesterPostIds = dsl.resultQuery(
            "SELECT up.post_id FROM sys_user_post up WHERE up.user_id = ?", requesterId)
            .fetch()
            .map(record -> record.get(0, Long.class));

        for (Long postId : requesterPostIds) {
            try {
                String postCode = dsl.resultQuery(
                    "SELECT post_code FROM sys_post WHERE post_id = ?", postId)
                    .fetchOne()
                    .get(0, String.class);
                if (UserConstants.POST_SUPERVISOR.equals(postCode)) {
                    isRequesterSupervisor = true;
                    break;
                }
            } catch (Exception e) {
                logger.warn("查询岗位信息失败，岗位ID: {}", postId);
            }
        }

        Long supervisorId = null;
        Long managerId = null;

        if (isRequesterSupervisor) {
            // 如果申请人是主管，直接找经理审批
            managerId = userService.findApproverByRole(requesterId, UserConstants.POST_MANAGER);
            if (managerId == null) {
                logger.error("无法确定主管申请人 {} 的经理", requesterId);
                throw new RuntimeException("无法确定申请人的经理，请联系系统管理员");
            }
        } else {
            // 如果申请人是普通员工，按原流程：先主管，再经理
            supervisorId = determineSupervisorId(requesterId);
            if (supervisorId == null) {
                logger.error("无法确定申请人 {} 的主管", requesterId);
                throw new RuntimeException("无法确定申请人的主管，请联系系统管理员");
            }
        }

        // 3. 创建审批请求记录
        DownloadApprovalRequestsRecord requestRecord = dsl.newRecord(DOWNLOAD_APPROVAL_REQUESTS);
        requestRecord.setFileId(fileId);
        requestRecord.setRequesterId(requesterId);
        requestRecord.setRequestReason(reason);

        // 根据申请人类型设置不同的初始状态和审批人
        if (isRequesterSupervisor) {
            // 主管申请：直接到经理审批
            requestRecord.setStatus(ApprovalStatus.PENDING_MANAGER_APPROVAL.name());
            requestRecord.setManagerId(managerId);
            // 主管申请时，supervisor_id 可以为空或设置为申请人自己
            requestRecord.setSupervisorId(null);
        } else {
            // 普通员工申请：先主管审批
            requestRecord.setStatus(ApprovalStatus.PENDING_SUPERVISOR_APPROVAL.name());
            requestRecord.setSupervisorId(supervisorId);
            // manager_id 将在主管审批通过后设置
        }

        requestRecord.setCreateTime(LocalDateTime.now());
        requestRecord.setUpdateTime(LocalDateTime.now());

        // 4. 插入数据库
        try {
            requestRecord.store();
            Long approvalRequestId = requestRecord.getId();
            logger.info("审批请求创建成功，ID: {}", approvalRequestId);

            // 5. 创建初始操作记录
            ApprovalStatus initialStatus = isRequesterSupervisor ?
                ApprovalStatus.PENDING_MANAGER_APPROVAL : ApprovalStatus.PENDING_SUPERVISOR_APPROVAL;
            createActionRecord(approvalRequestId, requesterId, ApprovalActionType.SUBMIT,
                               "提交文件下载申请: " + reason, null, initialStatus);

            // 6. 状态机集成
            StateMachine<ApprovalStatus, ApprovalEvent> stateMachine =
                stateMachineFactory.getStateMachine(approvalRequestId.toString());
            try {
                // 设置初始状态的上下文变量
                stateMachine.getExtendedState().getVariables().put("approvalRequestId", approvalRequestId);
                stateMachine.getExtendedState().getVariables().put("requesterId", requesterId);
                if (supervisorId != null) {
                    stateMachine.getExtendedState().getVariables().put("supervisorId", supervisorId);
                }
                if (managerId != null) {
                    stateMachine.getExtendedState().getVariables().put("managerId", managerId);
                }

                // 启动状态机
                stateMachine.startReactively().block(); // 确保启动完成

                // 如果是主管申请，需要手动设置状态为 PENDING_MANAGER_APPROVAL
                if (isRequesterSupervisor) {
                    // 由于状态机默认初始状态是 PENDING_SUPERVISOR_APPROVAL，
                    // 对于主管申请，我们需要手动设置状态
                    StateMachineContext<ApprovalStatus, ApprovalEvent> context =
                        new DefaultStateMachineContext<>(ApprovalStatus.PENDING_MANAGER_APPROVAL, null, null, null);
                    stateMachine.getStateMachineAccessor()
                        .doWithAllRegions(access -> access.resetStateMachine(context));
                }

                logger.info("状态机已启动，ID: {}, 初始状态: {}", approvalRequestId, stateMachine.getState().getId());

                // 持久化初始状态
                persister.persist(stateMachine, approvalRequestId.toString());
                logger.info("状态机初始状态已持久化，ID: {}", approvalRequestId);

            } catch (Exception e) {
                logger.error("状态机处理失败，审批请求ID: " + approvalRequestId, e);
                // 注意：这里可能需要回滚数据库中已创建的 requestRecord，如果事务配置允许
                throw new RuntimeException("初始化审批流程状态失败", e);
            }

            return approvalRequestId;

        } catch (Exception e) {
            // 此处的catch可能捕获 requestRecord.store() 或状态机部分的异常
            logger.error("创建审批请求或初始化状态机失败", e);
            throw new RuntimeException("创建审批请求失败: " + e.getMessage(), e);
        }
    }



    @Override
    @Transactional
    public void approve(Long approvalRequestId, Long actorId, String comment) {
        logger.info("处理审批批准操作，审批请求ID: {}, 操作人ID: {}", approvalRequestId, actorId);

        // 1. 验证参数
        Objects.requireNonNull(approvalRequestId, "审批请求ID不能为空");
        Objects.requireNonNull(actorId, "操作人ID不能为空");
        if (comment == null) {
            comment = ""; // 允许空评论，但转换为空字符串而非null
        }

        // 2. 获取审批请求记录
        DownloadApprovalRequestsRecord requestRecord = dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
                .fetchOne();

        if (requestRecord == null) {
            logger.error("未找到审批请求: {}", approvalRequestId);
            throw new IllegalArgumentException("未找到指定的审批请求: " + approvalRequestId);
        }

        // 3. 确定当前状态和需要发送的事件
        ApprovalStatus currentStatus = ApprovalStatus.valueOf(requestRecord.getStatus());
        ApprovalEvent approvalEvent;

        // 根据当前状态确定要发送的批准事件
        switch (currentStatus) {
            case PENDING_SUPERVISOR_APPROVAL:
                // 检查操作人是否是指定的主管
                if (!actorId.equals(requestRecord.getSupervisorId())) {
                    logger.error("操作人 {} 不是指定的主管 {}", actorId, requestRecord.getSupervisorId());
                    throw new IllegalStateException("只有指定的主管可以批准此请求");
                }
                approvalEvent = ApprovalEvent.SUPERVISOR_APPROVE;
                break;

            case PENDING_MANAGER_APPROVAL:
                // 检查操作人是否是指定的经理
                if (!actorId.equals(requestRecord.getManagerId())) {
                    logger.error("操作人 {} 不是指定的经理 {}", actorId, requestRecord.getManagerId());
                    throw new IllegalStateException("只有指定的经理可以批准此请求");
                }
                approvalEvent = ApprovalEvent.MANAGER_APPROVE;
                break;

            default:
                logger.error("当前状态 {} 不允许执行批准操作", currentStatus);
                throw new IllegalStateException("当前状态不允许执行批准操作: " + currentStatus);
        }

        logger.info("即将发送 {} 事件，当前状态: {}", approvalEvent, currentStatus);

        try {
            // 4. 恢复状态机
            StateMachine<ApprovalStatus, ApprovalEvent> stateMachine = restoreStateMachine(approvalRequestId);

            // 5. 构建事件消息并包含必要的头信息
            Message<ApprovalEvent> eventMessage = MessageBuilder
                .withPayload(approvalEvent)
                .setHeader("actorId", actorId)
                .setHeader("comment", comment)
                .setHeader("approvalRequestId", approvalRequestId)
                .build();

            // 6. 发送事件 - 使用响应式API
            // 注意：这里使用block()确保操作完成后再继续
            StateMachineEventResult<ApprovalStatus, ApprovalEvent> result = stateMachine
                .sendEvent(Mono.just(eventMessage))
                .blockFirst();

            if (result.getResultType() != StateMachineEventResult.ResultType.ACCEPTED) {
                logger.error("状态机拒绝了事件: {}, 原因: {}", approvalEvent, result);
                throw new IllegalStateException("状态机拒绝了批准操作，可能是因为当前状态下不允许此操作");
            }

            // 7. 持久化更新后的状态机状态
            persister.persist(stateMachine, approvalRequestId.toString());
            logger.info("批准操作成功，状态机已从 {} 转换到 {}", currentStatus, stateMachine.getState().getId());

            // 注：实际的数据库更新（状态、manager_id等）和操作记录由状态机动作处理
            // 参见 SupervisorApproveAction 和 ManagerApproveAction 类

        } catch (Exception e) {
            logger.error("处理批准操作时发生错误，审批请求ID: " + approvalRequestId, e);
            throw new RuntimeException("处理批准操作失败: " + e.getMessage(), e);
        }
    }


    @Override
    @Transactional
    public void reject(Long approvalRequestId, Long actorId, String comment) {
        logger.info("处理审批拒绝操作，审批请求ID: {}, 操作人ID: {}", approvalRequestId, actorId);

        // 1. 验证参数
        Objects.requireNonNull(approvalRequestId, "审批请求ID不能为空");
        Objects.requireNonNull(actorId, "操作人ID不能为空");
        if (comment == null) {
            comment = ""; // 允许空评论，但转换为空字符串
        }

        // 2. 获取审批请求记录
        DownloadApprovalRequestsRecord requestRecord = dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
                .fetchOne();

        if (requestRecord == null) {
            logger.error("未找到审批请求: {}", approvalRequestId);
            throw new IllegalArgumentException("未找到指定的审批请求: " + approvalRequestId);
        }

        // 3. 确定当前状态和需要发送的事件
        ApprovalStatus currentStatus = ApprovalStatus.valueOf(requestRecord.getStatus());
        ApprovalEvent rejectEvent;

        // 根据当前状态确定要发送的拒绝事件
        switch (currentStatus) {
            case PENDING_SUPERVISOR_APPROVAL:
                // 检查操作人是否是指定的主管
                if (!actorId.equals(requestRecord.getSupervisorId())) {
                    logger.error("操作人 {} 不是指定的主管 {}", actorId, requestRecord.getSupervisorId());
                    throw new IllegalStateException("只有指定的主管可以拒绝此请求");
                }
                rejectEvent = ApprovalEvent.SUPERVISOR_REJECT;
                break;

            case PENDING_MANAGER_APPROVAL:
                // 检查操作人是否是指定的经理
                if (!actorId.equals(requestRecord.getManagerId())) {
                    logger.error("操作人 {} 不是指定的经理 {}", actorId, requestRecord.getManagerId());
                    throw new IllegalStateException("只有指定的经理可以拒绝此请求");
                }
                rejectEvent = ApprovalEvent.MANAGER_REJECT;
                break;

            default:
                logger.error("当前状态 {} 不允许执行拒绝操作", currentStatus);
                throw new IllegalStateException("当前状态不允许执行拒绝操作: " + currentStatus);
        }

        logger.info("即将发送 {} 事件，当前状态: {}", rejectEvent, currentStatus);

        try {
            // 4. 恢复状态机
            StateMachine<ApprovalStatus, ApprovalEvent> stateMachine = restoreStateMachine(approvalRequestId);

            // 5. 构建事件消息并包含必要的头信息
            Message<ApprovalEvent> eventMessage = MessageBuilder
                .withPayload(rejectEvent)
                .setHeader("actorId", actorId)
                .setHeader("comment", comment)
                .setHeader("approvalRequestId", approvalRequestId)
                .build();

            // 6. 发送事件 - 使用响应式API
            StateMachineEventResult<ApprovalStatus, ApprovalEvent> result = stateMachine
                .sendEvent(Mono.just(eventMessage))
                .blockFirst();

            if (result.getResultType() != StateMachineEventResult.ResultType.ACCEPTED) {
                logger.error("状态机拒绝了事件: {}, 原因: {}", rejectEvent, result);
                throw new IllegalStateException("状态机拒绝了拒绝操作，可能是因为当前状态下不允许此操作");
            }

            // 7. 持久化更新后的状态机状态
            persister.persist(stateMachine, approvalRequestId.toString());
            logger.info("拒绝操作成功，状态机已从 {} 转换到 {}", currentStatus, stateMachine.getState().getId());

            // 注：实际的数据库更新（状态等）和操作记录由状态机动作处理
            // 参见 SupervisorRejectAction 和 ManagerRejectAction 类

        } catch (Exception e) {
            logger.error("处理拒绝操作时发生错误，审批请求ID: " + approvalRequestId, e);
            throw new RuntimeException("处理拒绝操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void cancelRequest(Long approvalRequestId, Long actorId) {
        logger.info("处理撤销申请操作，审批请求ID: {}, 操作人ID: {}", approvalRequestId, actorId);

        // 1. 验证参数
        Objects.requireNonNull(approvalRequestId, "审批请求ID不能为空");
        Objects.requireNonNull(actorId, "操作人ID不能为空");

        // 2. 获取审批请求记录
        DownloadApprovalRequestsRecord requestRecord = dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
                .fetchOne();

        if (requestRecord == null) {
            logger.error("未找到审批请求: {}", approvalRequestId);
            throw new IllegalArgumentException("未找到指定的审批请求: " + approvalRequestId);
        }

        // 3. 检查操作人是否是申请人自己
        if (!actorId.equals(requestRecord.getRequesterId())) {
            logger.error("操作人 {} 不是申请人 {}", actorId, requestRecord.getRequesterId());
            throw new IllegalStateException("只有申请人自己可以撤销申请");
        }

        // 4. 确定当前状态是否可撤销
        ApprovalStatus currentStatus = ApprovalStatus.valueOf(requestRecord.getStatus());

        // 只有在审批中的请求才能撤销，已经最终处理的请求不能撤销
        if (currentStatus != ApprovalStatus.PENDING_SUPERVISOR_APPROVAL &&
            currentStatus != ApprovalStatus.PENDING_MANAGER_APPROVAL) {
            logger.error("当前状态 {} 不允许撤销", currentStatus);
            throw new IllegalStateException("当前状态不允许撤销申请: " + currentStatus);
        }

        logger.info("即将发送 {} 事件，当前状态: {}", ApprovalEvent.CANCEL_BY_REQUESTER, currentStatus);

        try {
            // 5. 恢复状态机
            StateMachine<ApprovalStatus, ApprovalEvent> stateMachine = restoreStateMachine(approvalRequestId);

            // 6. 构建事件消息
            Message<ApprovalEvent> eventMessage = MessageBuilder
                .withPayload(ApprovalEvent.CANCEL_BY_REQUESTER)
                .setHeader("actorId", actorId)
                .setHeader("comment", "申请人撤销申请")
                .setHeader("approvalRequestId", approvalRequestId)
                .build();

            // 7. 发送事件
            StateMachineEventResult<ApprovalStatus, ApprovalEvent> result = stateMachine
                .sendEvent(Mono.just(eventMessage))
                .blockFirst();

            if (result.getResultType() != StateMachineEventResult.ResultType.ACCEPTED) {
                logger.error("状态机拒绝了事件: {}, 原因: {}", ApprovalEvent.CANCEL_BY_REQUESTER, result);
                throw new IllegalStateException("状态机拒绝了撤销操作，可能是因为当前状态不允许此操作");
            }

            // 8. 持久化更新后的状态机状态
            persister.persist(stateMachine, approvalRequestId.toString());
            logger.info("撤销操作成功，状态机已从 {} 转换到 {}", currentStatus, stateMachine.getState().getId());

            // 注：实际的数据库更新（状态等）和操作记录由状态机动作处理
            // 参见 CancelRequestAction 类

        } catch (Exception e) {
            logger.error("处理撤销操作时发生错误，审批请求ID: " + approvalRequestId, e);
            throw new RuntimeException("处理撤销操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ApprovalRequestVO getApprovalRequestDetails(Long approvalRequestId) {
        logger.info("获取审批请求详情，ID: {}", approvalRequestId);

        // 1. 从视图中查询审批请求详细信息
        ApprovalRequestDetailsRecord detailsRecord = dsl.selectFrom(APPROVAL_REQUEST_DETAILS)
                .where(APPROVAL_REQUEST_DETAILS.ID.eq(approvalRequestId))
                .fetchOne();

        if (detailsRecord == null) {
            logger.error("未找到审批请求记录: {}", approvalRequestId);
            return null;
        }

        // 2. 创建 VO 对象并填充基本信息
        ApprovalRequestVO vo = ApprovalRequestVO.builder()
                .id(detailsRecord.getId())
                .fileId(detailsRecord.getFileId())
                .fileName(detailsRecord.getFileName())
                .filePath(detailsRecord.getFilePath())
                .requesterId(detailsRecord.getRequesterId())
                .requesterName(detailsRecord.getRequesterName())
                .requesterNickName(detailsRecord.getRequesterNickName())
                .requesterDepartment(detailsRecord.getRequesterDeptName())
                .requestReason(detailsRecord.getRequestReason())
                .status(ApprovalStatus.valueOf(detailsRecord.getStatus()))
                .statusDesc(ApprovalStatus.valueOf(detailsRecord.getStatus()).getDisplayName())
                .supervisorId(detailsRecord.getSupervisorId())
                .supervisorName(detailsRecord.getSupervisorName())
                .supervisorNickName(detailsRecord.getSupervisorNickName())
                .supervisorDepartment(detailsRecord.getSupervisorDeptName())
                .managerId(detailsRecord.getManagerId())
                .managerName(detailsRecord.getManagerName())
                .managerNickName(detailsRecord.getManagerNickName())
                .managerDepartment(detailsRecord.getManagerDeptName())
                .createTime(detailsRecord.getCreateTime())
                .updateTime(detailsRecord.getUpdateTime())
                .build();

        // 3. 查询审批操作记录
        List<ApprovalActionRecordsRecord> actionRecords = dsl.selectFrom(APPROVAL_ACTION_RECORDS)
                .where(APPROVAL_ACTION_RECORDS.APPROVAL_REQUEST_ID.eq(approvalRequestId))
                .orderBy(APPROVAL_ACTION_RECORDS.ACTION_TIME.asc())
                .fetch();

        // 4. 收集所有操作人ID
        Set<Long> actorIds = actionRecords.stream()
                .map(ApprovalActionRecordsRecord::getActorId)
                .collect(Collectors.toSet());

        // 5. 批量查询操作人信息
        Map<Long, UserInfo> actorInfoMap = userService.getUserInfoByIds(actorIds);

        // 6. 将操作记录转换为 VO 并添加到列表
        List<ApprovalActionRecordVO> actionRecordVOs = new ArrayList<>();

        for (ApprovalActionRecordsRecord record : actionRecords) {
            UserInfo actorInfo = actorInfoMap.get(record.getActorId());

            ApprovalActionType actionType = ApprovalActionType.valueOf(record.getActionType());

            ApprovalActionRecordVO actionVO = ApprovalActionRecordVO.builder()
                    .id(record.getId())
                    .approvalRequestId(record.getApprovalRequestId())
                    .actorId(record.getActorId())
                    .actorName(actorInfo != null ? actorInfo.getUserName() : null)
                    .actorDepartment(actorInfo != null ? actorInfo.getDeptName() : null)
                    .actionType(actionType)
                    .actionTypeDesc(actionType.getDisplayName())
                    .actionTypeIcon(actionType.getIconType())
                    .actionTypeColor(actionType.getColorType())
                    .actionTypeDescription(actionType.getDescription())
                    .comment(record.getComment())
                    .actionTime(record.getActionTime())
                    .build();

            if (record.getPreviousStatus() != null) {
                ApprovalStatus prevStatus = ApprovalStatus.valueOf(record.getPreviousStatus());
                actionVO.setPreviousStatus(prevStatus);
                actionVO.setPreviousStatusDesc(prevStatus.getDisplayName());
            }

            if (record.getNextStatus() != null) {
                ApprovalStatus nextStatus = ApprovalStatus.valueOf(record.getNextStatus());
                actionVO.setNextStatus(nextStatus);
                actionVO.setNextStatusDesc(nextStatus.getDisplayName());
            }

            actionRecordVOs.add(actionVO);
        }

        vo.setActionRecords(actionRecordVOs);

        logger.info("审批请求详情获取成功，ID: {}, 操作记录数: {}", approvalRequestId, actionRecordVOs.size());
        return vo;
    }

    @Override
    public TableDataInfo listPendingApprovalsByApprover(Long approverId, int pageNum, int pageSize) {
        logger.info("查询审批人 {} 的待处理审批请求", approverId);

        Objects.requireNonNull(approverId, "审批人ID不能为空");

        // 使用分页工具类获取数据
        TableDataInfo tableData = JooqPaginationUtils.getDataTable(
            dsl,
            dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                .where(
                    // 主管角色的待处理请求
                    DOWNLOAD_APPROVAL_REQUESTS.SUPERVISOR_ID.eq(approverId)
                    .and(DOWNLOAD_APPROVAL_REQUESTS.STATUS.eq(ApprovalStatus.PENDING_SUPERVISOR_APPROVAL.name()))
                    .or(
                    // 经理角色的待处理请求
                    DOWNLOAD_APPROVAL_REQUESTS.MANAGER_ID.eq(approverId)
                    .and(DOWNLOAD_APPROVAL_REQUESTS.STATUS.eq(ApprovalStatus.PENDING_MANAGER_APPROVAL.name()))
                    )
                )
                .orderBy(DOWNLOAD_APPROVAL_REQUESTS.CREATE_TIME.desc()),
            pageNum,
            pageSize
        );

        @SuppressWarnings("unchecked")
        List<DownloadApprovalRequestsRecord> pendingRequests = (List<DownloadApprovalRequestsRecord>) tableData.getRows();

        if (pendingRequests.isEmpty()) {
            logger.info("未找到审批人 {} 的待处理请求", approverId);
            return tableData; // 返回空列表，而不是抛出异常
        }

        // 2. 收集需要查询的用户ID
        Set<Long> userIds = new HashSet<>();
        Set<Long> fileIds = new HashSet<>();

        for (DownloadApprovalRequestsRecord request : pendingRequests) {
            // 添加申请人ID
            userIds.add(request.getRequesterId());

            // 添加主管ID（如果存在）
            if (request.getSupervisorId() != null) {
                userIds.add(request.getSupervisorId());
            }

            // 添加经理ID（如果存在）
            if (request.getManagerId() != null) {
                userIds.add(request.getManagerId());
            }

            // 添加文件ID
            fileIds.add(request.getFileId());
        }

        // 3. 批量获取用户信息
        Map<Long, UserInfo> userInfoMap = userService.getUserInfoByIds(userIds);

        // 4. 批量获取文件信息（先使用原生 SQL 获取文件名）
        Map<Long, String> fileNameMap = new HashMap<>();
        if (!fileIds.isEmpty()) {
            String sql = "SELECT id as file_id, file_name FROM files WHERE id IN (" +
                    fileIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")";

            try {
                dsl.resultQuery(sql)
                   .fetch()
                   .forEach(record -> {
                       Long fileId = record.get(0, Long.class);
                       String fileName = record.get(1, String.class);
                       fileNameMap.put(fileId, fileName);
                   });
            } catch (Exception e) {
                logger.error("获取文件信息失败", e);
            }
        }

        // 5. 构建返回结果
        List<ApprovalRequestVO> result = new ArrayList<>();
        for (DownloadApprovalRequestsRecord record : pendingRequests) {
            // 获取用户信息
            UserInfo requesterInfo = userInfoMap.get(record.getRequesterId());
            UserInfo supervisorInfo = record.getSupervisorId() != null ? userInfoMap.get(record.getSupervisorId()) : null;
            UserInfo managerInfo = record.getManagerId() != null ? userInfoMap.get(record.getManagerId()) : null;

            // 获取文件名称
            String fileName = fileNameMap.getOrDefault(record.getFileId(), "未知文件");

            // 状态转换
            ApprovalStatus status = ApprovalStatus.valueOf(record.getStatus());

            // 构建 VO 对象
            ApprovalRequestVO vo = ApprovalRequestVO.builder()
                    .id(record.getId())
                    .fileId(record.getFileId())
                    .fileName(fileName)
                    .requesterId(record.getRequesterId())
                    .requesterName(requesterInfo != null ? requesterInfo.getUserName() : null)
                    // 由于 UserInfo 中没有 getNickName 方法，暂时使用 userName 作为 nickName
                    .requesterNickName(requesterInfo != null ? requesterInfo.getUserName() : null)
                    .requesterDepartment(requesterInfo != null ? requesterInfo.getDeptName() : null)
                    .requestReason(record.getRequestReason())
                    .status(status)
                    .statusDesc(status.getDisplayName())
                    .supervisorId(record.getSupervisorId())
                    .supervisorName(supervisorInfo != null ? supervisorInfo.getUserName() : null)
                    .supervisorNickName(supervisorInfo != null ? supervisorInfo.getUserName() : null)
                    .supervisorDepartment(supervisorInfo != null ? supervisorInfo.getDeptName() : null)
                    .managerId(record.getManagerId())
                    .managerName(managerInfo != null ? managerInfo.getUserName() : null)
                    .managerNickName(managerInfo != null ? managerInfo.getUserName() : null)
                    .managerDepartment(managerInfo != null ? managerInfo.getDeptName() : null)
                    .createTime(record.getCreateTime())
                    .updateTime(record.getUpdateTime())
                    .build();

            result.add(vo);
        }

        // 将转换后的VO对象列表设置回tableData
        tableData.setRows(result);

        logger.info("查询审批人 {} 的待处理审批请求成功，共 {} 条记录", approverId, result.size());
        return tableData;
    }

    @Override
    public TableDataInfo listUserRequestsByRequester(Long requesterId, int pageNum, int pageSize) {
        logger.info("查询用户{}的审批请求，页码:{}，每页数量:{}", requesterId, pageNum, pageSize);

        Objects.requireNonNull(requesterId, "申请人ID不能为空");

        // 使用分页工具类获取数据
        TableDataInfo tableData = JooqPaginationUtils.getDataTable(
            dsl,
            dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                .where(DOWNLOAD_APPROVAL_REQUESTS.REQUESTER_ID.eq(requesterId))
                .orderBy(DOWNLOAD_APPROVAL_REQUESTS.CREATE_TIME.desc()),
            pageNum,
            pageSize
        );

        @SuppressWarnings("unchecked")
        List<DownloadApprovalRequestsRecord> records = (List<DownloadApprovalRequestsRecord>) tableData.getRows();

        if (records.isEmpty()) {
            logger.info("未找到申请人 {} 的申请记录", requesterId);
            throw new ServiceException("未找到申请人 " + requesterId + " 的申请记录");
        }

        // 收集需要查询的用户ID和文件ID
        Set<Long> userIds = new HashSet<>();
        Set<Long> fileIds = new HashSet<>();

        for (DownloadApprovalRequestsRecord record : records) {
            // 添加申请人ID
            userIds.add(record.getRequesterId());

            // 添加主管ID（如果存在）
            if (record.getSupervisorId() != null) {
                userIds.add(record.getSupervisorId());
            }

            // 添加经理ID（如果存在）
            if (record.getManagerId() != null) {
                userIds.add(record.getManagerId());
            }

            // 添加文件ID
            fileIds.add(record.getFileId());
        }

        // 批量获取用户信息
        Map<Long, UserInfo> userInfoMap = userService.getUserInfoByIds(userIds);

        // 批量获取文件信息
        Map<Long, String> fileNameMap = new HashMap<>();
        String sql = "SELECT id as file_id, file_name FROM files WHERE id IN (" +
                fileIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")";

        try {
            dsl.resultQuery(sql)
               .fetch()
               .forEach(record -> {
                   Long fileId = record.get(0, Long.class);
                   String fileName = record.get(1, String.class);
                   fileNameMap.put(fileId, fileName);
               });
        } catch (Exception e) {
            logger.error("获取文件信息失败", e);
        }

        // 构建返回结果
        List<ApprovalRequestVO> result = new ArrayList<>();
        for (DownloadApprovalRequestsRecord record : records) {
            // 获取用户信息
            UserInfo requesterInfo = userInfoMap.get(record.getRequesterId());
            UserInfo supervisorInfo = record.getSupervisorId() != null ? userInfoMap.get(record.getSupervisorId()) : null;
            UserInfo managerInfo = record.getManagerId() != null ? userInfoMap.get(record.getManagerId()) : null;

            // 获取文件名称
            String fileName = fileNameMap.getOrDefault(record.getFileId(), "未知文件");

            // 状态转换
            ApprovalStatus status = ApprovalStatus.valueOf(record.getStatus());

            // 构建 VO 对象
            ApprovalRequestVO vo = ApprovalRequestVO.builder()
                    .id(record.getId())
                    .fileId(record.getFileId())
                    .fileName(fileName)
                    .requesterId(record.getRequesterId())
                    .requesterName(requesterInfo != null ? requesterInfo.getUserName() : null)
                    // 由于 UserInfo 中没有 getNickName 方法，暂时使用 userName 作为 nickName
                    .requesterNickName(requesterInfo != null ? requesterInfo.getUserName() : null)
                    .requesterDepartment(requesterInfo != null ? requesterInfo.getDeptName() : null)
                    .requestReason(record.getRequestReason())
                    .status(status)
                    .statusDesc(status.getDisplayName())
                    .supervisorId(record.getSupervisorId())
                    .supervisorName(supervisorInfo != null ? supervisorInfo.getUserName() : null)
                    .supervisorNickName(supervisorInfo != null ? supervisorInfo.getUserName() : null)
                    .supervisorDepartment(supervisorInfo != null ? supervisorInfo.getDeptName() : null)
                    .managerId(record.getManagerId())
                    .managerName(managerInfo != null ? managerInfo.getUserName() : null)
                    .managerNickName(managerInfo != null ? managerInfo.getUserName() : null)
                    .managerDepartment(managerInfo != null ? managerInfo.getDeptName() : null)
                    .createTime(record.getCreateTime())
                    .updateTime(record.getUpdateTime())
                    .build();

            result.add(vo);
        }

        // 将丰富的VO对象列表设置到分页数据对象中
        tableData.setRows(result);

        logger.info("查询申请人 {} 的审批请求成功，共 {} 条记录", requesterId, result.size());
        return tableData;
    }



    @Override
    public TableDataInfo listCompletedApprovals(Long userId, int pageNum, int pageSize, String fileName, String requesterName, String status) {
        logger.info("查询已完成的审批记录，用户ID: {}, 页码: {}, 每页数量: {}, 文件名: {}, 申请人: {}, 状态: {}",
                userId, pageNum, pageSize, fileName, requesterName, status);

        Objects.requireNonNull(userId, "用户ID不能为空");

        // 获取用户信息，判断用户角色
        SysUser user = sysUserService.selectUserById(userId);
        if (user == null) {
            logger.error("未找到用户信息，用户ID: {}", userId);
            throw new ServiceException("未找到用户信息");
        }

        // 判断用户是否为管理员
        boolean isAdmin = SysUser.isAdmin(userId);

        // 判断用户是否为主管或经理
        boolean isSupervisorOrManager = false;
        // 获取用户岗位列表
        List<SysPost> posts = new ArrayList<>();
        try {
            // 使用原生SQL查询用户岗位
            String sql = "SELECT p.* FROM sys_post p " +
                         "JOIN sys_user_post up ON p.post_id = up.post_id " +
                         "WHERE up.user_id = ?";
            dsl.resultQuery(sql, userId)
               .fetch()
               .forEach(record -> {
                   SysPost post = new SysPost();
                   post.setPostId(record.get("post_id", Long.class));
                   post.setPostCode(record.get("post_code", String.class));
                   post.setPostName(record.get("post_name", String.class));
                   posts.add(post);
               });
        } catch (Exception e) {
            logger.error("获取用户岗位信息失败", e);
        }

        for (SysPost post : posts) {
            if (UserConstants.POST_SUPERVISOR.equals(post.getPostCode()) ||
                UserConstants.POST_MANAGER.equals(post.getPostCode())) {
                isSupervisorOrManager = true;
                break;
            }
        }

        // 构建查询条件
        Condition whereCondition;

        // 已完成状态列表
        List<String> completedStatuses = Arrays.asList(
            ApprovalStatus.APPROVED.name(),
            ApprovalStatus.REJECTED_BY_SUPERVISOR.name(),
            ApprovalStatus.REJECTED_BY_MANAGER.name(),
            ApprovalStatus.CANCELLED.name()
        );

        // 根据用户角色构建不同的查询条件
        if (isAdmin) {
            // 管理员可以查看所有已完成的审批记录
            whereCondition = APPROVAL_REQUEST_DETAILS.STATUS.in(completedStatuses);
        } else if (isSupervisorOrManager) {
            // 主管或经理可以查看自己审批过的记录，以及状态为 PENDING_MANAGER_APPROVAL 的记录
            whereCondition = APPROVAL_REQUEST_DETAILS.STATUS.in(completedStatuses)
                .and(
                    APPROVAL_REQUEST_DETAILS.SUPERVISOR_ID.eq(userId)
                    .or(APPROVAL_REQUEST_DETAILS.MANAGER_ID.eq(userId))
                    .or(APPROVAL_REQUEST_DETAILS.REQUESTER_ID.eq(userId))
                )
                .or(
                    // 主管用户可以看到状态为 PENDING_MANAGER_APPROVAL 的审批记录
                    APPROVAL_REQUEST_DETAILS.STATUS.eq(ApprovalStatus.PENDING_MANAGER_APPROVAL.name())
                    .and(APPROVAL_REQUEST_DETAILS.SUPERVISOR_ID.eq(userId))
                );
        } else {
            // 普通用户只能查看自己的已完成审批记录
            whereCondition = APPROVAL_REQUEST_DETAILS.STATUS.in(completedStatuses)
                .and(APPROVAL_REQUEST_DETAILS.REQUESTER_ID.eq(userId));
        }

        // 添加可选的过滤条件
        if (status != null && !status.isEmpty()) {
            whereCondition = whereCondition.and(APPROVAL_REQUEST_DETAILS.STATUS.eq(status));
        }

        // 使用视图查询，以便获取更多信息
        SelectConditionStep<?> query = dsl.selectFrom(APPROVAL_REQUEST_DETAILS)
            .where(whereCondition);

        // 添加文件名过滤条件
        if (fileName != null && !fileName.isEmpty()) {
            query = query.and(APPROVAL_REQUEST_DETAILS.FILE_NAME.like("%" + fileName + "%"));
        }

        // 添加申请人名称过滤条件
        if (requesterName != null && !requesterName.isEmpty()) {
            query = query.and(
                APPROVAL_REQUEST_DETAILS.REQUESTER_NAME.like("%" + requesterName + "%")
                .or(APPROVAL_REQUEST_DETAILS.REQUESTER_NICK_NAME.like("%" + requesterName + "%"))
            );
        }

        // 使用分页工具类获取数据
        TableDataInfo tableData = JooqPaginationUtils.getDataTable(
            dsl,
            query.orderBy(APPROVAL_REQUEST_DETAILS.UPDATE_TIME.desc()),
            pageNum,
            pageSize
        );

        @SuppressWarnings("unchecked")
        List<ApprovalRequestDetailsRecord> records = (List<ApprovalRequestDetailsRecord>) tableData.getRows();

        if (records.isEmpty()) {
            logger.info("未找到已完成的审批记录");
            // 返回空列表，而不是抛出异常
            return tableData;
        }

        // 构建返回结果
        List<ApprovalRequestVO> result = new ArrayList<>();

        for (ApprovalRequestDetailsRecord record : records) {
            // 确定处理人信息（最后一个操作人）
            String approverName = null;
            String approverNickName = null;

            // 根据状态确定处理人
            if (ApprovalStatus.APPROVED.name().equals(record.getStatus())) {
                // 如果是已批准，处理人是经理
                approverName = record.getManagerName();
                approverNickName = record.getManagerNickName();
            } else if (ApprovalStatus.REJECTED_BY_MANAGER.name().equals(record.getStatus())) {
                // 如果是经理拒绝，处理人是经理
                approverName = record.getManagerName();
                approverNickName = record.getManagerNickName();
            } else if (ApprovalStatus.REJECTED_BY_SUPERVISOR.name().equals(record.getStatus())) {
                // 如果是主管拒绝，处理人是主管
                approverName = record.getSupervisorName();
                approverNickName = record.getSupervisorNickName();
            } else if (ApprovalStatus.CANCELLED.name().equals(record.getStatus())) {
                // 如果是已撤销，处理人是申请人自己
                approverName = record.getRequesterName();
                approverNickName = record.getRequesterNickName();
            }

            // 构建VO对象
            ApprovalRequestVO vo = ApprovalRequestVO.builder()
                    .id(record.getId())
                    .fileId(record.getFileId())
                    .fileName(record.getFileName() != null ? record.getFileName() : "未知文件")
                    .filePath(record.getFilePath())
                    .requesterId(record.getRequesterId())
                    .requesterName(record.getRequesterName() != null ? record.getRequesterName() : "未知用户")
                    .requesterNickName(record.getRequesterNickName())
                    .requesterDepartment(record.getRequesterDeptName())
                    .requestReason(record.getRequestReason())
                    .status(ApprovalStatus.valueOf(record.getStatus()))
                    .statusDesc(ApprovalStatus.valueOf(record.getStatus()).getDisplayName())
                    .supervisorId(record.getSupervisorId())
                    .supervisorName(record.getSupervisorName())
                    .supervisorNickName(record.getSupervisorNickName())
                    .supervisorDepartment(record.getSupervisorDeptName())
                    .managerId(record.getManagerId())
                    .managerName(record.getManagerName())
                    .managerNickName(record.getManagerNickName())
                    .managerDepartment(record.getManagerDeptName())
                    .createTime(record.getCreateTime())
                    .updateTime(record.getUpdateTime())
                    // 添加处理人信息
                    .approverName(approverName)
                    .approverNickName(approverNickName)
                    .build();

            result.add(vo);
        }

        // 将丰富的VO对象列表设置到分页数据对象中
        tableData.setRows(result);

        logger.info("查询已完成审批记录成功，共 {} 条记录", result.size());
        return tableData;
    }

    /**
     * 确定申请人的主管
     * 使用UserService的findApproverByRole方法
     *
     * @param requesterId 申请人ID
     * @return 主管ID
     */
    private Long determineSupervisorId(Long requesterId) {
        return userService.findApproverByRole(requesterId, UserConstants.POST_SUPERVISOR);
    }


    private void createActionRecord(Long approvalRequestId, Long actorId, ApprovalActionType actionType, String comment, ApprovalStatus previousStatus, ApprovalStatus nextStatus) {
        ApprovalActionRecordsRecord actionRecord = dsl.newRecord(APPROVAL_ACTION_RECORDS);
        actionRecord.setApprovalRequestId(approvalRequestId)
        .setActorId(actorId)
        .setActionType(actionType.name())
        .setComment(comment)
        .setPreviousStatus(previousStatus != null ? previousStatus.name() : null)
        .setNextStatus(nextStatus != null ? nextStatus.name() : null)
        .setActionTime(LocalDateTime.now());
        actionRecord.store();
        logger.info("Action record created for request ID: {}, action: {}", approvalRequestId, actionType);
    }

    /**
     * 恢复状态机状态
     *
     * @param approvalRequestId 审批请求ID
     * @return 恢复后的状态机实例
     */
    private StateMachine<ApprovalStatus, ApprovalEvent> restoreStateMachine(Long approvalRequestId) {
        try {
            // 获取新的状态机实例
            StateMachine<ApprovalStatus, ApprovalEvent> stateMachine =
                stateMachineFactory.getStateMachine(approvalRequestId.toString());

            // 从持久化存储中恢复状态
            return persister.restore(stateMachine, approvalRequestId.toString());

        } catch (Exception e) {
            logger.error("恢复状态机失败，审批请求ID: " + approvalRequestId, e);
            throw new RuntimeException("恢复审批流程状态失败", e);
        }
    }




}
