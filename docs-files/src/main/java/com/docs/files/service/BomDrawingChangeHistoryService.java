package com.docs.files.service;

import com.docs.common.core.domain.vo.BomDrawingChangeHistoryDTO;
import com.docs.common.enums.ChangeType;
import com.docs.common.enums.DrawingFileType;

import java.util.List;

/**
 * BOM图纸变更历史服务接口 - 新版本
 * 支持4种文件类型的完整变更历史追踪
 */
public interface BomDrawingChangeHistoryService {
    
    /**
     * 根据BOM项ID查询图纸变更历史
     * 
     * @param bomItemId BOM项ID
     * @return 变更历史列表
     */
    List<BomDrawingChangeHistoryDTO> selectBomDrawingChangeHistory(Integer bomItemId);
    
    /**
     * 根据BOM项ID和文件类型查询图纸变更历史
     * 
     * @param bomItemId BOM项ID
     * @param fileType 文件类型
     * @return 变更历史列表
     */
    List<BomDrawingChangeHistoryDTO> selectBomDrawingChangeHistoryByType(Integer bomItemId, DrawingFileType fileType);
    
    /**
     * 记录图纸变更历史
     * 
     * @param bomItemId BOM项ID
     * @param materialCode 物料编码
     * @param fileType 文件类型
     * @param changeType 变更类型
     * @param oldDrawingNo 变更前图纸编号
     * @param oldFileId 变更前文件ID
     * @param oldFileName 变更前文件名
     * @param oldFileVersion 变更前文件版本
     * @param newDrawingNo 变更后图纸编号
     * @param newFileId 变更后文件ID
     * @param newFileName 变更后文件名
     * @param newFileVersion 变更后文件版本
     * @param changeReason 变更原因
     */
    void recordDrawingChange(Integer bomItemId, String materialCode, DrawingFileType fileType, 
                           ChangeType changeType, String oldDrawingNo, Integer oldFileId, 
                           String oldFileName, String oldFileVersion, String newDrawingNo, 
                           Integer newFileId, String newFileName, String newFileVersion, 
                           String changeReason);
    
    /**
     * 批量记录图纸变更历史（用于多文件上传）
     * 
     * @param bomItemId BOM项ID
     * @param materialCode 物料编码
     * @param changes 变更信息列表
     * @param changeReason 变更原因
     */
    void recordMultipleDrawingChanges(Integer bomItemId, String materialCode, 
                                    List<DrawingChangeInfo> changes, String changeReason);
    
    /**
     * 图纸变更信息
     */
    class DrawingChangeInfo {
        private DrawingFileType fileType;
        private ChangeType changeType;
        private String oldDrawingNo;
        private Integer oldFileId;
        private String oldFileName;
        private String oldFileVersion;
        private String newDrawingNo;
        private Integer newFileId;
        private String newFileName;
        private String newFileVersion;
        
        // 构造器和getter/setter
        public DrawingChangeInfo(DrawingFileType fileType, ChangeType changeType,
                               String oldDrawingNo, Integer oldFileId, String oldFileName, String oldFileVersion,
                               String newDrawingNo, Integer newFileId, String newFileName, String newFileVersion) {
            this.fileType = fileType;
            this.changeType = changeType;
            this.oldDrawingNo = oldDrawingNo;
            this.oldFileId = oldFileId;
            this.oldFileName = oldFileName;
            this.oldFileVersion = oldFileVersion;
            this.newDrawingNo = newDrawingNo;
            this.newFileId = newFileId;
            this.newFileName = newFileName;
            this.newFileVersion = newFileVersion;
        }
        
        // Getters
        public DrawingFileType getFileType() { return fileType; }
        public ChangeType getChangeType() { return changeType; }
        public String getOldDrawingNo() { return oldDrawingNo; }
        public Integer getOldFileId() { return oldFileId; }
        public String getOldFileName() { return oldFileName; }
        public String getOldFileVersion() { return oldFileVersion; }
        public String getNewDrawingNo() { return newDrawingNo; }
        public Integer getNewFileId() { return newFileId; }
        public String getNewFileName() { return newFileName; }
        public String getNewFileVersion() { return newFileVersion; }
    }
}