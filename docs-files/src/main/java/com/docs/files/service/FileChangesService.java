package com.docs.files.service;

import com.docs.common.core.domain.vo.FileChangeHistoryDTO;
import com.docs.common.jooq.generated.tables.pojos.FileChanges;

import java.util.List;

public interface FileChangesService {

    /**
     * 查询文件变更记录列表（基本信息）
     * 
     * @param fileChanges 查询条件
     * @return 变更记录列表
     */
    List<FileChanges> selectFileChangesList(FileChanges fileChanges);
    
    /**
     * 查询文件变更记录列表（包含详细信息）
     * 
     * @param fileChanges 查询条件
     * @return 变更记录列表（包含文件信息）
     */
    List<FileChangeHistoryDTO> selectFileChangesDetailList(FileChanges fileChanges);
    
    /**
     * 根据BOM项ID查询图纸变更记录列表（包含详细信息）
     * 
     * @param bomItemId BOM项ID
     * @return 变更记录列表（包含文件信息）
     */
    List<FileChangeHistoryDTO> selectBomItemDrawingChangesList(Integer bomItemId);
}
