package com.docs.files.service;

import java.util.Collection;
import java.util.Map;

import com.docs.common.core.domain.entity.UserInfo;

/**
 * 用户相关服务接口
 */
public interface UserService {
    
    /**
     * 根据部门ID和岗位编码查找用户
     * 
     * @param deptId 部门ID
     * @param postCode 岗位编码
     * @return 用户ID，如果未找到则返回null
     */
    Long findUserByPostCode(Long deptId, String postCode);
    
    /**
     * 根据用户名查找用户ID
     * 
     * @param username 用户名
     * @return 用户ID，如果未找到则返回null
     */
    Long findUserIdByUsername(String username);
    
    /**
     * 根据申请人ID和角色类型查找审批人
     * 统一处理确定主管或经理的逻辑
     * 
     * @param requesterId 申请人ID
     * @param role 角色类型，例如 "supervisor"、"manager" 等，对应岗位编码
     * @return 审批人ID
     * @throws RuntimeException 当未找到用户、用户未分配部门或未找到对应角色时抛出异常
     */
    Long findApproverByRole(Long requesterId, String role);
    
    /**
     * 根据用户ID获取用户基本信息，包含部门信息
     * 
     * @param userId 用户ID
     * @return 用户基本信息，如果未找到则返回null
     */
    UserInfo getUserInfoById(Long userId);
    
    /**
     * 批量获取用户基本信息，包含部门信息
     * 
     * @param userIds 用户ID集合
     * @return 以用户ID为键，用户信息为值的Map集合
     */
    Map<Long, UserInfo> getUserInfoByIds(Collection<Long> userIds);
}
