package com.docs.files.service.impl;

import com.alibaba.excel.EasyExcel;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.tables.MaterialDrawingMap;
import com.docs.common.jooq.generated.tables.daos.MaterialDrawingMapDao;
import com.docs.files.listener.RelationExcelListener;
import com.docs.files.service.MaterialDrawingMapService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物料-图纸映射服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterialDrawingMapServiceImpl implements MaterialDrawingMapService {


    private final MaterialDrawingMapDao materialDrawingMapDao;

    /**
     * upsert 物料-图纸映射关系
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsertMaterialDrawingMap(String materialCode, String drawingNo, Integer drawingFileId, String materialName, String specification) {
        DSLContext dsl = materialDrawingMapDao.ctx();
        try {
            // 先进行唯一性校验
            validateUniqueness(materialCode, drawingNo);
            
            com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap exist =
                    dsl.selectFrom(MaterialDrawingMap.MATERIAL_DRAWING_MAP)
                            .where(MaterialDrawingMap.MATERIAL_DRAWING_MAP.MATERIAL_CODE.eq(materialCode))
                            .fetchOneInto(com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap.class);

            if (exist == null) {
                com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap map =
                        new com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap();
                map.setMaterialCode(materialCode);
                map.setDrawingNo(drawingNo);
                map.setDrawingFileId(drawingFileId);
                map.setMaterialName(materialName);
                map.setSpecification(specification);
                materialDrawingMapDao.insert(map);
                log.info("插入物料-图纸映射: materialCode={}, drawingNo={}", materialCode, drawingNo);
            } else {
                exist.setDrawingNo(drawingNo);
                exist.setDrawingFileId(drawingFileId);
                exist.setMaterialName(materialName);
                exist.setSpecification(specification);
                materialDrawingMapDao.update(exist);
                log.info("更新物料-图纸映射: materialCode={}, drawingNo={}", materialCode, drawingNo);
            }
        } catch (Exception e) {
            log.error("物料-图纸映射 upsert 失败: materialCode={}, drawingNo={}", materialCode, drawingNo, e);
            throw e;
        }
    }

    /**
     * upsert 物料-图纸映射关系（多文件支持）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsertMaterialDrawingMapMultiFiles(String materialCode, String drawingNo, 
                                                  Integer pdfFileId, Integer drawing2dFileId, 
                                                  Integer drawing3dFileId, Integer compressedFileId,
                                                  String materialName, String specification) {
        DSLContext dsl = materialDrawingMapDao.ctx();
        try {
            // 先进行唯一性校验
            validateUniqueness(materialCode, drawingNo);
            
            com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap exist =
                    dsl.selectFrom(MaterialDrawingMap.MATERIAL_DRAWING_MAP)
                            .where(MaterialDrawingMap.MATERIAL_DRAWING_MAP.MATERIAL_CODE.eq(materialCode))
                            .fetchOneInto(com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap.class);

            if (exist == null) {
                // 插入新记录
                com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap map =
                        new com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap();
                map.setMaterialCode(materialCode);
                map.setDrawingNo(drawingNo);
                map.setMaterialName(materialName);
                map.setSpecification(specification);
                
                // 设置多文件ID
                map.setPdfFileId(pdfFileId);
                map.setDrawing_2dFileId(drawing2dFileId);
                map.setDrawing_3dFileId(drawing3dFileId);
                map.setCompressedFileId(compressedFileId);
                
                materialDrawingMapDao.insert(map);
                log.info("插入物料-图纸映射(多文件): materialCode={}, drawingNo={}", materialCode, drawingNo);
            } else {
                // 更新现有记录
                exist.setDrawingNo(drawingNo);
                exist.setMaterialName(materialName);
                exist.setSpecification(specification);
                
                // 更新多文件ID（只更新非null的文件ID）
                if (pdfFileId != null) {
                    exist.setPdfFileId(pdfFileId);
                }
                if (drawing2dFileId != null) {
                    exist.setDrawing_2dFileId(drawing2dFileId);
                }
                if (drawing3dFileId != null) {
                    exist.setDrawing_3dFileId(drawing3dFileId);
                }
                if (compressedFileId != null) {
                    exist.setCompressedFileId(compressedFileId);
                }
                
                materialDrawingMapDao.update(exist);
                log.info("更新物料-图纸映射(多文件): materialCode={}, drawingNo={}", materialCode, drawingNo);
            }
        } catch (Exception e) {
            log.error("物料-图纸映射多文件 upsert 失败: materialCode={}, drawingNo={}", materialCode, drawingNo, e);
            throw e;
        }
    }

    /**
     * 根据物料编码列表批量查询物料-图纸映射关系
     * @param materialCodes 物料编码列表
     * @return 物料编码到物料-图纸映射的映射
     */
    @Override
    public Map<String, com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap> findByMaterialCodes(List<String> materialCodes) {
        if (materialCodes == null || materialCodes.isEmpty()) {
            return Collections.emptyMap();
        }

        DSLContext dsl = materialDrawingMapDao.ctx();
        return dsl.selectFrom(MaterialDrawingMap.MATERIAL_DRAWING_MAP)
                .where(MaterialDrawingMap.MATERIAL_DRAWING_MAP.MATERIAL_CODE.in(materialCodes))
                .fetchStream()
                .collect(Collectors.toMap(
                    record -> record.get(MaterialDrawingMap.MATERIAL_DRAWING_MAP.MATERIAL_CODE),
                    record -> record.into(com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap.class),
                    (existing, replacement) -> existing  // 如果有重复项，保留第一个
                ));
    }

    /**
     * 批量处理物料-图纸映射关系（更新或插入）
     * @param relations 物料-图纸映射关系列表
     * @return 处理成功的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpsertFromRelations(List<com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap> relations) {
        if (relations == null || relations.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap relation : relations) {
            try {
                // 调用现有的 upsertMaterialDrawingMap 方法，确保正确处理一对一关系的更新
                upsertMaterialDrawingMap(
                    relation.getMaterialCode(),
                    relation.getDrawingNo(),
                    relation.getDrawingFileId(),
                    relation.getMaterialName(),
                    relation.getSpecification()
                );
                successCount++;
            } catch (Exception e) {
                log.error("处理物料-图纸映射失败: materialCode={}, drawingNo={}",
                    relation.getMaterialCode(), relation.getDrawingNo(), e);
                // 不终止整个流程, 继续处理下一条
            }
        }

        log.info("批量处理物料-图纸映射完成，成功: {}/{}", successCount, relations.size());
        return successCount;
    }

    /**
     * 根据图纸编号查询物料-图纸映射关系
     * @param drawingNo 图纸编号
     * @return 物料-图纸映射关系，如果不存在返回 null
     */
    @Override
    public com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap findByDrawingNo(String drawingNo) {
        if (drawingNo == null || drawingNo.isEmpty()) {
            return null;
        }

        DSLContext dsl = materialDrawingMapDao.ctx();
        return dsl.selectFrom(MaterialDrawingMap.MATERIAL_DRAWING_MAP)
                .where(MaterialDrawingMap.MATERIAL_DRAWING_MAP.DRAWING_NO.eq(drawingNo))
                .fetchOneInto(com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap.class);
    }

    /**
     * 根据物料编码查询物料-图纸映射关系
     * @param materialCode 物料编码
     * @return 物料-图纸映射关系，如果不存在返回 null
     */
    @Override
    public com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap findByMaterialCode(String materialCode) {
        if (materialCode == null || materialCode.isEmpty()) {
            return null;
        }

        DSLContext dsl = materialDrawingMapDao.ctx();
        return dsl.selectFrom(MaterialDrawingMap.MATERIAL_DRAWING_MAP)
                .where(MaterialDrawingMap.MATERIAL_DRAWING_MAP.MATERIAL_CODE.eq(materialCode))
                .fetchOneInto(com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap.class);
    }

    /**
     * 更新物料-图纸映射关系
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMaterialDrawingMap(String materialCode, String drawingNo, Integer drawingFileId) {
        DSLContext dsl = materialDrawingMapDao.ctx();
        dsl.update(MaterialDrawingMap.MATERIAL_DRAWING_MAP)
            .set(MaterialDrawingMap.MATERIAL_DRAWING_MAP.DRAWING_FILE_ID, drawingFileId)
            .where(MaterialDrawingMap.MATERIAL_DRAWING_MAP.MATERIAL_CODE.eq(materialCode))
            .and(MaterialDrawingMap.MATERIAL_DRAWING_MAP.DRAWING_NO.eq(drawingNo))
            .execute();
    }

    @Override
    public List<com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap> parseRelationExcel(String filePath, Integer fileId) {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new ServiceException("文件不存在: " + filePath);
        }

        // 创建监听器
        RelationExcelListener listener = new RelationExcelListener();

        // 使用EasyExcel读取Excel文件，添加配置
        EasyExcel.read(file, listener)
                .sheet()
                .headRowNumber(0)  // 设置表头行号为1
                .doRead();

        return listener.getRelations();
    }

    /**
     * 验证图纸编号和物料编号的唯一性
     */
    @Override
    public void validateUniqueness(String materialCode, String drawingNo) {
        DSLContext dsl = materialDrawingMapDao.ctx();
        
        // 只需要检查图纸编号是否被其他物料使用
        // 对于更新场景，物料可以修改其图纸编号，只要新的图纸编号在数据库中唯一即可
        com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap existingByDrawingNo =
                dsl.selectFrom(MaterialDrawingMap.MATERIAL_DRAWING_MAP)
                        .where(MaterialDrawingMap.MATERIAL_DRAWING_MAP.DRAWING_NO.eq(drawingNo))
                        .and(MaterialDrawingMap.MATERIAL_DRAWING_MAP.MATERIAL_CODE.ne(materialCode))
                        .fetchOneInto(com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap.class);

        if (existingByDrawingNo != null) {
            throw new ServiceException(String.format(
                "图纸编号 '%s' 已被物料 '%s' 使用，不能重复使用。同一个图纸编号只能对应一个物料。", 
                drawingNo, existingByDrawingNo.getMaterialCode()));
        }
        
        log.debug("唯一性校验通过: materialCode={}, drawingNo={}", materialCode, drawingNo);
    }

    /**
     * 更新物料的图纸编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDrawingNumber(String materialCode, String newDrawingNo) {
        DSLContext dsl = materialDrawingMapDao.ctx();
        
        try {
            // 1. 检查物料是否存在
            com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap existing = 
                    findByMaterialCode(materialCode);
            
            if (existing == null) {
                throw new ServiceException("物料编号 '" + materialCode + "' 不存在图纸映射关系");
            }
            
            // 2. 如果新图纸编号与当前相同，无需更新
            if (newDrawingNo.equals(existing.getDrawingNo())) {
                log.info("图纸编号未发生变化，无需更新: materialCode={}, drawingNo={}", materialCode, newDrawingNo);
                return;
            }
            
            // 3. 验证新图纸编号的唯一性
            validateUniqueness(materialCode, newDrawingNo);
            
            // 4. 更新图纸编号
            int updatedRows = dsl.update(MaterialDrawingMap.MATERIAL_DRAWING_MAP)
                    .set(MaterialDrawingMap.MATERIAL_DRAWING_MAP.DRAWING_NO, newDrawingNo)
                    .where(MaterialDrawingMap.MATERIAL_DRAWING_MAP.MATERIAL_CODE.eq(materialCode))
                    .execute();
            
            if (updatedRows == 0) {
                throw new ServiceException("更新图纸编号失败，物料不存在: " + materialCode);
            }
            
            log.info("成功更新图纸编号: materialCode={}, oldDrawingNo={}, newDrawingNo={}", 
                    materialCode, existing.getDrawingNo(), newDrawingNo);
                    
        } catch (ServiceException e) {
            log.error("更新图纸编号失败: materialCode={}, newDrawingNo={}", materialCode, newDrawingNo, e);
            throw e;
        } catch (Exception e) {
            log.error("更新图纸编号时发生意外错误: materialCode={}, newDrawingNo={}", materialCode, newDrawingNo, e);
            throw new ServiceException("更新图纸编号失败: " + e.getMessage());
        }
    }

    /**
     * 获取物料已存在的文件类型
     */
    @Override
    public List<String> getExistingFileTypes(String materialCode) {
        com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap mapping = 
                findByMaterialCode(materialCode);
        
        if (mapping == null) {
            return Collections.emptyList();
        }
        
        List<String> existingTypes = new ArrayList<>();
        
        if (mapping.getPdfFileId() != null) {
            existingTypes.add("PDF");
        }
        if (mapping.getDrawing_2dFileId() != null) {
            existingTypes.add("2D");
        }
        if (mapping.getDrawing_3dFileId() != null) {
            existingTypes.add("3D");
        }
        if (mapping.getCompressedFileId() != null) {
            existingTypes.add("2D/3D");
        }
        
        return existingTypes;
    }
}
