package com.docs.files.service.impl;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.entity.FilesEntity;
import com.docs.common.core.domain.model.FileChangeRequest;
import com.docs.common.core.domain.vo.FileListVO;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.files.repository.FileRepository;
import com.docs.files.service.FilesService;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.util.FileProcessorHelper;
import com.docs.files.util.FileVersionHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 文件服务实现类（重构版）
 * 使用组合设计模式和策略模式
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FilesServiceImplNew implements FilesService {

    private final FileRepository fileRepository;
    private final FileProcessorHelper fileProcessorHelper;
    private final FileVersionHelper fileVersionHelper;

    /**
     * 查询文件列表
     */
    @Override
    public List<FileListVO> selectFilesList(FilesEntity filesEntity) {
        return fileRepository.selectFilesList(filesEntity);
    }

    /**
     * 上传文件
     */
    @Override
    public AjaxResult uploadFile(MultipartFile file, String productModel, String fileType,
                          String subType, String drawingNo, String version) {
        try {
            // 构建文件上传请求
            FileUploadRequest request = FileUploadRequest.builder()
                    .files(new MultipartFile[]{file})
                    .productModel(productModel)
                    .fileType(fileType)
                    .subType(subType)
                    .drawingNo(drawingNo)
                    .version(version)
                    .build();

            // 调用文件上传组件
            return fileProcessorHelper.uploadFile(request);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return AjaxResult.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传多文件
     */
    @Override
    public AjaxResult uploadFiles(MultipartFile[] files, String productModel, String fileType,
                           String subType, String drawingNo, String version) {
        try {
            // 构建文件上传请求
            FileUploadRequest request = FileUploadRequest.builder()
                    .files(files)
                    .productModel(productModel)
                    .fileType(fileType)
                    .subType(subType)
                    .drawingNo(drawingNo)
                    .version(version)
                    .build();

            // 调用文件上传组件
            return fileProcessorHelper.uploadFile(request);
        } catch (Exception e) {
            log.error("多文件上传失败", e);
            return AjaxResult.error("多文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 变更文件版本
     */
    @Override
    public AjaxResult changeFileVersion(FileChangeRequest request) {
        return fileVersionHelper.changeFileVersionWithValidation(request);
    }

    /**
     * 变更文件版本（多文件）
     */
    @Override
    public AjaxResult changeFileVersionMultiple(FileChangeRequest request) {
        return fileVersionHelper.changeFileVersionMultiple(request);
    }

    /**
     * 变更文件版本（带版本号校验）
     * 新版本号必须大于当前版本号
     */
    @Override
    public AjaxResult changeFileVersionWithValidation(FileChangeRequest request) {
        return fileVersionHelper.changeFileVersionWithValidation(request);
    }

    /**
     * 下载文件
     */
    @Override
    public void downloadFile(Integer fileId, HttpServletResponse response) throws IOException {
        try {
            // 1. 获取文件信息
            Files file = fileRepository.fetchFileById(fileId);
            if (file == null) {
                log.warn("尝试下载不存在的文件记录，fileId: {}", fileId);
                throw new ServiceException("文件记录不存在，可能已被删除");
            }

            // 2. 检查物理文件是否存在
            Path filePath = Paths.get(file.getFilePath());
            if (!java.nio.file.Files.exists(filePath)) {
                log.warn("文件记录存在但物理文件不存在，fileId: {}, filePath: {}", fileId, file.getFilePath());
                throw new ServiceException("文件已丢失，请联系管理员");
            }

            // 3. 检查文件是否可读
            if (!java.nio.file.Files.isReadable(filePath)) {
                log.warn("文件存在但不可读，fileId: {}, filePath: {}", fileId, file.getFilePath());
                throw new ServiceException("文件无法读取，请联系管理员");
            }

            // 4. 设置响应头
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(file.getFileName(), "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            // 添加 CORS 头以确保前端能访问 Content-Disposition
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            // 5. 读取文件并写入响应
            try (InputStream inputStream = java.nio.file.Files.newInputStream(filePath);
                 OutputStream outputStream = response.getOutputStream()) {

                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }

            log.info("文件下载成功: {}", file.getFilePath());
        } catch (java.nio.file.NoSuchFileException e) {
            log.error("文件不存在: {}", e.getFile());
            throw new ServiceException("文件已丢失，请联系管理员");
        } catch (java.nio.file.AccessDeniedException e) {
            log.error("文件访问被拒绝: {}", e.getFile());
            throw new ServiceException("文件无法访问，请联系管理员");
        } catch (IOException e) {
            log.error("文件读取失败，fileId: {}", fileId, e);
            throw new ServiceException("文件读取失败，请稍后重试或联系管理员");
        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("文件下载过程中发生未知错误，fileId: {}", fileId, e);
            throw new ServiceException("文件下载失败，请联系管理员");
        }
    }

    /**
     * 删除文件记录
     */
    @Override
    public int deleteFile(Integer fileId) {
        return fileRepository.deleteFile(fileId);
    }
}
