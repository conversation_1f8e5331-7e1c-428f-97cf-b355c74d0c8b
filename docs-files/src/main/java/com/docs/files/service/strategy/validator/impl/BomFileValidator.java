package com.docs.files.service.strategy.validator.impl;

import com.docs.common.constant.FileConstants;
import com.docs.common.exception.ServiceException;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.service.strategy.validator.FileValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * BOM文件验证器
 */
@Component
public class BomFileValidator implements FileValidator {
    private final CommonFileValidator commonValidator;
    
    public BomFileValidator(CommonFileValidator commonValidator) {
        this.commonValidator = commonValidator;
    }
    
    @Override
    public void validate(FileUploadRequest request) {
        // 通用验证
        commonValidator.validate(request);
        
        // BOM特定验证，与原BomFileStrategy.validateParams和validateFileCount保持一致
        if (request.getFiles() == null || request.getFiles().length != 2) {
            throw new ServiceException("请上传两个Excel文件：BOM数据文件和物料图纸关系文件");
        }
        
        if (StringUtils.isEmpty(request.getSubType())) {
            throw new ServiceException("BOM类型不能为空");
        }
        if (!FileConstants.VALID_BOM_TYPES.contains(request.getSubType())) {
            throw new ServiceException("无效的BOM类型: " + request.getSubType());
        }
    }
}
