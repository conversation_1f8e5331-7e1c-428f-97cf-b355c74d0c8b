package com.docs.files.service.processing;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件处理策略注册表
 */
@Component
public class FileProcessingStrategyRegistry {
    private final Map<String, FileProcessingStrategy> strategies = new HashMap<>();
    
    public FileProcessingStrategyRegistry(List<FileProcessingStrategy> strategyList) {
        strategyList.forEach(strategy -> strategies.put(strategy.getFileType(), strategy));
    }
    
    /**
     * 根据文件类型获取对应的处理策略
     * @param fileType 文件类型
     * @return 处理策略
     */
    public FileProcessingStrategy getStrategy(String fileType) {
        FileProcessingStrategy strategy = strategies.get(fileType);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileType);
        }
        return strategy;
    }
}
