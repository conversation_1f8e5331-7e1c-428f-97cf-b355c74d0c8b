package com.docs.files.service.impl;

import com.docs.common.constant.UserConstants;
import com.docs.common.enums.ApprovalStatus;
import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.records.DownloadApprovalRequestsRecord;
import com.docs.common.utils.SecurityUtils;
import com.docs.files.config.DrawingPermissionProperties;
import com.docs.files.service.FileDownloadAuthService;
import com.docs.system.domain.SysPost;
import com.docs.system.service.ISysPostService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import static com.docs.common.jooq.generated.Tables.DOWNLOAD_APPROVAL_REQUESTS;
import static com.docs.common.jooq.generated.Tables.FILE_DOWNLOAD_LOGS;
import static com.docs.common.jooq.generated.Tables.FILES;

/**
 * 文件下载权限服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FileDownloadAuthServiceImpl implements FileDownloadAuthService {

    private final DSLContext dsl;
    private final ISysPostService postService;
    private final DrawingPermissionProperties drawingPermissionProperties;

    @Override
    public boolean hasDownloadPermission(Long fileId, Long userId) {
        return checkDownloadPermissionStatus(fileId, userId) == PermissionResult.ALLOWED;
    }

    @Override
    public PermissionResult checkDownloadPermissionStatus(Long fileId, Long userId) {
        log.info("检查用户 {} 下载文件 {} 的权限状态", userId, fileId);

        // 管理员始终有权限下载
        if (SecurityUtils.isAdmin()) {
            log.info("用户 {} 是管理员，有权限下载文件 {}", userId, fileId);
            return PermissionResult.ALLOWED;
        }

        // 获取文件信息以检查是否为图纸文件
        Files file = dsl.selectFrom(FILES)
                .where(FILES.ID.eq(fileId.intValue()))
                .fetchOneInto(Files.class);

        if (file == null) {
            log.warn("文件 {} 不存在", fileId);
            return PermissionResult.NO_PERMISSION;
        }

        // 如果是图纸文件，需要额外检查图纸子类型权限
        if (FilesFileType.DRAWING.equals(file.getFileType()) ||
            FilesFileType.BOM_DRAWING.equals(file.getFileType())) {

            // 检查用户是否有该图纸子类型的下载权限
            if (!hasDrawingSubTypeDownloadPermission(file.getSubType(), userId)) {
                log.warn("用户 {} 没有下载 {} 类型图纸的权限", userId, file.getSubType());
                return PermissionResult.NO_PERMISSION;
            }
        }

        // 检查用户岗位
        List<Long> userPostIds = postService.selectPostListByUserId(userId);
        boolean isEmployee = false;
        boolean isSupervisor = false;
        boolean isManager = false;

        for (Long postId : userPostIds) {
            SysPost post = postService.selectPostById(postId);
            if (post != null) {
                if (UserConstants.POST_EMPLOYEE.equals(post.getPostCode())) {
                    isEmployee = true;
                } else if (UserConstants.POST_SUPERVISOR.equals(post.getPostCode())) {
                    isSupervisor = true;
                } else if (UserConstants.POST_MANAGER.equals(post.getPostCode())) {
                    isManager = true;
                }
            }
        }

        // 只有经理有默认下载权限，无需审批
        if (isManager) {
            log.info("用户 {} 是经理，有权限下载文件 {}", userId, fileId);
            return PermissionResult.ALLOWED;
        }

        // 主管和普通员工都需要检查是否有已批准的下载申请
        if (isSupervisor || isEmployee) {
            DownloadApprovalRequestsRecord approvalRequest = dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                    .where(DOWNLOAD_APPROVAL_REQUESTS.FILE_ID.eq(fileId))
                    .and(DOWNLOAD_APPROVAL_REQUESTS.REQUESTER_ID.eq(userId))
                    .and(DOWNLOAD_APPROVAL_REQUESTS.STATUS.eq(ApprovalStatus.APPROVED.name()))
                    .orderBy(DOWNLOAD_APPROVAL_REQUESTS.UPDATE_TIME.desc())
                    .fetchAny();

            if (approvalRequest != null) {
                log.info("用户 {} 有已批准的下载申请，可以下载文件 {}", userId, fileId);
                return PermissionResult.ALLOWED;
            }

            String userRole = isSupervisor ? "主管" : "普通员工";
            log.warn("{} {} 没有已批准的下载申请，需要申请权限下载文件 {}", userRole, userId, fileId);
            return PermissionResult.NEED_APPROVAL;
        }

        // 如果用户没有任何岗位，默认不允许下载
        log.warn("用户 {} 没有任何岗位，不能下载文件 {}", userId, fileId);
        return PermissionResult.NO_PERMISSION;
    }

    @Override
    public boolean hasDrawingSubTypeDownloadPermission(String subType, Long userId) {
        // 检查是否启用图纸子类型权限检查
        if (!drawingPermissionProperties.getValidation().isEnabled()) {
            if (drawingPermissionProperties.getValidation().isLogPermissionChecks()) {
                log.debug("图纸子类型权限检查已禁用，允许下载");
            }
            return true;
        }

        if (drawingPermissionProperties.getValidation().isLogPermissionChecks()) {
            log.debug("检查用户 {} 是否有下载 {} 类型图纸的权限", userId, subType);
        }

        // 管理员始终有权限
        if (SecurityUtils.isAdmin()) {
            if (drawingPermissionProperties.getValidation().isLogPermissionChecks()) {
                log.debug("用户 {} 是管理员，有权限下载 {} 类型图纸", userId, subType);
            }
            return true;
        }

        // 如果子类型为空，根据配置决定是否允许下载
        if (subType == null || subType.trim().isEmpty()) {
            boolean allowUnknown = drawingPermissionProperties.getDefaultConfig().isAllowUnknownSubtype();
            if (drawingPermissionProperties.getValidation().isLogPermissionChecks()) {
                log.debug("图纸子类型为空，根据配置 allowUnknownSubtype={} 决定是否允许下载", allowUnknown);
            }
            return allowUnknown;
        }

        // 从配置中获取权限代码
        String permissionCode = drawingPermissionProperties.getPermissionCode(subType);
        if (permissionCode == null) {
            // 未知子类型的处理
            boolean allowUnknown = drawingPermissionProperties.getDefaultConfig().isAllowUnknownSubtype();
            if (drawingPermissionProperties.getValidation().isLogPermissionChecks()) {
                log.warn("未知的图纸子类型: {}，根据配置 allowUnknownSubtype={} 决定是否允许下载", subType, allowUnknown);
            }

            if (!allowUnknown) {
                // 如果不允许未知子类型，检查默认权限
                String defaultPermission = drawingPermissionProperties.getDefaultConfig().getUnknownSubtypePermission();
                if (defaultPermission != null && !defaultPermission.trim().isEmpty()) {
                    return SecurityUtils.hasPermi(defaultPermission);
                }
            }
            return allowUnknown;
        }

        // 检查用户是否有对应的权限
        boolean hasPermission = SecurityUtils.hasPermi(permissionCode);
        if (drawingPermissionProperties.getValidation().isLogPermissionChecks()) {
            log.debug("用户 {} 对 {} 类型图纸的权限检查结果: {} (权限代码: {})", userId, subType, hasPermission, permissionCode);
        }

        return hasPermission;
    }



    @Override
    public void recordDownloadAction(Long fileId, Long userId) {
//        log.info("记录用户 {} 下载文件 {} 的操作", userId, fileId);

        try {
            // 记录下载操作
            dsl.insertInto(FILE_DOWNLOAD_LOGS)
                .set(FILE_DOWNLOAD_LOGS.FILE_ID, fileId)
                .set(FILE_DOWNLOAD_LOGS.USER_ID, userId)
                .set(FILE_DOWNLOAD_LOGS.DOWNLOAD_TIME, LocalDateTime.now())
                .set(FILE_DOWNLOAD_LOGS.IP_ADDRESS, SecurityUtils.getIpAddr())
                .execute();

            log.info("成功记录用户 {} 下载文件 {} 的操作", userId, fileId);
        } catch (Exception e) {
            // 记录失败不应影响下载，只记录日志
            log.error("记录用户 {} 下载文件 {} 的操作失败", userId, fileId, e);
        }
    }
}
