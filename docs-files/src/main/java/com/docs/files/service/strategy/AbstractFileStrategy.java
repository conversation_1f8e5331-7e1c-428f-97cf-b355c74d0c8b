package com.docs.files.service.strategy;

import com.docs.common.constant.FileConstants;
import com.docs.common.core.domain.AjaxResult;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.enums.FilesStatus;
import com.docs.common.jooq.generated.tables.daos.FilesDao;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.common.utils.SecurityUtils;
import com.docs.files.service.ProductModelsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.stream.Collectors;

import static com.docs.common.jooq.generated.Tables.FILES;

/**
 * 文件处理策略抽象基类，实现了文件上传的基本流程和通用方法
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractFileStrategy {

    @Autowired
    protected FilesDao filesDao;
    @Autowired
    protected ProductModelsService productModelsService;

    //当前日期yyyyMMdd
    protected String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));


    /**
     * Helper method to get ProductModels, handling null case.
     */
    protected ProductModels getProductModel(String productModelCode) {
        ProductModels model = productModelsService.selectByModelCode(productModelCode);
        if (model == null) {
            throw new ServiceException("产品型号不存在: " + productModelCode);
        }
        return model;
    }

    /**
     * Helper method to check if a version already exists using DSLContext.
     */
    protected Files selectByVersion(Integer modelId, String fileType, String subType, String version) {
        DSLContext dsl = filesDao.ctx(); // Get DSLContext from DAO

        FilesFileType fileTypeEnum;
        try {
            fileTypeEnum = FilesFileType.valueOf(fileType); // Convert String to Enum
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Invalid fileType string '{}' provided for version check", fileType, e);
            // Depending on requirements, you might return null or throw an exception
            // Returning null indicates 'not found' due to bad input type
            return null;
        }

        return dsl.selectFrom(FILES)
                .where(FILES.PRODUCT_MODEL_ID.eq(modelId))
                .and(FILES.FILE_TYPE.eq(fileTypeEnum))
                // Handle subType being potentially null or empty consistently
                .and(StringUtils.isEmpty(subType) ? FILES.SUB_TYPE.isNull().or(FILES.SUB_TYPE.eq("")) : FILES.SUB_TYPE.eq(subType))
                .and(FILES.VERSION.eq(version))
                .fetchOneInto(Files.class); // Fetch result directly into the POJO
    }


    protected Files selectByDrawingVersionByNo(String drawingNo, String version) {
        DSLContext dsl = filesDao.ctx();
        return dsl.selectFrom(FILES)
                .where(FILES.DRAWING_NO.eq(drawingNo))
                .and(FILES.VERSION.eq(version))
                .fetchOneInto(Files.class);
    }

    /**
     * 处理文件上传的主要流程
     *
     * @param files        上传的文件数组
     * @param productModel 产品型号
     * @param fileType     文件类型
     * @param subType      子类型
     * @param drawingNo    图纸编号
     * @param version      版本号
     * @return 处理结果
     */
    public AjaxResult processFiles(MultipartFile[] files, String productModel, String fileType,
                                   String subType, String drawingNo, String version) {
        // 检查机型权限
        checkModelPermission(productModel);
        try {
            // 1. 验证文件数量
            validateFileCount(files);

            // 2. 验证除文件以外的参数
            validateParams(productModel, fileType, subType, drawingNo, version);

            // 3. 获取产品型号ID
            ProductModels model = getProductModel(productModel);

            // 4. 处理文件
            MultipartFile file = files[0];
            if (file.isEmpty() || !isValidFileExtension(file.getOriginalFilename())) {
                return AjaxResult.error(getFileType() + "文件无效或不支持的文件类型");
            }

            // 5. 检查版本号是否已存在
            Files existingFile = selectByVersion(model.getId(), fileType, subType, version);
            if (existingFile != null) {
                return AjaxResult.error("该版本号 " + version + " 已存在，请使用其他版本号");
            }

            // 6. 构建文件保存路径
            String targetDir = buildTargetDirectory(fileType, subType, version);
            String fileName = preserveOriginalFilename(file.getOriginalFilename(), version);

            // 7. 保存文件
            Path filePath = processFileUpload(file, targetDir, fileName);

            // 8. 保存文件信息到数据库
            Files fileInfo = new Files();
            fileInfo.setProductModelId(model.getId());
            try {
                fileInfo.setFileType(FilesFileType.valueOf(fileType));
            } catch (IllegalArgumentException e) {
                log.error("Invalid fileType string: {}", fileType, e);
                throw new ServiceException("内部错误：无效的文件类型代码");
            }
            fileInfo.setSubType(subType);
            fileInfo.setVersion(version);
            // Ensure '使用' matches an enum constant name in FilesStatus
            fileInfo.setStatus(FilesStatus.ACTIVE); // Adjust if enum name is different
            fileInfo.setFilePath(filePath.toString());

            // 9. 设置特定文件类型的额外信息
            setAdditionalFileInfo(fileInfo, drawingNo);

            filesDao.insert(fileInfo);
            // It's generally reliable that jOOQ populates the ID after insert if using sequences/auto-increment
            // If issues persist, add fetching logic here as a fallback.

            return AjaxResult.success("上传成功", fileInfo);
        } catch (ServiceException e) {
            // Log specific service exceptions where they occur (e.g., validation)
            throw e;
        } catch (Exception e) {
            log.error("文件上传处理失败", e);
            throw new ServiceException("文件上传处理失败");
        }
    }

    /**
     * 获取支持的文件类型 (e.g., "DRAWING", "BOM", "SOFTWARE")
     *
     * @return 文件类型字符串
     */
    public abstract String getFileType();

    // FileProcessStrategy interface method
    public String getSupportedFileType() {
        return getFileType();
    }

    /**
     * 验证文件数量 (e.g., exactly 1 for drawing, exactly 2 for BOM)
     *
     * @param files 上传的文件数组
     */
    public abstract void validateFileCount(MultipartFile[] files);

    /**
     * 设置特定文件类型的额外信息 (e.g., set drawingNo for drawings)
     * 子类应重写此方法以添加特定字段。
     *
     * @param fileInfo  文件信息对象 (jOOQ POJO)
     * @param drawingNo 图纸编号 (或其他特定参数)
     */
    protected void setAdditionalFileInfo(Files fileInfo, String drawingNo) {
        // Default implementation does nothing. Subclasses override.
        // Example for a Drawing subtype:
        // if (fileInfo.getFileType() == FilesFileType.DRAWING) {
        //     fileInfo.setDrawingNo(drawingNo);
        // }
    }

    /**
     * 验证通用上传参数 (Model, FileType, Version)。
     * 子类可以调用 super.validateParams() 并添加特定验证。
     *
     * @param productModel 产品型号
     * @param fileType     文件类型
     * @param subType      子类型
     * @param drawingNo    图纸编号
     * @param version      版本号
     */
    protected void validateParams(String productModel, String fileType,
                                  String subType, String drawingNo, String version) {
        // 验证产品型号
        if (StringUtils.isEmpty(productModel)) {
            throw new ServiceException("产品型号不能为空");
        }

        // 验证文件类型
        if (StringUtils.isEmpty(fileType)) {
            throw new ServiceException("文件类型不能为空");
        }
        // Check if the provided fileType matches the strategy's file type
        if (!getFileType().equals(fileType)) {
            throw new ServiceException("文件类型不匹配，期望 " + getFileType() + "，实际为 " + fileType);
        }
        // General check against all valid types (optional, could be removed if previous check is sufficient)
        // if (!FileConstants.VALID_FILE_TYPES.contains(fileType)) {
        //     throw new ServiceException("不支持的文件类型: " + fileType);
        // }

        // 验证版本号
        if (StringUtils.isEmpty(version) || !version.matches(FileConstants.VERSION_REGEX)) {
            throw new ServiceException("版本号格式不正确 (应为 X.Y)");
        }
    }

    /**
     * 检查文件扩展名是否有效 (使用 FileConstants.ALLOWED_EXTENSIONS)。
     *
     * @param filename 文件名
     * @return 是否有效
     */
    protected boolean isValidFileExtension(String filename) {
        if (StringUtils.isEmpty(filename)) {
            return false;
        }
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return FileConstants.ALLOWED_EXTENSIONS.contains(extension);
    }

    /**
     * 根据图纸子类型验证文件扩展名
     *
     * @param filename 文件名
     * @param subType 图纸子类型
     * @return 是否有效
     */
    protected boolean isValidDrawingFileExtension(String filename, String subType) {
        if (StringUtils.isEmpty(filename) || StringUtils.isEmpty(subType)) {
            return false;
        }

        String extension = FilenameUtils.getExtension(filename).toLowerCase();

        if (FileConstants.DRAWING_SUB_TYPE_PDF.equals(subType)) {
            return FileConstants.PDF_EXTENSIONS.contains(extension);
        } else if (FileConstants.DRAWING_SUB_TYPE_2D3D.equals(subType)) {
            return FileConstants.ARCHIVE_EXTENSIONS.contains(extension);
        } else if (FileConstants.DRAWING_SUB_TYPE_2D.equals(subType) || FileConstants.DRAWING_SUB_TYPE_3D.equals(subType)) {
            // 2D和3D图纸类型不需要校验文件格式，接受任意格式
            return true;
        }

        return false;
    }

    /**
     * 构建目标目录路径。
     * 基础结构: base_path/model/fileType/subType/Version
     * 子类可以通过重写此方法来自定义路径结构。
     *
     * @param fileType 文件类型
     * @param subType  子类型
     * @param version  版本号
     * @return 目标目录路径字符串
     */
    protected String buildTargetDirectory(String fileType, String subType, String version) {
        // Ensure subType is not null/empty for path construction, use a default if necessary or validate earlier
        String safeSubType = StringUtils.isEmpty(subType) ? "default" : subType;
        return Paths.get(FileConstants.BASE_UPLOAD_PATH, currentDate, fileType, safeSubType, "V" + version).toString();
    }

    /**
     * 构建目标目录路径。
     * 基础结构: base_path/model/fileType/subType/Version
     * 子类可以通过重写此方法来自定义路径结构。
     *
     * @param drawingNo 图纸编号
     * @param fileType  文件类型
     * @param version   版本号
     * @return 目标目录路径字符串
     */
    protected String buildDrawingTargetDirectory(String drawingNo, String fileType, String version) {
        return Paths.get(FileConstants.BASE_UPLOAD_PATH, currentDate, fileType, drawingNo, "V" + version).toString();
    }

    /**
     * 保持原始文件名，并在基础名称后附加版本号 (_vX.Y)。
     *
     * @param originalFilename 原始文件名
     * @param version          版本号
     * @return 处理后的文件名
     */
    protected String preserveOriginalFilename(String originalFilename, String version) {
        if (StringUtils.isEmpty(originalFilename)) {
            // Provide a default name if original is missing
            return "uploaded_file_v" + version + ".bin"; // Or use a more specific default
        }
        String extension = FilenameUtils.getExtension(originalFilename);
        String baseName = FilenameUtils.getBaseName(originalFilename);
        // Ensure version format is safe for filename
        String safeVersion = version.replaceAll("[^a-zA-Z0-9.-]", "_");
        return String.format("%s_v%s.%s", baseName, safeVersion, extension);
    }

    /**
     * 处理单个文件上传，保存到指定目录和文件名。
     *
     * @param file      上传的文件
     * @param targetDir 目标目录
     * @param fileName  文件名
     * @return 保存后的文件路径
     * @throws IOException IO异常
     */
    protected Path processFileUpload(MultipartFile file, String targetDir, String fileName)
            throws IOException {
        Path directory = Paths.get(targetDir);
        // Use java.nio.file.Files for directory creation
        if (!java.nio.file.Files.exists(directory)) {
            try {
                java.nio.file.Files.createDirectories(directory);
            } catch (IOException e) {
                log.error("无法创建目录: {}", targetDir, e);
                throw new ServiceException("无法创建文件保存目录");
            }
        }
        Path filePath = directory.resolve(fileName);
        try {
            // Copy stream to target file, replacing if it exists
            java.nio.file.Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            log.error("无法保存文件 {} 到 {}: {}", fileName, filePath, e.getMessage(), e);
            throw new ServiceException("文件保存失败");
        }
        return filePath;
    }

    /**
     * 检查用户是否有指定机型的权限 (使用 SecurityUtils)。
     *
     * @param modelCode 机型代码
     */
    protected void checkModelPermission(String modelCode) {
        if (!SecurityUtils.isAdmin() && !getUserModelCodes().contains(modelCode)) {
            log.warn("用户 {} 尝试操作无权限的机型 {}", SecurityUtils.getUsername(), modelCode);
            // throw new ServiceException("无权操作该机型文件");
        }
    }

    /**
     * 获取用户可访问的机型代码列表 (基于角色)。
     *
     * @return 机型代码集合
     */
    protected Set<String> getUserModelCodes() {
        if (SecurityUtils.isAdmin()) {
            // Consider caching this list if ProductModels don't change often
            return productModelsService.selectProductModelsList(new ProductModels()).stream()
                    .map(ProductModels::getModelCode)
                    .collect(Collectors.toSet());
        }

        // Normal user: get models from roles
        return SecurityUtils.getLoginUser()
                .getUser()
                .getRoles()
                .stream()
                .map(role -> role.getRoleName())
                .filter(roleName -> roleName != null && roleName.startsWith(FileConstants.MODEL_ROLE_PREFIX))
                .map(roleName -> roleName.substring(FileConstants.MODEL_ROLE_PREFIX.length()))
                .collect(Collectors.toSet());
    }
}