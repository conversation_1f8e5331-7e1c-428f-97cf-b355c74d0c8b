package com.docs.files.service;

import java.util.List;

import com.docs.common.core.domain.entity.ApprovalRequestVO;
import com.docs.common.core.page.TableDataInfo;

/**
 * 文件下载审批服务接口 (位于 docs-files 模块, com.docs.files.service 包)
 */
public interface ApprovalService {

    /**
     * 提交文件下载申请
     *
     * @param fileId       文件ID
     * @param requesterId  申请人ID
     * @param reason       申请理由
     * @return 创建的审批请求ID (DownloadApprovalRequestsRecord.getId())
     * // @throws ServiceException 如果操作失败
     */
    Long submitRequest(Long fileId, Long requesterId, String reason);

    /**
     * 审批操作 - 批准
     *
     * @param approvalRequestId 审批请求ID
     * @param actorId          操作人（审批人）ID
     * @param comment          审批意见
     * // @throws ServiceException 如果操作失败
     */
    void approve(Long approvalRequestId, Long actorId, String comment);

    /**
     * 审批操作 - 拒绝
     *
     * @param approvalRequestId 审批请求ID
     * @param actorId          操作人（审批人）ID
     * @param comment          审批意见
     * // @throws ServiceException 如果操作失败
     */
    void reject(Long approvalRequestId, Long actorId, String comment);

    /**
     * 申请人撤销申请
     *
     * @param approvalRequestId 审批请求ID
     * @param actorId          操作人（申请人）ID
     * // @throws ServiceException 如果操作失败
     */
    void cancelRequest(Long approvalRequestId, Long actorId);

    /**
     * 获取审批请求详情及其操作记录
     *
     * @param approvalRequestId 审批请求ID
     * @return 审批请求详情，包含基本信息、相关人员信息和操作历史记录
     */
    ApprovalRequestVO getApprovalRequestDetails(Long approvalRequestId);

    /**
     * 分页查询指定审批人的待处理审批请求
     * 返回包含审批请求基本信息的分页数据对象
     *
     * @param approverId 审批人ID
     * @param pageNum    页码 (从1开始)
     * @param pageSize   每页数量
     * @return 分页数据对象，包含待处理审批请求VO列表以及分页信息(如总记录数)
     */
    TableDataInfo listPendingApprovalsByApprover(Long approverId, int pageNum, int pageSize);

    /**
     * 分页查询指定申请人的所有申请记录
     * 返回包含审批请求基本信息的分页数据对象
     *
     * @param requesterId 申请人ID
     * @param pageNum     页码 (从1开始)
     * @param pageSize    每页数量
     * @return 分页数据对象，包含申请请求VO列表以及分页信息(如总记录数)
     */
    TableDataInfo listUserRequestsByRequester(Long requesterId, int pageNum, int pageSize);

    /**
     * 分页查询已完成的审批记录
     * 根据用户角色返回不同范围的已完成审批记录：
     * - 普通用户：只返回自己的已完成审批记录
     * - 主管/经理：返回他们审批过的所有记录
     * - 管理员：返回所有已完成的审批记录
     *
     * @param userId    当前用户ID
     * @param pageNum   页码 (从1开始)
     * @param pageSize  每页数量
     * @param fileName  文件名称（可选，用于搜索）
     * @param requesterName 申请人名称（可选，用于搜索）
     * @param status    状态（可选，用于过滤特定状态的记录）
     * @return 分页数据对象，包含已完成审批记录VO列表以及分页信息
     */
    TableDataInfo listCompletedApprovals(Long userId, int pageNum, int pageSize, String fileName, String requesterName, String status);
}
