package com.docs.files.service.strategy.validator.impl;

import com.docs.common.exception.ServiceException;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.service.strategy.validator.FileValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 软件文件验证器
 */
@Component
public class SoftwareFileValidator implements FileValidator {
    private final CommonFileValidator commonValidator;
    
    public SoftwareFileValidator(CommonFileValidator commonValidator) {
        this.commonValidator = commonValidator;
    }
    
    @Override
    public void validate(FileUploadRequest request) {
        // 通用验证
        commonValidator.validate(request);
        
        // 软件特定验证，与原SoftwareFileStrategy.validateParams和validateFileCount保持一致
        if (request.getFiles() == null || request.getFiles().length != 1) {
            throw new ServiceException("请上传一个软件文件");
        }
        
        if (StringUtils.isEmpty(request.getSubType())) {
            throw new ServiceException("软件类型不能为空");
        }
    }
}
