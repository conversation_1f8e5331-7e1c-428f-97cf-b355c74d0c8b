package com.docs.files.service.impl;

import com.docs.common.core.domain.vo.FileChangeHistoryDTO;
import com.docs.common.jooq.generated.tables.daos.FileChangesDao;
import com.docs.common.jooq.generated.tables.daos.FilesDao;
import com.docs.common.jooq.generated.tables.pojos.FileChanges;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.files.service.FileChangesService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.docs.common.jooq.generated.Tables.FILES;
import static com.docs.common.jooq.generated.Tables.FILE_CHANGES;
@Service
@RequiredArgsConstructor
public class FileChangesServiceImpl  implements FileChangesService {

    private final FilesDao filesDao;
    private final FileChangesDao fileChangesDao;
    @Override
    public List<FileChanges> selectFileChangesList(FileChanges fileChanges) {
        return fileChangesDao.ctx().selectFrom(FILE_CHANGES)
            .where(FILE_CHANGES.FILE_ID.eq(fileChanges.getFileId()))
            .orderBy(FILE_CHANGES.CHANGED_AT.desc())
            .fetchInto(FileChanges.class);
    }

    /**
     * 根据BOM项ID查询变更记录列表
     *
     * @param bomItemId BOM项ID
     * @return 变更记录列表
     */
    private List<FileChanges> selectBomItemChangesList(Integer bomItemId) {
        return fileChangesDao.ctx().selectFrom(FILE_CHANGES)
            .where(FILE_CHANGES.BOM_ITEM_ID.eq(bomItemId))
            .orderBy(FILE_CHANGES.CHANGED_AT.desc())
            .fetchInto(FileChanges.class);
    }

    @Override
    public List<FileChangeHistoryDTO> selectFileChangesDetailList(FileChanges fileChanges) {
        // 1. 获取变更记录列表
        List<FileChanges> changesList = selectFileChangesList(fileChanges);
        if (changesList.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 获取所有相关文件ID
        List<Integer> fileIds = changesList.stream()
            .map(FileChanges::getFileId)
            .distinct()
            .collect(Collectors.toList());

        // 3. 获取文件信息
        List<Files> filesList = filesDao.ctx().selectFrom(FILES)
            .where(FILES.ID.in(fileIds))
            .fetchInto(Files.class);
        Map<Integer, Files> filesMap = filesList.stream()
            .collect(Collectors.toMap(Files::getId, file -> file, (existing, replacement) -> existing));

        // 4. 组装结果
        return changesList.stream().map(change -> {
            FileChangeHistoryDTO dto = new FileChangeHistoryDTO();
            // 复制变更记录基本信息
            dto.setId(change.getId())
               .setFileId(change.getFileId())
               .setOldVersion(change.getOldVersion())
               .setNewVersion(change.getNewVersion())
               .setChangeReason(change.getChangeReason())
               .setChangedBy(change.getChangedBy())
               .setChangedAt(change.getChangedAt());

            // 设置变更人姓名 - 简化处理，仅显示用户ID
            dto.setChangedByName("User ID: " + change.getChangedBy());

            // 设置文件信息
            Files file = filesMap.get(change.getFileId());
            if (file != null) {
                dto.setFileInfo(new FileChangeHistoryDTO.FileInfo()
                    .setId(file.getId())
                    .setFileName(file.getFileName())
                    .setFilePath(file.getFilePath())
                    .setFileType(file.getFileType().toString())
                    .setVersion(file.getVersion())
                    .setStatus(file.getStatus()));
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<FileChangeHistoryDTO> selectBomItemDrawingChangesList(Integer bomItemId) {
        // 1. 获取变更记录列表
        List<FileChanges> changesList = selectBomItemChangesList(bomItemId);
        if (changesList.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 获取所有相关文件ID
        List<Integer> fileIds = changesList.stream()
            .map(FileChanges::getFileId)
            .distinct()
            .collect(Collectors.toList());

        // 3. 获取文件信息
        List<Files> filesList = filesDao.ctx().selectFrom(FILES)
            .where(FILES.ID.in(fileIds))
            .fetchInto(Files.class);
        Map<Integer, Files> filesMap = filesList.stream()
            .collect(Collectors.toMap(Files::getId, file -> file, (existing, replacement) -> existing));

        // 4. 组装结果
        return changesList.stream().map(change -> {
            FileChangeHistoryDTO dto = new FileChangeHistoryDTO();
            // 复制变更记录基本信息
            dto.setId(change.getId())
               .setFileId(change.getFileId())
               .setOldVersion(change.getOldVersion())
               .setNewVersion(change.getNewVersion())
               .setChangeReason(change.getChangeReason())
               .setChangedBy(change.getChangedBy())
               .setChangedAt(change.getChangedAt())
               // 设置变更人姓名 - 简化处理，仅显示用户ID
               .setChangedByName("User ID: " + change.getChangedBy());

            // 设置文件信息
            Files file = filesMap.get(change.getFileId());
            if (file != null) {
                dto.setFileInfo(new FileChangeHistoryDTO.FileInfo()
                    .setId(file.getId())
                    .setFileName(file.getFileName())
                    .setFilePath(file.getFilePath())
                    .setFileType(file.getFileType().toString())
                    .setVersion(file.getVersion())
                    .setStatus(file.getStatus()));
            }
            return dto;
        }).collect(Collectors.toList());
    }
}
