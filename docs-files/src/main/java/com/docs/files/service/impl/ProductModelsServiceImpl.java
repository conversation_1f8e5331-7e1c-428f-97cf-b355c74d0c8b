package com.docs.files.service.impl;


import com.docs.common.jooq.generated.tables.daos.ProductModelsDao;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.common.jooq.generated.tables.records.ProductModelsRecord;
import com.docs.files.service.ProductModelsService;
import lombok.RequiredArgsConstructor;
import org.jooq.SelectWhereStep;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.docs.common.jooq.generated.Tables.PRODUCT_MODELS;


@Service
@RequiredArgsConstructor
public class ProductModelsServiceImpl  implements ProductModelsService {

    private final ProductModelsDao productModelsDao;

    @Override
    public List<ProductModels> selectProductModelsList(ProductModels productModel) {
        try (SelectWhereStep<ProductModelsRecord> query = productModelsDao.ctx().selectFrom(PRODUCT_MODELS)) {
            if (productModel != null) {
                if (productModel.getModelCode() != null && !productModel.getModelCode().isEmpty()) {
                    query.where(PRODUCT_MODELS.MODEL_CODE.like("%" + productModel.getModelCode() + "%"));
                }

                if (productModel.getStatus() != null) {
                    query.where(PRODUCT_MODELS.STATUS.eq(productModel.getStatus()));
                }
            }

            return query.fetchInto(ProductModels.class);
        }
    }

    @Override
    public boolean checkModelExists(String modelCode) {
        List<ProductModels> productModels = productModelsDao.fetchByModelCode(modelCode);
        return productModels != null && !productModels.isEmpty();
    }

    @Override
    public int insertProductModel(ProductModels productModels) {
        productModelsDao.insert(productModels);
        return 1;
    }

    @Override
    public ProductModels selectByModelCode(String modelCode) {
        return productModelsDao.fetchOneByModelCode(modelCode);
    }

    @Override
    public ProductModels selectByPrimaryKey(Integer productModelId) {
        return productModelsDao.fetchOneById(productModelId);
    }
}
