package com.docs.files.service.impl;

import com.alibaba.excel.EasyExcel;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.tables.daos.MaterialDrawingMapDao;
import com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap;
import com.docs.files.listener.RelationExcelListener;
import com.docs.files.service.MaterialDrawingRelationsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class MaterialDrawingRelationsServiceImpl implements MaterialDrawingRelationsService {

    private final MaterialDrawingMapDao materialDrawingMapDao;

    @Override
    public List<MaterialDrawingMap> parseRelationExcel(String filePath, Integer fileId) {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new ServiceException("文件不存在: " + filePath);
        }

        // 创建监听器
        RelationExcelListener listener = new RelationExcelListener();

        // 使用EasyExcel读取Excel文件，添加配置
        EasyExcel.read(file, listener)
                .sheet()
                .headRowNumber(0)  // 设置表头行号为1
                .doRead();

        return listener.getRelations();
    }

    @Override
    public void batchInsert(List<MaterialDrawingMap> relations) {
        materialDrawingMapDao.insert(relations);
    }


}
