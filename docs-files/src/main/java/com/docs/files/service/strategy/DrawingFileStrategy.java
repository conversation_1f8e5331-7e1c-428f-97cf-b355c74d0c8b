package com.docs.files.service.strategy;

import com.docs.common.constant.FileConstants;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.tables.pojos.Files;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 图纸文件处理策略，处理图纸文件的上传
 */
@Slf4j
@Component
public class DrawingFileStrategy extends AbstractFileStrategy {

    private final String fileType = FilesFileType.DRAWING.name();
    /**
     * 获取支持的文件类型
     *
     * @return 文件类型
     */
    @Override
    public String getSupportedFileType() {
        return fileType;
    }

    /**
     * 获取文件类型 (Required by Abstract Class)
     *
     * @return 文件类型
     */
    @Override
    public String getFileType() {
        return fileType;
    }

    /**
     * 验证文件数量
     *
     * @param files 上传的文件数组
     */
    @Override
    public void validateFileCount(MultipartFile[] files) {
        if (files == null || files.length != 1) {
            throw new ServiceException("请上传一个图纸文件");
        }
    }

    /**
     * 验证上传参数 (Specific for Drawing)
     *
     * @param productModel 产品型号
     * @param fileType 文件类型
     * @param subType 子类型
     * @param drawingNo 图纸编号
     * @param version 版本号
     */
    @Override
    public void validateParams(String productModel, String fileType,
                             String subType, String drawingNo, String version) {
        super.validateParams(productModel, fileType, subType, drawingNo, version); // Call common validation first

        if (StringUtils.isEmpty(drawingNo)) {
            throw new ServiceException("图纸编号不能为空");
        }

        // 验证图纸子类型
        if (StringUtils.isEmpty(subType)) {
            throw new ServiceException("图纸类型不能为空");
        }
        if (!FileConstants.VALID_DRAWING_SUB_TYPES.contains(subType)) {
            throw new ServiceException("无效的图纸类型: " + subType);
        }
    }

    /**
     * 重写文件验证逻辑，支持根据图纸子类型验证文件格式
     *
     * @param files 上传的文件数组
     * @param subType 图纸子类型
     * @return 验证结果
     */
    public boolean validateFilesBySubType(MultipartFile[] files, String subType) {
        if (files == null || files.length != 1) {
            return false;
        }

        MultipartFile file = files[0];
        if (file.isEmpty()) {
            return false;
        }

        return isValidDrawingFileExtension(file.getOriginalFilename(), subType);
    }

    /**
     * 设置特定文件类型的额外信息 (Specific for Drawing)
     *
     * @param fileInfo 文件信息对象
     * @param drawingNo 图纸编号
     */
    @Override
    public void setAdditionalFileInfo(Files fileInfo, String drawingNo) {
        fileInfo.setDrawingNo(drawingNo);
    }
} 