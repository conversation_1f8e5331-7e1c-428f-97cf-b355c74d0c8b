package com.docs.files.service.strategy.validator.impl;

import com.docs.common.exception.ServiceException;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.service.strategy.validator.FileValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 图纸文件验证器
 */
@Component
public class DrawingFileValidator implements FileValidator {
    private final CommonFileValidator commonValidator;
    
    public DrawingFileValidator(CommonFileValidator commonValidator) {
        this.commonValidator = commonValidator;
    }
    
    @Override
    public void validate(FileUploadRequest request) {
        // 通用验证
        commonValidator.validate(request);
        
        // 图纸特定验证，与原DrawingFileStrategy.validateParams和validateFileCount保持一致
        if (request.getFiles() == null || request.getFiles().length != 1) {
            throw new ServiceException("请上传一个图纸文件");
        }
        
        if (StringUtils.isEmpty(request.getDrawingNo())) {
            throw new ServiceException("图纸编号不能为空");
        }
    }
}
