package com.docs.files.service.security.impl;

import com.docs.common.constant.FileConstants;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.common.utils.SecurityUtils;
import com.docs.files.service.ProductModelsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * 安全服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecurityServiceImpl {
    private final ProductModelsService productModelsService;
    
    /**
     * 检查用户是否有指定机型的权限
     * 与原AbstractFileStrategy.checkModelPermission保持一致
     * 
     * @param modelCode 机型代码
     */
    public void checkModelPermission(String modelCode) {
        if (!SecurityUtils.isAdmin() && !getUserModelCodes().contains(modelCode)) {
            log.warn("用户 {} 尝试操作无权限的机型 {}", SecurityUtils.getUsername(), modelCode);
            // throw new ServiceException("无权操作该机型文件");
        }
    }
    
    /**
     * 获取用户可访问的机型代码列表
     * 与原AbstractFileStrategy.getUserModelCodes保持一致
     * 
     * @return 机型代码集合
     */
    public Set<String> getUserModelCodes() {
        if (SecurityUtils.isAdmin()) {
            return productModelsService.selectProductModelsList(new ProductModels()).stream()
                    .map(ProductModels::getModelCode)
                    .collect(Collectors.toSet());
        }

        return SecurityUtils.getLoginUser()
                .getUser()
                .getRoles()
                .stream()
                .map(role -> role.getRoleName())
                .filter(roleName -> roleName != null && roleName.startsWith(FileConstants.MODEL_ROLE_PREFIX))
                .map(roleName -> roleName.substring(FileConstants.MODEL_ROLE_PREFIX.length()))
                .collect(Collectors.toSet());
    }
}
