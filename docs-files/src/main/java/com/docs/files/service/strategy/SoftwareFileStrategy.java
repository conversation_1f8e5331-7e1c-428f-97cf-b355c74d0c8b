package com.docs.files.service.strategy;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.enums.FilesStatus;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;

/**
 * 软件文件处理策略，处理软件文件的上传
 */
@Slf4j
@Component
public class SoftwareFileStrategy extends AbstractFileStrategy {

    private final String fileType = FilesFileType.SOFTWARE.name();

    /**
     * 获取支持的文件类型
     *
     * @return 文件类型
     */
    @Override
    public String getSupportedFileType() {
        return fileType;
    }

    /**
     * 获取文件类型
     *
     * @return 文件类型
     */
    @Override
    public String getFileType() {
        return fileType;
    }

    /**
     * 验证文件数量
     *
     * @param files 上传的文件数组
     */
    @Override
    public void validateFileCount(MultipartFile[] files) {
        if (files == null || files.length != 1) {
            throw new ServiceException("请上传一个软件文件");
        }
    }

    /**
     * 验证上传参数
     *
     * @param productModel 产品型号
     * @param fileType 文件类型
     * @param subType 子类型
     * @param drawingNo 图纸编号
     * @param version 版本号
     */
    @Override
    public void validateParams(String productModel, String fileType,
                             String subType, String drawingNo, String version) {
        super.validateParams(productModel, fileType, subType, drawingNo, version);

        if (StringUtils.isEmpty(subType)) {
            throw new ServiceException("软件类型不能为空");
        }
    }

    /**
     * 处理文件上传
     *
     * @param files 上传的文件数组
     * @param productModel 产品型号
     * @param fileType 文件类型
     * @param subType 子类型
     * @param drawingNo 图纸编号
     * @param version 版本号
     * @return 处理结果
     */
    @Override
    public AjaxResult processFiles(MultipartFile[] files, String productModel, String fileType,
                                 String subType, String drawingNo, String version) {
        checkModelPermission(productModel);
        try {
            if (files == null || files.length != 1) {
                return AjaxResult.error("请上传一个软件文件");
            }

            validateParams(productModel, fileType, subType, drawingNo, version);

            ProductModels model = productModelsService.selectByModelCode(productModel);
            if (model == null) {
                return AjaxResult.error("产品型号不存在");
            }

            MultipartFile softwareFile = files[0];
            if (softwareFile.isEmpty() || !isValidFileExtension(softwareFile.getOriginalFilename())) {
                return AjaxResult.error("软件文件无效");
            }

            Files existingFile = super.selectByVersion(model.getId(), fileType, subType, version);
            if (existingFile != null) {
                return AjaxResult.error("该版本号" + version + "已存在，请使用其他版本号");
            }

            String targetDir = buildTargetDirectory(fileType, subType, version);
            String fileName = preserveOriginalFilename(softwareFile.getOriginalFilename(), version);

            Path filePath = processFileUpload(softwareFile, targetDir, fileName);

            Files fileInfo = new Files()
                .setProductModelId(model.getId())
                .setFileType(FilesFileType.valueOf(fileType))
                .setSubType(subType)
                .setVersion(version)
                .setStatus(FilesStatus.ACTIVE)
                .setFilePath(filePath.toString());

            filesDao.insert(fileInfo);

            return AjaxResult.success("上传成功");
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException("文件上传失败");
        }
    }
}