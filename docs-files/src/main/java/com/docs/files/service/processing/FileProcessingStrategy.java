package com.docs.files.service.processing;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;

/**
 * 文件处理策略接口
 * 定义不同类型文件的特殊处理逻辑
 */
public interface FileProcessingStrategy {
    /**
     * 图纸文件类型常量
     */
    String DRAWING_FILE_TYPE = "DRAWING";
    
    /**
     * BOM文件类型常量
     */
    String BOM_FILE_TYPE = "BOM";
    
    /**
     * 软件文件类型常量
     */
    String SOFTWARE_FILE_TYPE = "SOFTWARE";
    
    /**
     * 获取策略支持的文件类型
     * @return 文件类型
     */
    String getFileType();
    
    /**
     * 验证文件上传请求
     * @param request 文件上传请求
     * @return 验证结果，true表示验证通过
     */
    boolean validateRequest(FileUploadRequest request);
    
    /**
     * 验证文件内容
     * @param file 文件
     * @return 验证结果，true表示验证通过
     */
    boolean validateFile(MultipartFile file);
    
    /**
     * 检查版本是否存在
     * @param request 文件上传请求
     * @return 版本检查结果，null表示版本不存在，否则返回已存在的文件
     */
    Files checkVersionExists(FileUploadRequest request);
    
    /**
     * 构建目标路径
     * @param request 文件上传请求
     * @return 目标路径
     */
    String buildTargetDirectory(FileUploadRequest request);
    
    /**
     * 处理文件上传后的特殊逻辑
     * @param fileInfo 文件信息
     * @param filePaths 文件路径列表
     * @param model 产品型号
     * @param request 文件上传请求
     * @return 处理结果
     */
    AjaxResult postProcess(Files fileInfo, Path[] filePaths, ProductModels model, FileUploadRequest request);
}
