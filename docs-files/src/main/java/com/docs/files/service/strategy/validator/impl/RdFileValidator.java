package com.docs.files.service.strategy.validator.impl;

import com.docs.common.exception.ServiceException;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.service.strategy.validator.FileValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 研发文件验证器
 * 研发文件是独立于图纸的文件类型，验证规则相对宽松
 */
@Component
public class RdFileValidator implements FileValidator {
    private final CommonFileValidator commonValidator;
    
    public RdFileValidator(CommonFileValidator commonValidator) {
        this.commonValidator = commonValidator;
    }
    
    @Override
    public void validate(FileUploadRequest request) {
        // 通用验证
        commonValidator.validate(request);
        
        // 研发文件特定验证
        if (request.getFiles() == null || request.getFiles().length != 1) {
            throw new ServiceException("请上传一个研发文件");
        }
        
        // 研发文件的子类型是可选的，可以为空
        if (StringUtils.isNotBlank(request.getSubType()) && request.getSubType().length() > 20) {
            throw new ServiceException("子类型长度不能超过20个字符");
        }
        
        // 验证版本号格式（研发文件版本号格式相对灵活）
        if (StringUtils.isBlank(request.getVersion())) {
            throw new ServiceException("版本号不能为空");
        }
        
        if (request.getVersion().trim().length() > 50) {
            throw new ServiceException("版本号长度不能超过50个字符");
        }
    }
}
