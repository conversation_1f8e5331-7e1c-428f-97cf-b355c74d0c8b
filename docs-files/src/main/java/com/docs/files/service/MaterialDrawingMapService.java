package com.docs.files.service;

import com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public interface MaterialDrawingMapService {
    /**
     * upsert 物料-图纸映射关系
     * @param materialCode 物料编码
     * @param drawingNo 图纸编号
     * @param drawingFileId 图纸文件ID
     * @param materialName 物料名称
     * @param specification 型号规格
     */
    @Transactional
    void upsertMaterialDrawingMap(String materialCode, String drawingNo, Integer drawingFileId, String materialName, String specification);

    /**
     * upsert 物料-图纸映射关系（多文件支持）
     * @param materialCode 物料编码
     * @param drawingNo 图纸编号
     * @param pdfFileId PDF文件ID
     * @param drawing2dFileId 2D图纸文件ID
     * @param drawing3dFileId 3D图纸文件ID
     * @param compressedFileId 压缩包文件ID
     * @param materialName 物料名称
     * @param specification 型号规格
     */
    @Transactional
    void upsertMaterialDrawingMapMultiFiles(String materialCode, String drawingNo, 
                                          Integer pdfFileId, Integer drawing2dFileId, 
                                          Integer drawing3dFileId, Integer compressedFileId,
                                          String materialName, String specification);

    /**
     * 更新物料-图纸映射关系
     * @param materialCode 物料编码
     * @param drawingNo 图纸编号
     * @param drawingFileId 图纸文件ID
     */
    @Transactional
    void updateMaterialDrawingMap(String materialCode, String drawingNo, Integer drawingFileId);

    /**
     * 根据物料编码列表批量查询物料-图纸映射关系
     * @param materialCodes 物料编码列表
     * @return 物料编码到物料-图纸映射的映射
     */
    Map<String, MaterialDrawingMap> findByMaterialCodes(List<String> materialCodes);

    /**
     * 批量处理物料-图纸映射关系（更新或插入）
     * @param relations 物料-图纸映射关系列表
     * @return 处理成功的记录数
     */
    int batchUpsertFromRelations(List<MaterialDrawingMap> relations);

    /**
     * 根据图纸编号查询物料-图纸映射关系
     * @param drawingNo 图纸编号
     * @return 物料-图纸映射关系，如果不存在返回 null
     */
    MaterialDrawingMap findByDrawingNo(String drawingNo);

    /**
     * 根据物料编码查询物料-图纸映射关系
     * @param materialCode 物料编码
     * @return 物料-图纸映射关系，如果不存在返回 null
     */
    MaterialDrawingMap findByMaterialCode(String materialCode);

    /**
     * 解析物料-图纸关系Excel文件
     * @param filePath 文件路径
     * @param fileId 文件ID
     * @return 解析出的物料-图纸关系列表
     */
    List<MaterialDrawingMap> parseRelationExcel(String filePath, Integer fileId);

    /**
     * 验证图纸编号和物料编号的唯一性
     * @param materialCode 物料编码
     * @param drawingNo 图纸编号
     * @throws com.docs.common.exception.ServiceException 如果违反唯一性约束
     */
    void validateUniqueness(String materialCode, String drawingNo);

    /**
     * 更新物料的图纸编号
     * @param materialCode 物料编码
     * @param newDrawingNo 新图纸编号
     * @throws com.docs.common.exception.ServiceException 如果物料不存在或图纸编号冲突
     */
    @Transactional
    void updateDrawingNumber(String materialCode, String newDrawingNo);

    /**
     * 获取物料已存在的文件类型
     * @param materialCode 物料编码
     * @return 已存在的文件类型列表
     */
    List<String> getExistingFileTypes(String materialCode);

}
