package  com.docs.files.service.strategy.validator.impl;

import com.docs.common.exception.ServiceException;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.service.strategy.validator.FileValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 通用文件验证器
 */
@Component
public class CommonFileValidator implements FileValidator {

    @Override
    public void validate(FileUploadRequest request) {
        // 基本验证，与原AbstractFileStrategy.validateParams保持一致
//        if (StringUtils.isEmpty(request.getProductModel())) {
//            throw new ServiceException("产品型号不能为空");
//        }
        if (StringUtils.isEmpty(request.getFileType())) {
            throw new ServiceException("文件类型不能为空");
        }
        if (StringUtils.isEmpty(request.getVersion())) {
            throw new ServiceException("版本号不能为空");
        }
    }
}
