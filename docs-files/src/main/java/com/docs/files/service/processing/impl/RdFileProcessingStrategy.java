package com.docs.files.service.processing.impl;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.files.service.processing.FileProcessingStrategy;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.util.FilePathUtil;
import com.docs.files.util.FileProcessorHelper;
import com.docs.files.service.strategy.validator.FileValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;

/**
 * 研发文件处理策略
 * 研发文件是独立于图纸的文件类型，支持所有文件格式
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RdFileProcessingStrategy implements FileProcessingStrategy {
    private final FileValidator rdFileValidator;
    private final FileProcessorHelper fileProcessorHelper;
    private final FilePathUtil filePathUtil;
    
    @Override
    public String getFileType() {
        return RD_FILE_TYPE;
    }
    
    @Override
    public boolean validateRequest(FileUploadRequest request) {
        try {
            rdFileValidator.validate(request);
            return true;
        } catch (Exception e) {
            log.warn("研发文件请求验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.warn("研发文件为空");
            return false;
        }
        
        // 检查文件大小限制（100MB）
        long maxSize = 100 * 1024 * 1024; // 100MB
        if (file.getSize() > maxSize) {
            log.warn("研发文件大小超过限制: {}MB", file.getSize() / 1024 / 1024);
            return false;
        }
        
        return true;
    }
    
    @Override
    public Files checkVersionExists(FileUploadRequest request) {
        // 研发文件以fileType+version+fileName为唯一性校验
        // 获取产品型号ID
        Integer modelId = null;
        if (request.getProductModel() != null) {
            try {
                modelId = Integer.parseInt(request.getProductModel());
            } catch (NumberFormatException e) {
                // 如果不是数字，可能是型号代码，需要查询获取ID
                return null; // 暂时返回null，表示版本不存在
            }
        }
        return fileProcessorHelper.selectByVersion(modelId, getFileType(), request.getSubType(), request.getVersion());
    }
    
    @Override
    public String buildTargetDirectory(FileUploadRequest request) {
        // 研发文件使用独立的目录结构
        return filePathUtil.buildRdFileTargetDirectory(request.getProductModel(), request.getVersion());
    }
    
    @Override
    public AjaxResult postProcess(Files fileInfo, Path[] filePaths, ProductModels model, FileUploadRequest request) {
        // 研发文件没有特殊的后处理逻辑，只需要记录上传成功
        log.info("研发文件上传成功: 产品型号={}, 文件名={}, 版本={}", 
                model.getModelCode(), fileInfo.getFileName(), fileInfo.getVersion());
        
        return AjaxResult.success("研发文件上传成功", fileInfo);
    }
}
