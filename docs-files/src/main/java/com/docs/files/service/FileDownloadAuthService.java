package com.docs.files.service;

/**
 * 文件下载权限服务接口
 * 负责检查用户是否有权限下载特定文件
 */
public interface FileDownloadAuthService {

    /**
     * 权限检查结果枚举
     */
    enum PermissionResult {
        ALLOWED,           // 允许下载
        NEED_APPROVAL,     // 需要审批
        NO_PERMISSION      // 无权限
    }

    /**
     * 检查用户是否有权限下载指定文件
     *
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return 如果有权限返回true，否则返回false
     */
    boolean hasDownloadPermission(Long fileId, Long userId);

    /**
     * 检查用户下载文件的权限状态
     *
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return 权限检查结果
     */
    PermissionResult checkDownloadPermissionStatus(Long fileId, Long userId);

    /**
     * 检查用户是否有权限下载指定图纸子类型
     *
     * @param subType 图纸子类型 (PDF, 2D, 3D, 2D/3D)
     * @param userId 用户ID
     * @return 如果有权限返回true，否则返回false
     */
    boolean hasDrawingSubTypeDownloadPermission(String subType, Long userId);

    /**
     * 记录文件下载操作
     *
     * @param fileId 文件ID
     * @param userId 用户ID
     */
    void recordDownloadAction(Long fileId, Long userId);
}
