package com.docs.files.service.processing;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.tables.daos.FilesDao;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.files.service.security.impl.SecurityServiceImpl;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.util.FilePathUtil;
import com.docs.files.util.FileProcessorHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * 统一的文件处理服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileProcessingService {
    private final FileProcessingStrategyRegistry strategyRegistry;
    private final SecurityServiceImpl securityService;
    private final FilePathUtil filePathUtil;
    private final FileProcessorHelper fileProcessorHelper;
    private final FilesDao filesDao;

    /**
     * 处理文件上传请求
     * @param request 文件上传请求
     * @return 处理结果
     */
    public AjaxResult processUpload(FileUploadRequest request) {
        // 1. 检查权限
        if(StringUtils.isNotBlank(request.getProductModel())) {
            securityService.checkModelPermission(request.getProductModel());
        }
        try {
            // 2. 获取文件类型对应的处理策略
            String fileType = request.getFileType();
            FileProcessingStrategy strategy = strategyRegistry.getStrategy(fileType);

            // 3. 验证请求
            if (!strategy.validateRequest(request)) {
                return AjaxResult.error("请求验证失败");
            }

            // 4. 验证文件
            MultipartFile[] files = request.getFiles();
            for (MultipartFile file : files) {
                if (!strategy.validateFile(file) || !filePathUtil.isValidFileExtension(file.getOriginalFilename())) {
                    return AjaxResult.error("无效或不支持的文件类型");
                }
            }

            // 5. 获取产品型号
            ProductModels model = null;
            if (!StringUtils.isEmpty(request.getProductModel())) {
                model = fileProcessorHelper.getProductModel(request.getProductModel());

            }
            // 6. 检查文件是否存在（用于判断更新还是新增）
            Files existingFile = null;
            if (model != null) {
                existingFile = fileProcessorHelper.checkFileExists(
                    model.getId(), 
                    request.getFileType(), 
                    request.getSubType(), 
                    files[0].getOriginalFilename()
                );
            }
            
            // 如果是相同文件，则进行更新操作；如果是新文件，继续检查版本冲突
            boolean isUpdate = (existingFile != null);
            if (!isUpdate) {
                Files versionConflict = strategy.checkVersionExists(request);
                if (versionConflict != null) {
                    return AjaxResult.error("该版本已存在，请使用其他版本号");
                }
            }
            // 7. 构建目标路径
            String targetDir = strategy.buildTargetDirectory(request);

            // 8. 保存文件
            List<Path> filePaths = new ArrayList<>();
            List<String> fileNames = new ArrayList<>();

            for (MultipartFile file : files) {
                if(request.getFileName() == null) {
                    request.setFileName(file.getOriginalFilename());
                }
                String fileName = filePathUtil.preserveOriginalFilename(file.getOriginalFilename(), request.getVersion());
                Path filePath = filePathUtil.storeFile(file, targetDir, fileName);
                filePaths.add(filePath);
                fileNames.add(file.getOriginalFilename());
            }

            // 9. 保存文件信息到数据库
            Files fileInfo;
            if (isUpdate) {
                // 更新现有文件
                boolean updateSuccess = fileProcessorHelper.updateExistingFile(
                    existingFile, filePaths.get(0), fileNames.get(0));
                if (!updateSuccess) {
                    return AjaxResult.error("更新现有文件失败");
                }
                fileInfo = existingFile;
                log.info("更新现有文件: fileId={}, fileName={}", fileInfo.getId(), fileNames.get(0));
            } else {
                // 创建新文件
                fileInfo = fileProcessorHelper.createFileInfo(
                    model, fileType, request.getSubType(), request.getVersion(),
                    filePaths.get(0), fileNames.get(0));

                // 如果是图纸文件，设置图纸编号
                if (fileType.equals(FileProcessingStrategy.DRAWING_FILE_TYPE)) {
                    fileInfo.setDrawingNo(request.getDrawingNo());
                }

                filesDao.insert(fileInfo);
                log.info("创建新文件: fileId={}, fileName={}", fileInfo.getId(), fileNames.get(0));
            }

            // 10. 执行文件类型特定的后处理逻辑
            return strategy.postProcess(fileInfo, filePaths.toArray(new Path[0]), model, request);

        } catch (ServiceException e) {
            throw e;
        } catch (org.springframework.dao.DuplicateKeyException e) {
            log.warn("文件重复上传: {}", e.getMessage());
            // 解析重复键异常，提供更友好的错误信息
            String errorMessage = parseDuplicateKeyError(e.getMessage(), request);
            throw new ServiceException(errorMessage);
        } catch (Exception e) {
            log.error("处理文件上传时发生意外错误", e);
            throw new ServiceException("文件上传处理失败，请联系管理员");
        }
    }

    /**
     * 解析重复键异常，生成用户友好的错误信息
     */
    private String parseDuplicateKeyError(String errorMessage, FileUploadRequest request) {
        if (errorMessage.contains("uk_filetype_filename_version")) {
            // 这是文件类型-文件名-版本的唯一约束冲突
            String fileType = request.getFileType() != null ? request.getFileType() : "文件";
            String fileName = request.getFileName() != null ? request.getFileName() : "该文件";
            String version = request.getVersion() != null ? request.getVersion() : "该版本";
            
            if ("DRAWING".equals(request.getFileType())) {
                return String.format("图纸文件 '%s' 的版本 '%s' 已经存在。请修改版本号或检查是否重复上传。", fileName, version);
            } else if ("BOM".equals(request.getFileType())) {
                return String.format("BOM文件 '%s' 的版本 '%s' 已经存在。请修改版本号或检查是否重复上传。", fileName, version);
            } else {
                return String.format("文件 '%s' 的版本 '%s' 已经存在。请修改版本号或检查是否重复上传。", fileName, version);
            }
        } else if (errorMessage.contains("uk_drawing_no_version")) {
            // 这是图纸编号-版本的唯一约束冲突
            String drawingNo = request.getDrawingNo() != null ? request.getDrawingNo() : "该图纸编号";
            String version = request.getVersion() != null ? request.getVersion() : "该版本";
            return String.format("图纸编号 '%s' 的版本 '%s' 已经存在。请修改图纸编号或版本号。", drawingNo, version);
        } else {
            // 其他类型的重复键异常
            return "文件已存在，请检查文件名、版本号或图纸编号是否重复。";
        }
    }
}
