package com.docs.files.repository;

import com.docs.common.core.domain.entity.FilesEntity;
import com.docs.common.core.domain.entity.SysRole;
import com.docs.common.core.domain.vo.BomItemsVO;
import com.docs.common.core.domain.vo.FileListVO;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.enums.FilesStatus;
import com.docs.common.jooq.generated.tables.daos.FileChangesDao;
import com.docs.common.jooq.generated.tables.daos.FilesDao;
import com.docs.common.jooq.generated.tables.pojos.FileChanges;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.common.utils.SecurityUtils;
import com.docs.common.utils.TreeUtils;
import com.docs.files.service.BomItemService;
import com.docs.files.service.MaterialDrawingMapService;
import com.docs.files.service.ProductModelsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.docs.common.constant.FileConstants.MODEL_ROLE_PREFIX;
import static com.docs.common.jooq.generated.Tables.*;

/**
 * 文件数据访问仓库
 * 负责与文件及其关联表的数据库交互
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class FileRepository {


    private final FilesDao filesDao;
    private final ProductModelsService productModelsService;
    private final BomItemService bomItemService;
    private final MaterialDrawingMapService materialDrawingMapService;
    private final FileChangesDao fileChangesDao;



    @Transactional(rollbackFor = Exception.class)
    public int deleteFile(Integer fileId) {
        // 检查文件信息并验证权限
        com.docs.common.jooq.generated.tables.pojos.Files fileInfo = filesDao.fetchOneById(fileId);
        if (fileInfo == null) {
            throw new ServiceException("文件不存在");
        }
        // 1. 删除变更记录
        FileChanges changes = new FileChanges();
        changes.setFileId(fileId);
        List<FileChanges> changesList = fileChangesDao.fetchByFileId(fileId);
        for (FileChanges change : changesList) {
            fileChangesDao.deleteById(change.getId());
        }

        // 2. 删除文件记录
        filesDao.deleteById(fileId);

        // 3. 删除BOM数据
        bomItemService.deleteBomitemsByFileId(fileId);
        return 1;
    }

    public Files fetchFileById(Integer fileId) {
        return filesDao.fetchOneById(fileId);
    }

    /**
     * 查询文件列表
     *
     * @param filesEntity 文件查询参数
     * @return 文件列表视图对象
     */
    public List<FileListVO> selectFilesList(FilesEntity filesEntity) {
        // 1. 构建并执行基础查询
        List<FileListVO> resultList = executeBaseQuery(filesEntity);
        if (resultList.isEmpty()) {
            return resultList;
        }

        // 2. 增强查询结果
        enrichFileListResults(resultList, filesEntity);

        return resultList;
    }

    /**
     * 执行基础文件查询
     *
     * @param filesEntity 查询参数
     * @return 文件列表结果
     */
    private List<FileListVO> executeBaseQuery(FilesEntity filesEntity) {
        DSLContext dsl = filesDao.ctx();

        // 构建基础查询
        SelectJoinStep<? extends Record> query = dsl
                .select(FILES.ID, FILES.FILE_NAME, FILES.PRODUCT_MODEL_ID, FILES.FILE_TYPE,
                        FILES.SUB_TYPE, FILES.VERSION, FILES.STATUS, FILES.FILE_PATH,
                        FILES.CREATED_AT, FILES.UPDATED_AT, FILES.DRAWING_NO,
                        FILES.BOM_HEADER, FILES.BOM_SUBHEADER, PRODUCT_MODELS.MODEL_CODE)
                .from(FILES)
                .leftJoin(PRODUCT_MODELS).on(PRODUCT_MODELS.ID.eq(FILES.PRODUCT_MODEL_ID));

        // 应用查询条件
        SelectConditionStep<? extends Record> whereClause = query.where();
        whereClause = applyFileFilter(whereClause, filesEntity);

        // 执行查询并返回结果
        return whereClause
                .orderBy(FILES.CREATED_AT.desc())
                .fetchInto(FileListVO.class);
    }

    /**
     * 应用文件过滤条件
     */
    private SelectConditionStep<? extends Record> applyFileFilter(SelectConditionStep<? extends Record> whereClause, FilesEntity filesEntity) {
        if (filesEntity == null) {
            return whereClause;
        }

        // 文件ID过滤 - 添加对fileId参数的支持
        if (filesEntity.getId() != null) {
            whereClause = whereClause.and(FILES.ID.eq(filesEntity.getId()));
            // 如果指定了文件ID，则直接返回，不应用其他过滤条件
            return whereClause;
        }

        // 排除BOM关联图纸文件，这些文件只应在BOM上下文中可见
        whereClause = whereClause.and(FILES.FILE_TYPE.ne(FilesFileType.BOM_DRAWING));

        // 产品型号ID过滤
        if (filesEntity.getProductModelId() != null) {
            whereClause = whereClause.and(FILES.PRODUCT_MODEL_ID.eq(filesEntity.getProductModelId()));
        }

        // 文件类型过滤
        if (StringUtils.isNotBlank(filesEntity.getFileType())) {
            try {
                whereClause = whereClause.and(FILES.FILE_TYPE.eq(FilesFileType.valueOf(filesEntity.getFileType())));
            } catch (IllegalArgumentException e) {
                log.warn("查询中提供了无效的文件类型: {}", filesEntity.getFileType());
            }
        }

        // 子类型过滤
        if (StringUtils.isNotBlank(filesEntity.getSubType())) {
            whereClause = whereClause.and(FILES.SUB_TYPE.eq(filesEntity.getSubType()));
        }

        // 状态过滤
        if (StringUtils.isNotBlank(filesEntity.getStatus())) {
            try {
                whereClause = whereClause.and(FILES.STATUS.eq(FilesStatus.valueOf(filesEntity.getStatus())));
            } catch (IllegalArgumentException e) {
                log.warn("查询中提供了无效的状态: {}", filesEntity.getStatus());
            }
        }

        // 图纸编号过滤已移至应用层，与物料编号过滤逻辑保持一致

        return whereClause;
    }

    /**
     * 增强文件列表结果 (优化版)
     * 使用批量获取数据提高性能
     *
     * @param resultList 文件列表
     * @param filesEntity      查询参数
     */
    private void enrichFileListResults(List<FileListVO> resultList, FilesEntity filesEntity) {
        if (resultList == null || resultList.isEmpty()) {
            return;
        }
        long startTime = System.currentTimeMillis();
        log.debug("[EnrichResults] 开始增强 {} 个文件列表结果", resultList.size());

        // 1. 批量获取文件变更历史
        Set<Integer> allFileIds = resultList.stream()
                .map(FileListVO::getId)
                .collect(Collectors.toSet());
        Map<Integer, Boolean> fileChangesMap = getFileChangesMap(allFileIds);
        log.debug("[EnrichResults] 获取变更历史耗时: {}ms", System.currentTimeMillis() - startTime);

        // 2. 处理物料编号和图纸编号过滤
        final String materialNo = filesEntity != null ? filesEntity.getMaterialNo() : null;
        final String drawingNo = filesEntity != null ? filesEntity.getDrawingNo() : null;
        final boolean hasMaterialNoFilter = StringUtils.isNotBlank(materialNo);
        final boolean hasDrawingNoFilter = StringUtils.isNotBlank(drawingNo);

        for (FileListVO vo : resultList) {
            // 设置文件是否有变更历史
            vo.setHasChanges(fileChangesMap.getOrDefault(vo.getId(), false));

            if (com.docs.common.jooq.generated.enums.FilesFileType.BOM.equals(vo.getFileType())) {
                List<BomItemsVO> flatBomItems = bomItemService.selectByFileId(vo.getId());

                if (hasMaterialNoFilter) {
                    // 使用物料编号过滤方法
                    vo.setBomItems(filterAndBuildBomTree(flatBomItems, materialNo));
                } else if (hasDrawingNoFilter) {
                    // 使用图纸编号过滤方法
                    vo.setBomItems(filterAndBuildBomTreeByDrawingNo(flatBomItems, drawingNo));
                } else {
                    // 使用常规树构建
                    vo.setBomItems(buildBomTree(flatBomItems));
                }
            }
        }

        log.info("[EnrichResults] 文件列表结果增强完成 | 文件总数: {}, 总耗时: {}ms",
                resultList.size(), System.currentTimeMillis() - startTime);
    }

    /**
     * 获取文件变更历史映射
     *
     * @param fileIds 文件ID集合
     * @return 文件ID到是否有变更的映射
     */
    private Map<Integer, Boolean> getFileChangesMap(Set<Integer> fileIds) {
        if (fileIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 批量查询并构建映射
        DSLContext dsl = filesDao.ctx();
        Set<Integer> fileIdsWithChanges = new HashSet<>(dsl
                .selectDistinct(FILE_CHANGES.FILE_ID)
                .from(FILE_CHANGES)
                .where(FILE_CHANGES.FILE_ID.in(fileIds))
                .fetch(FILE_CHANGES.FILE_ID));

        return fileIds.stream()
                .collect(Collectors.toMap(
                        id -> id,
                        fileIdsWithChanges::contains
                ));
    }

    /**
     * 为BomItemsVO设置图纸相关信息
     * 优化版本: 使用函数式编程和Stream API
     *
     * @param bomItemsVOList 要设置图纸信息的BomItemsVO列表
     * @param drawingMaps    物料-图纸映射关系
     */
    private void enrichBomItemsWithDrawingInfo(List<BomItemsVO> bomItemsVOList, Map<String, MaterialDrawingMap> drawingMaps) {
        if (bomItemsVOList == null || bomItemsVOList.isEmpty() || drawingMaps == null || drawingMaps.isEmpty()) {
            return;
        }

        bomItemsVOList.forEach(bomItem -> {
            // 设置当前项的图纸信息
            Optional.ofNullable(bomItem.getMaterialCode())
                    .filter(StringUtils::isNotBlank)
                    .map(drawingMaps::get)
                    .ifPresent(drawingMap -> {
                        bomItem.setDrawingFileId(drawingMap.getDrawingFileId());
                        bomItem.setDrawingNo(drawingMap.getDrawingNo());
                    });

            // 递归处理子项
            if (bomItem.getChildren() != null && !bomItem.getChildren().isEmpty()) {
                enrichBomItemsWithDrawingInfo(bomItem.getChildren(), drawingMaps);
            }
        });
    }

    /**
     * 查找物料编号直接匹配搜索条件的项。
     * 优化版本: 使用Stream API
     *
     * @param flatList   BOM项的扁平列表
     * @param materialNo 要匹配的物料编号 (不区分大小写)
     * @return 直接匹配项的ID集合
     */
    private Set<Integer> findDirectMatches(List<BomItemsVO> flatList, String materialNo) {
        if (flatList == null || flatList.isEmpty() || StringUtils.isBlank(materialNo)) {
            return Collections.emptySet();
        }

        String lowerCaseMaterialNo = materialNo.toLowerCase();
        return flatList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getMaterialCode()))
                .filter(item -> item.getMaterialCode().toLowerCase().contains(lowerCaseMaterialNo))
                .map(BomItemsVO::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 根据图纸编号过滤BOM项并构建树形结构
     * 与 filterAndBuildBomTree 方法保持一致的逻辑
     *
     * @param flatList   BOM项的扁平列表
     * @param drawingNo  要匹配的图纸编号 (不区分大小写)
     * @return 过滤后的BOM树形结构
     */
    private List<BomItemsVO> filterAndBuildBomTreeByDrawingNo(List<BomItemsVO> flatList, String drawingNo) {
        // 输入验证
        if (flatList == null || flatList.isEmpty() || StringUtils.isBlank(drawingNo)) {
            return Collections.emptyList();
        }

        log.debug("[FilterBomByDrawingNo] 开始根据图纸编号过滤BOM项 | 输入项数: {}, 图纸编号: {}", 
                flatList.size(), drawingNo);

        // 标记需要包含的节点
        Set<Integer> includeIds = markNodesForInclusionByDrawingNo(flatList, drawingNo);

        if (includeIds.isEmpty()) {
            log.debug("[FilterBomByDrawingNo] 没有找到匹配的BOM项");
            return Collections.emptyList();
        }

        // 使用 TreeUtils.buildTree 构建过滤后的树结构
        List<BomItemsVO> resultTree = TreeUtils.buildTree(
                flatList,                           // 扁平列表
                includeIds,                         // 要包含的节点ID集合
                BomItemsVO::getId,                  // ID提取器
                BomItemsVO::getDepth,               // 深度提取器
                item -> item,                       // 项到VO的映射器（恒等函数）
                BomItemsVO::getChildren,            // 获取子列表的函数
                BomItemsVO::setChildren             // 设置子列表的函数
        );
        
        log.debug("[FilterBomByDrawingNo] 完成BOM树构建 | 根节点数: {}", resultTree.size());
        return resultTree;
    }

    /**
     * 根据图纸编号标记需要包含的节点
     *
     * @param flatList   BOM项的扁平列表
     * @param drawingNo  要匹配的图纸编号
     * @return 需要包含的节点ID集合
     */
    private Set<Integer> markNodesForInclusionByDrawingNo(List<BomItemsVO> flatList, String drawingNo) {
        // 1. 构建树关系映射
        TreeUtils.TreeRelationships<Integer, BomItemsVO> treeRelationships = 
                TreeUtils.buildTreeRelationships(
                        flatList,                       // 扁平列表
                        BomItemsVO::getId,              // ID提取器
                        BomItemsVO::getDepth            // 深度提取器
                );

        // 2. 查找直接匹配的节点
        Set<Integer> directMatches = findDirectMatchesByDrawingNo(flatList, drawingNo);
        log.debug("[FilterBomByDrawingNo] 找到直接匹配节点: {}", directMatches.size());

        // 3. 构建完整的包含集合
        Set<Integer> includeIds = new HashSet<>(directMatches);

        // 4. 添加祖先节点
        Set<Integer> ancestors = TreeUtils.findAncestors(
                directMatches,                              // 起始节点ID集合
                treeRelationships.getChildToParentMap()     // 子到父的映射
        );
        includeIds.addAll(ancestors);

        // 5. 添加后代节点
        Set<Integer> descendants = TreeUtils.findDescendants(
                directMatches,                              // 起始节点ID集合
                treeRelationships.getParentToChildrenMap() // 父到子的映射
        );
        includeIds.addAll(descendants);

        log.debug("[FilterBomByDrawingNo] 总共包含节点数: {}", includeIds.size());
        return includeIds;
    }

    /**
     * 查找图纸编号直接匹配搜索条件的项
     *
     * @param flatList   BOM项的扁平列表
     * @param drawingNo  要匹配的图纸编号 (不区分大小写)
     * @return 直接匹配项的ID集合
     */
    private Set<Integer> findDirectMatchesByDrawingNo(List<BomItemsVO> flatList, String drawingNo) {
        if (flatList == null || flatList.isEmpty() || StringUtils.isBlank(drawingNo)) {
            return Collections.emptySet();
        }

        String lowerCaseDrawingNo = drawingNo.toLowerCase();
        
        return flatList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getMaterialCode()))
                .filter(item -> {
                    // 通过物料编号查找对应的图纸编号
                    MaterialDrawingMap mapping = materialDrawingMapService.findByMaterialCode(item.getMaterialCode());
                    return mapping != null && 
                           StringUtils.isNotBlank(mapping.getDrawingNo()) &&
                           mapping.getDrawingNo().toLowerCase().contains(lowerCaseDrawingNo);
                })
                .map(BomItemsVO::getId)
                .collect(Collectors.toSet());
    }


    /**
     * 获取用户可访问的机型角色列表。
     */
    public Set<String> getUserModelCodes() {
        if (SecurityUtils.isAdmin()) {
            // 管理员返回所有机型
            return productModelsService.selectProductModelsList(new ProductModels()).stream()
                    .map(ProductModels::getModelCode).collect(Collectors.toSet());
        }

        // 普通用户返回有权限的机型
        return SecurityUtils.getLoginUser().getUser().getRoles().stream()
                .map(SysRole::getRoleName)
                .filter(roleName -> roleName.startsWith(MODEL_ROLE_PREFIX))
                .map(roleName -> roleName.substring(MODEL_ROLE_PREFIX.length()))
                .collect(Collectors.toSet());
    }

    /**
     * 获取用户可访问的机型ID列表。
     */
    public Set<Integer> getUserModelIds() {
        Set<String> modelCodes = getUserModelCodes();
        if (modelCodes.isEmpty()) {
            return Collections.emptySet();
        }

        return productModelsService.selectProductModelsList(new ProductModels()).stream()
                .filter(model -> modelCodes.contains(model.getModelCode()))
                .map(ProductModels::getId)
                .collect(Collectors.toSet());
    }

    /**
     * 检查用户是否有指定机型的权限。
     */
    public void checkModelPermission(String modelCode) {
        if (!SecurityUtils.isAdmin() && !getUserModelCodes().contains(modelCode)) {
            // throw new com.docs.common.exception.ServiceException("无权操作该机型文件");
        }
    }

    /**
     * 根据机型ID获取机型代码
     */
    public String getModelCodeById(Integer modelId) {
        ProductModels model = productModelsService.selectByPrimaryKey(modelId);
        if (model == null) {
            throw new com.docs.common.exception.ServiceException("机型不存在");
        }
        return model.getModelCode();
    }

    /**
     * 根据物料编号过滤BOM项并构建层级树。
     * 仅包含直接匹配项(按物料编号)、它们的祖先和它们的后代。
     * 不匹配的兄弟节点及其后代将被排除。
     *
     * @param flatList   BOM项的扁平列表
     * @param materialNo 用于过滤的物料编号
     * @return 匹配的BOM项的层级树
     */
    private List<BomItemsVO> filterAndBuildBomTree(List<BomItemsVO> flatList, String materialNo) {
        if (flatList == null || flatList.isEmpty()) {
            log.debug("BOM过滤: 扁平列表为空, 返回空树");
            return new ArrayList<>();
        }

        log.debug("BOM过滤: 按物料编号 '{}' 过滤 {} 个项目", materialNo, flatList.size());

        // 步骤 1: 标记需要包含的节点
        Set<Integer> nodeIdsToInclude = markNodesForInclusion(flatList, materialNo);

        // 如果未找到匹配项, 返回空列表
        if (nodeIdsToInclude.isEmpty()) {
            log.debug("BOM过滤: 未找到物料编号 '{}' 的匹配项", materialNo);
            return new ArrayList<>();
        }

        log.debug("BOM过滤: 过滤后找到 {} 个要包含的项目", nodeIdsToInclude.size());

        // 步骤 2: 仅使用标记的节点构建树
        return TreeUtils.buildTree(
                flatList,
                nodeIdsToInclude,
                BomItemsVO::getId,
                BomItemsVO::getDepth,
                item -> item,
                BomItemsVO::getChildren,
                BomItemsVO::setChildren);
    }

    /**
     * 根据物料编号标记要包含的节点, 包括直接匹配项、它们的祖先和它们的后代。
     *
     * @param flatList   要分析的BOM项的扁平列表
     * @param materialNo 用于过滤的物料编号 (不区分大小写的子字符串匹配)
     * @return 要在过滤后的树中包含的节点ID集合
     */
    private Set<Integer> markNodesForInclusion(List<BomItemsVO> flatList, String materialNo) {
        if (flatList == null || flatList.isEmpty() || StringUtils.isBlank(materialNo)) {
            log.debug("BOM过滤: 列表为空或物料编号为空, 返回空集合。");
            return Collections.emptySet();
        }

        log.debug("BOM过滤: 开始对物料编号 '{}' 进行过滤", materialNo);

        // 使用TreeUtils构建树操作所需的数据结构
        TreeUtils.TreeRelationships<Integer, BomItemsVO> treeRelationships = TreeUtils.buildTreeRelationships(
                flatList,
                BomItemsVO::getId,
                BomItemsVO::getDepth);

        // 根据物料编号查找直接匹配项
        Set<Integer> directMatchIds = findDirectMatches(flatList, materialNo);

        if (directMatchIds.isEmpty()) {
            log.debug("BOM过滤: 未找到物料编号 '{}' 的直接匹配项", materialNo);
            return Collections.emptySet();
        }

        log.debug("BOM过滤: 找到 {} 个直接匹配项: {}", directMatchIds.size(), directMatchIds);

        // 使用TreeUtils查找直接匹配项的祖先和后代
        Set<Integer> ancestors = TreeUtils.findAncestors(directMatchIds, treeRelationships.getChildToParentMap());
        Set<Integer> descendants = TreeUtils.findDescendants(directMatchIds,
                treeRelationships.getParentToChildrenMap());

        // 合并所有要包含的节点
        Set<Integer> nodesToInclude = new HashSet<>(directMatchIds);
        nodesToInclude.addAll(ancestors);
        nodesToInclude.addAll(descendants);

        log.debug("BOM过滤: 最终包含的节点集大小: {}", nodesToInclude.size());
        return nodesToInclude;
    }

    /**
     * 从扁平的项目列表中构建BOM树。
     * 基于深度属性创建层级结构。
     *
     * @param flatList BOM项的扁平列表
     * @return BOM项的层级树结构
     */
    private List<BomItemsVO> buildBomTree(List<BomItemsVO> flatList) {
        if (flatList == null || flatList.isEmpty()) {
            return new ArrayList<>();
        }

        return TreeUtils.buildTree(
                flatList,
                flatList.stream().map(BomItemsVO::getId).collect(Collectors.toSet()), // Include all nodes
                BomItemsVO::getId,
                BomItemsVO::getDepth,
                item -> item,
                BomItemsVO::getChildren,
                BomItemsVO::setChildren);
    }

    /**
     * 批量根据文件ID查询BOM数据
     *
     * @param fileIds 文件ID集合
     * @return Map<Integer, List < BomItemsVO>> key为fileId, value为对应的BomItemsVO列表
     */
    private Map<Integer, List<BomItemsVO>> fetchAllBomItemsForFiles(Set<Integer> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return Collections.emptyMap();
        }
        // 调用 BomItemService 中新增的批量查询方法
        List<BomItemsVO> allBomItems = bomItemService.selectByFileIds(fileIds);

        // 按 fileId 分组
        return allBomItems.stream()
                .filter(Objects::nonNull) // 过滤掉可能的 null 值
                .collect(Collectors.groupingBy(BomItemsVO::getFileId));
    }


    public void updateFilesById(Files files) {
        filesDao.update(files);
    }

    public void insertFileChanges(FileChanges changes) {
        fileChangesDao.insert(changes);
    }

    public void insertFiles(Files newFile) {
        filesDao.insert(newFile);
    }

}
