package com.docs.files.mapper;

import com.docs.common.core.domain.entity.FileChanges;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FileChangesMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(FileChanges record);

    int insertSelective(FileChanges record);

    FileChanges selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FileChanges record);

    int updateByPrimaryKey(FileChanges record);

    int updateBatch(@Param("list") List<FileChanges> list);

    int updateBatchSelective(@Param("list") List<FileChanges> list);

    int batchInsert(@Param("list") List<FileChanges> list);

    /**
     * 查询文件变更历史列表
     * 
     * @param fileChanges 查询条件
     * @return 变更历史列表
     */
    List<FileChanges> selectFileChangesList(FileChanges fileChanges);
}