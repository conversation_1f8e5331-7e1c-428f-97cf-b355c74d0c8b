package com.docs.files.mapper;

import com.docs.common.core.domain.entity.BomItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * BOM表项数据Mapper接口
 */
public interface BomItemMapper {
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入记录
     */
    int insert(BomItem record);

    /**
     * 选择性插入记录
     */
    int insertSelective(BomItem record);

    /**
     * 根据主键查询
     */
    BomItem selectByPrimaryKey(Integer id);

    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(BomItem record);

    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(BomItem record);

    /**
     * 批量更新
     */
    int updateBatch(@Param("list") List<BomItem> list);

    /**
     * 选择性批量更新
     */
    int updateBatchSelective(@Param("list") List<BomItem> list);

    /**
     * 批量插入
     */
    int batchInsert(@Param("list") List<BomItem> list);
    
    /**
     * 根据文件ID删除BOM数据
     */
    int deleteByFileId(Integer fileId);
    
    /**
     * 根据文件ID查询BOM数据
     */
    List<BomItem> selectByFileId(Integer fileId);
    
    /**
     * 查询BOM数据列表
     */
    List<BomItem> selectBomItemList(BomItem bomItem);
} 