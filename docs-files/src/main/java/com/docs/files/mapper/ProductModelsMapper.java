package com.docs.files.mapper;

import com.docs.common.core.domain.entity.ProductModels;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductModelsMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ProductModels record);

    int insertSelective(ProductModels record);

    ProductModels selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ProductModels record);

    int updateByPrimaryKey(ProductModels record);

    int updateBatch(@Param("list") List<ProductModels> list);

    int updateBatchSelective(@Param("list") List<ProductModels> list);

    int batchInsert(@Param("list") List<ProductModels> list);

    List<ProductModels> selectProductModelsList(ProductModels productModel);
    
    boolean checkModelExists(@Param("modelCode") String modelCode);
    
    /**
     * 根据机型代码查询机型信息
     */
    ProductModels selectByModelCode(@Param("modelCode") String modelCode);
}