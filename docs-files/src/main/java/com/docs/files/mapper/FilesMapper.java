package com.docs.files.mapper;

import com.docs.common.core.domain.entity.FilesEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FilesMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(FilesEntity record);

    int insertSelective(FilesEntity record);

    FilesEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(FilesEntity record);

    int updateByPrimaryKey(FilesEntity record);

    int updateBatch(@Param("list") List<FilesEntity> list);

    int updateBatchSelective(@Param("list") List<FilesEntity> list);

    int batchInsert(@Param("list") List<FilesEntity> list);

    /**
     * 查询文件列表
     */
    List<FilesEntity> selectFilesList(FilesEntity filesEntity);

    /**
     * 根据版本号查询文件
     */
    FilesEntity selectByVersion(@Param("productModelId") Integer productModelId,
                                @Param("fileType") String fileType,
                                @Param("subType") String subType,
                                @Param("version") String version);
}