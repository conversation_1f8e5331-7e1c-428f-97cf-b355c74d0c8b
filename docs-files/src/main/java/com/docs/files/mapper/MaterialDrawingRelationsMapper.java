package com.docs.files.mapper;

import com.docs.common.core.domain.entity.MaterialDrawingRelations;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MaterialDrawingRelationsMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(MaterialDrawingRelations record);

    int insertSelective(MaterialDrawingRelations record);

    MaterialDrawingRelations selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(MaterialDrawingRelations record);

    int updateByPrimaryKey(MaterialDrawingRelations record);

    int updateBatch(@Param("list") List<MaterialDrawingRelations> list);

    int updateBatchSelective(@Param("list") List<MaterialDrawingRelations> list);

    int batchInsert(@Param("list") List<MaterialDrawingRelations> list);

    /**
     * 查询所有物料图纸关系
     */
    List<MaterialDrawingRelations> selectAll();
}