package com.docs.files.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 版本号工具类
 * 
 * <AUTHOR>
 */
public class VersionUtils {
    
    // 版本号提取模式，匹配形如 "1043JS-BOM-000-V1.0" 中的 "1.0"
    private static final Pattern VERSION_PATTERN = Pattern.compile(".*?[vV](\\d+\\.\\d+)$");
    
    /**
     * 从完整版本号中提取实际版本号部分
     * 
     * @param fullVersion 完整版本号，如 "1043JS-BOM-000-V1.0"
     * @return 实际版本号，如 "1.0"，如果无法提取则返回原始字符串
     */
    public static String extractVersionNumber(String fullVersion) {
        if (fullVersion == null || fullVersion.isEmpty()) {
            return fullVersion;
        }
        
        // 尝试匹配复杂版本号格式
        Matcher matcher = VERSION_PATTERN.matcher(fullVersion);
        if (matcher.matches()) {
            return matcher.group(1);
        }
        
        // 如果不是复杂格式，直接返回原始字符串
        return fullVersion;
    }
    
    /**
     * 比较两个版本号
     * 
     * @param v1 版本号1，可以是完整格式如 "1043JS-BOM-000-V1.0"
     * @param v2 版本号2，可以是完整格式如 "1043JS-BOM-000-V1.1"
     * @return 如果v1 > v2返回1，如果v1 < v2返回-1，如果相等返回0
     */
    public static int compareVersions(String v1, String v2) {
        if (v1 == null || v2 == null) {
            return 0;
        }
        
        // 提取实际版本号部分
        String extractedV1 = extractVersionNumber(v1);
        String extractedV2 = extractVersionNumber(v2);
        
        String[] v1Parts = extractedV1.split("\\.");
        String[] v2Parts = extractedV2.split("\\.");
        
        int length = Math.max(v1Parts.length, v2Parts.length);
        
        for (int i = 0; i < length; i++) {
            int v1Part = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2Part = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;
            
            if (v1Part > v2Part) {
                return 1;
            }
            
            if (v1Part < v2Part) {
                return -1;
            }
        }
        
        return 0;
    }
}
