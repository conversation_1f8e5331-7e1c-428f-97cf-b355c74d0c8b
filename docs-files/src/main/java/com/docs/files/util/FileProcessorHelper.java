package com.docs.files.util;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.enums.FilesStatus;
import com.docs.common.jooq.generated.tables.daos.FilesDao;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.files.repository.FileRepository;
import com.docs.files.service.ProductModelsService;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.jooq.DSLContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;

import static com.docs.common.jooq.generated.Tables.FILES;

/**
 * 文件处理器辅助工具类
 * 提供文件处理器共用的方法，减少代码重复
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FileProcessorHelper {

    private final ProductModelsService productModelsService;
    private final FilesDao filesDao;
    private final FileRepository fileRepository;

    /**
     * 创建并初始化Files对象
     *
     * @param model 产品型号
     * @param fileType 文件类型
     * @param subType 子类型
     * @param version 版本号
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 初始化的Files对象
     */
    public Files createFileInfo(ProductModels model, String fileType, String subType,
                               String version, Path filePath, String fileName) {
        Files fileInfo = new Files();
        if(model != null){
            fileInfo.setProductModelId(model.getId());
        }
        try {
            fileInfo.setFileType(FilesFileType.valueOf(fileType));
        } catch (IllegalArgumentException e) {
            log.error("Invalid fileType string: {}", fileType, e);
            throw new ServiceException("内部错误：无效的文件类型代码");
        }
        fileInfo.setSubType(subType);
        fileInfo.setVersion(version);
        fileInfo.setStatus(FilesStatus.ACTIVE);
        fileInfo.setFilePath(filePath.toString());
        fileInfo.setFileName(fileName);
        return fileInfo;
    }

    /**
     * 创建并初始化带图纸编号的Files对象
     *
     * @param model 产品型号
     * @param fileType 文件类型
     * @param subType 子类型
     * @param version 版本号
     * @param filePath 文件路径
     * @param fileName 文件名
     * @param drawingNo 图纸编号
     * @return 初始化的Files对象
     */
    public Files createDrawingFileInfo(ProductModels model, String fileType, String subType,
                                      String version, Path filePath, String fileName, String drawingNo) {
        Files fileInfo = createFileInfo(model, fileType, subType, version, filePath, fileName);
        fileInfo.setDrawingNo(drawingNo);
        return fileInfo;
    }

    /**
     * 获取产品型号
     *
     * @param productModelCode 产品型号代码
     * @return 产品型号对象
     * @throws ServiceException 如果产品型号不存在
     */
    public ProductModels getProductModel(String productModelCode) {
        ProductModels model = productModelsService.selectByModelCode(productModelCode);
        if (model == null) {
            throw new ServiceException("产品型号不存在: " + productModelCode);
        }
        return model;
    }

    /**
     * 根据文件类型、版本号、文件名查询文件（唯一性校验）
     *
     * @param fileType 文件类型
     * @param version 版本号
     * @param fileName 文件名
     * @return 文件对象，如果不存在则返回null
     */
    public Files selectByVersion(String fileType, String version, String fileName) {
        DSLContext dsl = filesDao.ctx();
        FilesFileType fileTypeEnum;
        try {
            fileTypeEnum = FilesFileType.valueOf(fileType);
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Invalid fileType string '{}' provided for version check", fileType, e);
            return null;
        }
        return dsl.selectFrom(FILES)
             .where(FILES.FILE_TYPE.eq(fileTypeEnum))
             .and(FILES.VERSION.eq(version))
             .and(FILES.FILE_NAME.eq(fileName))
             .fetchOneInto(Files.class);
    }

    /**
     * 检查相同文件是否存在（基于产品型号、文件类型、子类型、文件名）
     * 用于判断是更新还是新增操作
     *
     * @param modelId 产品型号ID
     * @param fileType 文件类型
     * @param subType 子类型
     * @param fileName 文件名
     * @return 现有文件对象，如果不存在则返回null
     */
    public Files checkFileExists(Integer modelId, String fileType, String subType, String fileName) {
        DSLContext dsl = filesDao.ctx();

        FilesFileType fileTypeEnum;
        try {
            fileTypeEnum = FilesFileType.valueOf(fileType);
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Invalid fileType string '{}' provided for file existence check", fileType, e);
            return null;
        }

        return dsl.selectFrom(FILES)
                .where(FILES.PRODUCT_MODEL_ID.eq(modelId))
                .and(FILES.FILE_TYPE.eq(fileTypeEnum))
                .and(FILES.SUB_TYPE.eq(subType))
                .and(FILES.FILE_NAME.eq(fileName))
                .and(FILES.STATUS.eq(FilesStatus.ACTIVE))
                .fetchOneInto(Files.class);
    }

    /**
     * 根据版本信息查询文件
     *
     * @param modelId 产品型号ID
     * @param fileType 文件类型
     * @param subType 子类型
     * @param version 版本号
     * @return 文件对象，如果不存在则返回null
     */
    public Files selectByVersion(Integer modelId, String fileType, String subType, String version) {
        DSLContext dsl = filesDao.ctx();

        FilesFileType fileTypeEnum;
        try {
            fileTypeEnum = FilesFileType.valueOf(fileType);
        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("Invalid fileType string '{}' provided for version check", fileType, e);
            return null;
        }

        return dsl.selectFrom(FILES)
             .where(FILES.PRODUCT_MODEL_ID.eq(modelId))
             .and(FILES.FILE_TYPE.eq(fileTypeEnum))
             .and(FILES.SUB_TYPE.eq(subType))
             .and(FILES.VERSION.eq(version))
             .fetchOneInto(Files.class);
    }

    /**
     * 根据图纸编号和版本号查询文件
     *
     * @param drawingNo 图纸编号
     * @param version 版本号
     * @return 文件对象，如果不存在则返回null
     */
    public Files selectByDrawingVersionByNo(String drawingNo, String version) {
        DSLContext dsl = filesDao.ctx();
        return dsl.selectFrom(FILES)
            .where(FILES.DRAWING_NO.eq(drawingNo))
            .and(FILES.VERSION.eq(version))
            .fetchOneInto(Files.class);
    }

    /**
     * 更新现有文件
     * 用于文件重复上传时的更新操作
     *
     * @param existingFile 现有文件对象
     * @param newFilePath 新文件路径
     * @param newFileName 新文件名
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExistingFile(Files existingFile, Path newFilePath, String newFileName) {
        try {
            // 更新文件路径和文件名
            existingFile.setFilePath(newFilePath.toString());
            existingFile.setFileName(newFileName);
            existingFile.setUpdatedAt(LocalDateTime.now());
            
            // 保存到数据库
            filesDao.update(existingFile);
            
            log.info("更新现有文件成功: fileId={}, filePath={}", existingFile.getId(), newFilePath);
            return true;
        } catch (Exception e) {
            log.error("更新现有文件失败: fileId={}", existingFile.getId(), e);
            return false;
        }
    }


    /**
     * 上传文件
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult uploadFile(FileUploadRequest request) {
        try {
            log.info("开始处理文件上传请求");

            // 1. 检查机型权限
            Integer productModelId = Integer.parseInt(request.getProductModel());
            ProductModels model = productModelsService.selectByPrimaryKey(productModelId);
            fileRepository.checkModelPermission(model.getModelCode());

            // 2. 创建目标目录
            String targetDir = String.format("/uploads/%s/%s/V%s",
                    model.getModelCode(),
                    request.getFileType().toLowerCase(),
                    request.getVersion());
            Path directory = Paths.get(targetDir);
            if (!java.nio.file.Files.exists(directory)) {
                java.nio.file.Files.createDirectories(directory);
            }

            // 3. 保存文件
            MultipartFile file = request.getFiles()[0]; // 获取第一个文件
            String extension = FilenameUtils.getExtension(file.getOriginalFilename());
            String fileName = String.format("%s_v%s.%s", file.getOriginalFilename(), request.getVersion(), extension);
            Path filePath = directory.resolve(fileName);
            java.nio.file.Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            // 4. 创建文件记录
            Files fileRecord = new Files()
                .setFileName(FilenameUtils.getBaseName(file.getOriginalFilename()))
                .setProductModelId(productModelId)
                .setFileType(FilesFileType.valueOf(request.getFileType().toUpperCase()))
                .setSubType(request.getSubType())
                .setVersion(request.getVersion())
                .setStatus(FilesStatus.ACTIVE)
                .setFilePath(filePath.toString())
                .setCreatedAt(LocalDateTime.now());

            // 5. 保存文件记录
            filesDao.insert(fileRecord);

            log.info("文件上传成功: {}", filePath);
            return AjaxResult.success("上传成功", fileRecord.getId());
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException("文件上传失败: " + e.getMessage());
        }
    }

}
