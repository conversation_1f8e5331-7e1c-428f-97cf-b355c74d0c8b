package com.docs.files.util;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.model.FileChangeRequest;
import com.docs.common.core.domain.vo.BomItemsVO;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.enums.FilesStatus;
import com.docs.common.jooq.generated.tables.pojos.FileChanges;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.common.utils.SecurityUtils;
import com.docs.common.utils.bean.BeanUtils;
import com.docs.files.repository.FileRepository;
import com.docs.files.service.BomItemService;
import com.docs.files.service.MaterialDrawingMapService;
import com.docs.files.service.ProductModelsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文件版本管理工具类
 * 负责处理文件版本变更相关的功能
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FileVersionHelper {

    private final BomItemService bomItemService;
    private final ProductModelsService productModelsService;
    private final MaterialDrawingMapService materialDrawingMapService;
    private final FileRepository fileRepository;

    /**
     * 文件版本变更（带校验）- 优化版
     * 提高可读性、健壮性和可维护性
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult changeFileVersionWithValidation(FileChangeRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("[FileVersionChange] 开始执行文件版本变更(带校验) | Request: fileId={}, newVersion={}",
                request.getFileId(), request.getNewVersion());

        try {
            // 1. 校验请求参数
            validateFileChangeRequest(request);

            // 2. 获取并校验原文件信息
            Files oldFile = findAndValidateOldFile(request.getFileId());

            // 3. 校验版本号
            validateVersion(request.getNewVersion(), oldFile.getVersion());

            // 4. 校验权限 (如果需要，可在此处添加，当前逻辑依赖外部调用或Filter)
            // checkModelPermission(oldFile.getProductModelId());

            // 5. 处理上传的文件
            MultipartFile[] uploadedFiles = request.getFiles();
            validateUploadedFiles(uploadedFiles);
            MultipartFile bomFile = uploadedFiles[0];
            MultipartFile relationFile = (uploadedFiles.length > 1) ? uploadedFiles[1] : null;

            // 6. 更新原文件状态并记录历史
            obsoleteOldFile(oldFile);
            recordFileChangeHistory(oldFile, request);

            // 7. 构建新文件信息并保存
            Path bomFilePath = saveUploadedFile(bomFile, oldFile, request.getNewVersion());
            Files newFile = createAndInsertNewFileRecord(oldFile, request, bomFilePath);

            // 8. 保存关系文件（如果存在）
            Optional<Path> relationFilePathOpt = saveUploadedFileOptional(relationFile, oldFile, request.getNewVersion(), "material_drawing_relation");

            // 9. 处理BOM及物料关系（如果是BOM文件）
            if (isBomFile(newFile)) {
                newFile.setProductModelId(request.getProductModelId());
                processBomAndRelationFiles(newFile, bomFilePath, relationFilePathOpt.orElse(null));
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("[FileVersionChange] 文件版本变更成功完成 | FileId: {}, OldVersion: {}, NewVersion: {}, Duration: {}ms",
                    oldFile.getId(), oldFile.getVersion(), newFile.getVersion(), duration);
            return AjaxResult.success("变更成功");

        } catch (ServiceException se) {
            log.warn("[FileVersionChange] 文件版本变更校验失败 | Request: fileId={}, newVersion={}, Reason: {}",
                    request.getFileId(), request.getNewVersion(), se.getMessage());
            return AjaxResult.error(se.getMessage());
        } catch (Exception e) {
            log.error("[FileVersionChange] 文件版本变更时发生意外错误 | Request: fileId={}, newVersion={}",
                    request.getFileId(), request.getNewVersion(), e);
            // 考虑是否需要更具体的错误信息返回给前端
            throw new ServiceException("文件版本变更时发生意外错误");
        }
    }

    /**
     * 多文件版本变更
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult changeFileVersionMultiple(FileChangeRequest request) {
        try {
            log.info("开始执行多文件版本变更操作");
            // 1. 获取原文件信息并检查权限
            Files oldFile = fileRepository.fetchFileById(request.getFileId());
            if (oldFile == null) {
                return AjaxResult.error("文件不存在");
            }

            MultipartFile[] files = request.getFiles();
            // 2. 验证上传文件
            if (files == null || files.length == 0) {
                return AjaxResult.error("请选择要上传的文件");
            }

            // 3. 更新原文件状态为报废
            oldFile.setStatus(FilesStatus.OBSOLETE)
                    .setUpdatedAt(LocalDateTime.now());
            fileRepository.updateFilesById(oldFile);

            // 记录变更历史
            FileChanges changes = new FileChanges()
                    .setFileId(oldFile.getId())
                    .setOldVersion(oldFile.getVersion())
                    .setNewVersion(request.getNewVersion())
                    .setChangeReason(request.getChangeReason())
                    .setChangedBy(SecurityUtils.getUserId().intValue())
                    .setChangedAt(LocalDateTime.now());
            fileRepository.insertFileChanges(changes);

            // 构建目标目录
            String targetDir = oldFile.getFilePath().substring(0, oldFile.getFilePath().lastIndexOf('/'));
            targetDir = targetDir.substring(0, targetDir.lastIndexOf('/')) + "/V" + request.getNewVersion();
            Path directory = Paths.get(targetDir);
            if (!java.nio.file.Files.exists(directory)) {
                java.nio.file.Files.createDirectories(directory);
            }

            // 处理BOM文件
            MultipartFile bomFile = files[0];
            if (bomFile.isEmpty()) {
                return AjaxResult.error("上传的BOM文件为空");
            }

            String bomFileName = preserveOriginalFilename(bomFile.getOriginalFilename(), request.getNewVersion());
            Path bomFilePath = directory.resolve(bomFileName);
            java.nio.file.Files.copy(bomFile.getInputStream(), bomFilePath, StandardCopyOption.REPLACE_EXISTING);

            // 创建新版本文件记录
            Files newFile = new Files();
            BeanUtils.copyProperties(oldFile, newFile);
            newFile.setId(null)
                    .setVersion(request.getNewVersion())
                    .setStatus(FilesStatus.ACTIVE)
                    .setFilePath(bomFilePath.toString())
                    .setFileName(bomFilePath.getFileName().toString())
                    .setCreatedAt(null)
                    .setUpdatedAt(null);
            if (request.getProductModelId() != null) {
                newFile.setProductModelId(request.getProductModelId());
            }

            log.info("准备插入新文件记录: FileName={}, FilePath={}, Version={}",
                     newFile.getFileName(), newFile.getFilePath(), newFile.getVersion());

            fileRepository.insertFiles(newFile);

            // 处理物料图纸关系文件（如果有）
            Path relationFilePath = null;
            if (files.length > 1 && !files[1].isEmpty()) {
                MultipartFile relationFile = files[1];
                String relationFileExtension = FilenameUtils.getExtension(relationFile.getOriginalFilename());
                String relationFileName = String.format("material_drawing_relation_v%s.%s", request.getNewVersion(), relationFileExtension);
                relationFilePath = directory.resolve(relationFileName);
                java.nio.file.Files.copy(relationFile.getInputStream(), relationFilePath, StandardCopyOption.REPLACE_EXISTING);
                log.info("物料图纸关系文件已保存: {}", relationFilePath);
            } else {
                log.info("未提供物料图纸关系文件，将只处理BOM文件");
            }

            // 如果是BOM文件, 解析Excel内容并处理物料图纸关系
            if ("BOM".equals(newFile.getFileType().name()) && newFile.getSubType() != null) {
                processBomAndRelationFiles(newFile, bomFilePath, relationFilePath);
            }

            log.info("文件版本变更操作成功完成");
            return AjaxResult.success("变更成功");
        } catch (Exception e) {
            log.error("文件版本变更失败", e);
            throw new ServiceException("文件版本变更失败");
        }
    }

    /**
     * 处理BOM文件和物料图纸关系文件
     */
    private void processBomAndRelationFiles(Files newFile, Path bomFilePath, Path relationFilePath) {
        try {
            // 1. 解析BOM文件并保存
            bomItemService.parseBomExcelAndSave(newFile, bomFilePath.toString(), newFile.getSubType());

            // 2. 获取BOM项
            List<BomItemsVO> bomItems = bomItemService.selectByFileId(newFile.getId());

            // 3. 处理物料图纸关系
            if (relationFilePath != null) {
                // 3.1 如果提供了关系文件，解析并更新映射
                List<MaterialDrawingMap> relations =
                        materialDrawingMapService.parseRelationExcel(relationFilePath.toString(), newFile.getId());

                // 添加日志记录
                log.info("解析物料图纸关系文件成功，共解析出 {} 条关系", relations.size());

                // 然后将关系转换为 Map 用于后续处理
                Map<String, MaterialDrawingMap> relationsMap =
                        relations.stream()
                                .collect(Collectors.toMap(MaterialDrawingMap::getMaterialCode, Function.identity(), (existing, replacement) -> existing));

                // 3.2 更新BOM项
                bomItems.forEach(bomItem -> {
                    if (bomItem.getModelId() == null) {
                        bomItem.setModelId(newFile.getProductModelId());
                    }
                    MaterialDrawingMap relation = relationsMap.get(bomItem.getMaterialCode());
                    if (relation != null) {
                        relation.setMaterialName(bomItem.getMaterialName());
                        relation.setSpecification(bomItem.getSpecification());
                    }
                });
                // 批量处理物料-图纸映射关系（更新或插入）
                int successCount = materialDrawingMapService.batchUpsertFromRelations(relations);
                log.info("批量处理物料-图纸映射完成，成功率: {}/{}", successCount, relations.size());
            } else {
                // 4. 如果没有提供关系文件，从现有的物料-图纸映射表中查询并填充
                log.info("未提供物料图纸关系文件，将从现有的物料-图纸映射表中查询并填充");

                // 4.1 获取所有物料编码
                List<String> materialCodes = bomItems.stream()
                        .map(BomItemsVO::getMaterialCode)
                        .filter(java.util.Objects::nonNull)
                        .collect(Collectors.toList());

                if (!materialCodes.isEmpty()) {
                    // 4.2 批量查询物料-图纸映射关系
                    Map<String, com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap> existingMappings =
                            materialDrawingMapService.findByMaterialCodes(materialCodes);

                    log.info("从现有映射中找到 {} 条物料-图纸映射关系", existingMappings.size());

                    // 4.3 收集需要处理的物料-图纸映射关系
                    List<com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap> mappingsToProcess = new ArrayList<>();

                    for (BomItemsVO bomItem : bomItems) {
                        com.docs.common.jooq.generated.tables.pojos.MaterialDrawingMap mapping =
                                existingMappings.get(bomItem.getMaterialCode());
                        if (mapping != null) {
                            // 更新物料名称和规格，保留原有的图纸信息
                            mapping.setMaterialName(bomItem.getMaterialName());
                            mapping.setSpecification(bomItem.getSpecification());
                            mappingsToProcess.add(mapping);
                        }
                    }

                    // 4.4 批量处理物料-图纸映射关系
                    int successCount = materialDrawingMapService.batchUpsertFromRelations(mappingsToProcess);
                    log.info("从现有映射中填充了 {} 个BOM项的图纸信息", successCount);
                }
            }

            // 5. 更新BOM项
            bomItemService.updateBomItems(bomItems);
        } catch (Exception e) {
            log.error("处理BOM和物料图纸关系文件失败: {}", e.getMessage(), e);
            // 不终止整个流程, 记录错误并继续
        }
    }

    /**
     * 保留原始文件名，但添加版本号
     */
    private String preserveOriginalFilename(String originalFilename, String version) {
        String extension = FilenameUtils.getExtension(originalFilename);
        String nameWithoutExtension = FilenameUtils.getBaseName(originalFilename);
        return String.format("%s_v%s.%s", nameWithoutExtension, version, extension);
    }

    // --- 私有辅助方法 ---

    /**
     * 校验文件变更请求
     */
    private void validateFileChangeRequest(FileChangeRequest request) {
        if (request == null || request.getFileId() == null || StringUtils.isBlank(request.getNewVersion())) {
            throw new ServiceException("无效的文件变更请求：缺少必要参数（FileId, NewVersion）");
        }
        // 可根据需要添加更多校验，例如 reason 不能为空等
    }

    /**
     * 查找并校验旧文件记录
     */
    private Files findAndValidateOldFile(Integer fileId) {
        return Optional.ofNullable(fileRepository.fetchFileById(fileId))
                .orElseThrow(() -> new ServiceException("原文件不存在: ID = " + fileId));
    }

    /**
     * 校验版本号
     */
    private void validateVersion(String newVersion, String currentVersion) {
        if (StringUtils.isNotBlank(currentVersion) && StringUtils.isNotBlank(newVersion)) {
            int compareResult = VersionUtils.compareVersions(newVersion, currentVersion);
            if (compareResult <= 0) {
                throw new ServiceException("新版本号(" + newVersion + ")必须大于当前版本号(" + currentVersion + ")");
            }
            log.debug("[VersionValidation] 版本号校验通过: New={}, Current={}", newVersion, currentVersion);
        } else if (StringUtils.isBlank(newVersion)) {
            throw new ServiceException("新版本号不能为空");
        }
        // 如果 currentVersion 为空，则不进行比较 (允许首次设置版本)
    }

    /**
     * 校验上传的文件数组
     */
    private void validateUploadedFiles(MultipartFile[] files) {
        if (files == null || files.length == 0 || files[0] == null || files[0].isEmpty()) {
            throw new ServiceException("必须上传文件（至少一个BOM文件）");
        }
        // 可以添加更多校验，例如文件大小、类型等
    }

    /**
     * 将旧文件状态设置为报废
     */
    private void obsoleteOldFile(Files oldFile) {
        oldFile.setStatus(FilesStatus.OBSOLETE)
                .setUpdatedAt(LocalDateTime.now());
        fileRepository.updateFilesById(oldFile);
        log.info("[ObsoleteFile] 旧文件状态已更新为 OBSOLETE | FileId: {}", oldFile.getId());
    }

    /**
     * 记录文件变更历史
     */
    private void recordFileChangeHistory(Files oldFile, FileChangeRequest request) {
        FileChanges changes = new FileChanges()
                .setFileId(oldFile.getId())
                .setOldVersion(oldFile.getVersion())
                .setNewVersion(request.getNewVersion())
                .setChangeReason(StringUtils.trimToNull(request.getChangeReason())) // 使用 trimToNull
                .setChangedBy(SecurityUtils.getUserId().intValue())
                .setChangedAt(LocalDateTime.now());
        fileRepository.insertFileChanges(changes); // insert 方法通常是 void，不需要接收返回值
        // 检查 ID 是否已生成（如果需要）
        if (changes.getId() == null) {
            log.warn("[FileChangeHistory] 插入变更历史后未能获取到ID | FileId: {}, NewVersion: {}", oldFile.getId(), request.getNewVersion());
            // 根据需要决定是否抛出异常
        }
        log.info("[FileChangeHistory] 变更历史已记录 | FileId: {}, OldVersion: {}, NewVersion: {}, ChangeId: {}",
                oldFile.getId(), oldFile.getVersion(), request.getNewVersion(), changes.getId());
    }

    /**
     * 保存上传的文件
     */
    private Path saveUploadedFile(MultipartFile file, Files referenceFile, String newVersion) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("尝试保存一个空的上传文件");
        }
        try {
            Path directory = buildNewVersionDirectory(referenceFile.getFilePath(), newVersion);
            String newFilename = preserveOriginalFilename(file.getOriginalFilename(), newVersion);
            Path filePath = directory.resolve(newFilename);
            java.nio.file.Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            log.info("[FileUpload] 文件已保存 | Path: {}", filePath);
            return filePath;
        } catch (Exception e) {
            log.error("[FileUpload] 保存上传文件失败 | OriginalFilename: {}, Version: {}",
                    file.getOriginalFilename(), newVersion, e);
            throw new ServiceException("保存上传文件失败: " + e.getMessage());
        }
    }

    /**
     * 可选地保存上传的文件（如图纸关系文件）
     */
    private Optional<Path> saveUploadedFileOptional(MultipartFile file, Files referenceFile, String newVersion, String fileTypeIdentifier) {
        if (file == null || file.isEmpty()) {
            log.info("[FileUploadOptional] 未提供或空的 {} 文件，跳过保存.", fileTypeIdentifier);
            return Optional.empty();
        }
        try {
            Path directory = buildNewVersionDirectory(referenceFile.getFilePath(), newVersion);
            // 生成特定名称，而不是保留原始名称
            String extension = FilenameUtils.getExtension(file.getOriginalFilename());
            String newFilename = String.format("%s_v%s.%s", fileTypeIdentifier, newVersion, extension);
            Path filePath = directory.resolve(newFilename);
            java.nio.file.Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            log.info("[FileUploadOptional] {} 文件已保存 | Path: {}", fileTypeIdentifier, filePath);
            return Optional.of(filePath);
        } catch (Exception e) {
            log.error("[FileUploadOptional] 保存可选文件失败 | Identifier: {}, OriginalFilename: {}, Version: {}",
                    fileTypeIdentifier, file.getOriginalFilename(), newVersion, e);
            // 对于可选文件，可以选择只记录错误而不抛出异常，让主流程继续
            // throw new ServiceException("保存 " + fileTypeIdentifier + " 文件失败: " + e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 构建新版本文件的存储目录
     */
    private Path buildNewVersionDirectory(String referenceFilePath, String newVersion) throws java.io.IOException {
        if (StringUtils.isBlank(referenceFilePath)) {
            throw new ServiceException("无法确定文件存储目录：参照文件路径为空");
        }
        // /path/to/model/V1.0/file_v1.0.xlsx -> /path/to/model/V<newVersion>/
        Path parentDir = Paths.get(referenceFilePath).getParent(); // V1.0目录
        if (parentDir == null) {
            throw new ServiceException("无法获取参照文件的父目录");
        }
        Path grandParentDir = parentDir.getParent(); // model目录
        if (grandParentDir == null) {
            throw new ServiceException("无法获取参照文件的祖父目录");
        }

        Path newVersionDir = grandParentDir.resolve("V" + newVersion);
        if (!java.nio.file.Files.exists(newVersionDir)) {
            java.nio.file.Files.createDirectories(newVersionDir);
            log.info("[DirectoryCreation] 新版本目录已创建 | Path: {}", newVersionDir);
        }
        return newVersionDir;
    }

    /**
     * 创建并插入新版本的文件记录
     */
    private Files createAndInsertNewFileRecord(Files oldFile, FileChangeRequest request, Path bomFilePath) {
        Files newFile = new Files();
        BeanUtils.copyProperties(oldFile, newFile); // 复制基础信息

        newFile.setId(null) // ID 必须为 null 以便自增
                .setVersion(request.getNewVersion())
                .setStatus(FilesStatus.ACTIVE)
                .setFilePath(bomFilePath.toString())
                .setFileName(bomFilePath.getFileName().toString())
                .setCreatedAt(null) // 创建时间由数据库默认生成
                .setUpdatedAt(null); // 更新时间由数据库默认生成

        // 如果请求中指定了新的 productModelId，则使用它
        if (request.getProductModelId() != null) {
            newFile.setProductModelId(request.getProductModelId());
        }

        log.info("准备插入新文件记录: FileName={}, FilePath={}, Version={}",
                 newFile.getFileName(), newFile.getFilePath(), newFile.getVersion());

        fileRepository.insertFiles(newFile);
        if (newFile.getId() == null) {
            log.error("[NewFileRecord] 插入新文件记录后未能获取到自增ID | Version: {}, Path: {}", newFile.getVersion(), newFile.getFilePath());
            throw new ServiceException("创建新文件记录失败");
        }
        log.info("[NewFileRecord] 新文件记录已创建 | FileId: {}, Version: {}, Path: {}",
                newFile.getId(), newFile.getVersion(), newFile.getFilePath());
        return newFile;
    }

    /**
     * 判断文件是否为 BOM 文件
     */
    private boolean isBomFile(Files file) {
        return file != null &&
                file.getFileType() != null &&
                "BOM".equals(file.getFileType().name()) &&
                StringUtils.isNotBlank(file.getSubType());
    }
}
