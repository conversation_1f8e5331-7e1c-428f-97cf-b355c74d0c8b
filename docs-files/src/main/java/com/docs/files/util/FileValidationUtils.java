package com.docs.files.util;

import com.docs.common.core.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件验证工具类
 * 
 * <AUTHOR>
 */
public class FileValidationUtils {

    /**
     * 验证文件是否有效
     * 
     * @param files 文件数组
     * @return 如果验证失败返回错误结果，验证成功返回null
     */
    public static AjaxResult validateFiles(MultipartFile[] files) {
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                continue;
            }

            String filename = file.getOriginalFilename();
            if (filename == null || filename.isEmpty()) {
                return AjaxResult.error("文件名不能为空");
            }
        }
        return null;
    }
    
    /**
     * 验证Excel文件类型
     * 
     * @param files 文件数组
     * @return 如果验证失败返回错误结果，验证成功返回null
     */
    public static AjaxResult validateExcelFiles(MultipartFile[] files) {
        AjaxResult basicValidation = validateFiles(files);
        if (basicValidation != null) {
            return basicValidation;
        }
        
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                continue;
            }
            
            String filename = file.getOriginalFilename().toLowerCase();
            if (!filename.endsWith(".xlsx") && !filename.endsWith(".xls")) {
                return AjaxResult.error("只能上传Excel文件(xlsx或xls格式)");
            }
        }
        return null;
    }
    
    /**
     * 验证PDF文件类型
     * 
     * @param file 文件
     * @return 如果验证失败返回错误结果，验证成功返回null
     */
    public static AjaxResult validatePdfFile(MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择要上传的文件");
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null || filename.isEmpty()) {
            return AjaxResult.error("文件名不能为空");
        }
        
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        if (!"pdf".equals(extension)) {
            return AjaxResult.error("只能上传PDF格式的图纸文件");
        }
        
        return null;
    }
    
    /**
     * 验证2D/3D压缩文件类型
     * 
     * @param file 文件
     * @return 如果验证失败返回错误结果，验证成功返回null
     */
    public static AjaxResult validate2D3DCompressedFile(MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择要上传的文件");
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null || filename.isEmpty()) {
            return AjaxResult.error("文件名不能为空");
        }
        
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        String[] allowedExtensions = {"zip", "rar", "7z", "tar", "gz", "bz2"};
        
        boolean isValidExtension = false;
        for (String allowedExt : allowedExtensions) {
            if (allowedExt.equals(extension)) {
                isValidExtension = true;
                break;
            }
        }
        
        if (!isValidExtension) {
            return AjaxResult.error("2D/3D图纸只能上传压缩文件格式（zip、rar、7z、tar、gz、bz2）");
        }
        
        return null;
    }
    
    /**
     * 根据子类型验证图纸文件
     * 
     * @param file 文件
     * @param subType 子类型 (PDF, 2D, 3D, 2D/3D)
     * @return 如果验证失败返回错误结果，验证成功返回null
     */
    public static AjaxResult validateDrawingFileBySubType(MultipartFile file, String subType) {
        // 基础验证
        if (file.isEmpty()) {
            return AjaxResult.error("请选择要上传的文件");
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null || filename.isEmpty()) {
            return AjaxResult.error("文件名不能为空");
        }
        
        // 根据子类型进行不同的验证
        if ("PDF".equals(subType)) {
            return validatePdfFile(file);
        } else if ("2D/3D".equals(subType)) {
            return validate2D3DCompressedFile(file);
        } else if ("2D".equals(subType) || "3D".equals(subType)) {
            // 2D和3D独立选项不做文件类型校验，只做基础验证
            return null;
        } else {
            return AjaxResult.error("不支持的图纸类型: " + subType);
        }
    }

    /**
     * 根据子类型验证BOM文件
     * 
     * @param files 文件数组
     * @param subType 子类型 (EBOM, PBOM)
     * @return 如果验证失败返回错误结果，验证成功返回null
     */
    public static AjaxResult validateBomFileBySubType(MultipartFile[] files, String subType) {
        // 验证subType是否为有效的BOM类型
        if (!"EBOM".equals(subType) && !"PBOM".equals(subType)) {
            return AjaxResult.error("不支持的BOM类型: " + subType);
        }
        
        // BOM文件应该是Excel格式
        return validateExcelFiles(files);
    }
}
