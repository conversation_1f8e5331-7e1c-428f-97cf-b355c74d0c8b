package com.docs.files.util;

import com.docs.common.constant.FileConstants;
import com.docs.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 文件路径和存储工具类 (职责已合并)
 */
@Component
@Slf4j
public class FilePathUtil {
    /**
     * 构建目标目录路径（新规则）
     * 规则：BASE_UPLOAD_PATH/yyyyMMdd/fileType/V{version}
     *
     * @param fileType 文件类型
     * @param version 版本号
     * @return 目标目录路径字符串
     */
    public String buildTargetDirectory(String fileType, String version) {
        String date = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
        return Paths.get(FileConstants.BASE_UPLOAD_PATH, date, fileType, "V" + version).toString();
    }

    /**
     * 构建研发文件目标目录路径
     * 规则：BASE_UPLOAD_PATH/rd-files/productModel/V{version}
     *
     * @param productModel 产品型号
     * @param version 版本号
     * @return 目标目录路径字符串
     */
    public String buildRdFileTargetDirectory(String productModel, String version) {
        return Paths.get(FileConstants.BASE_UPLOAD_PATH, "rd-files", productModel, "V" + version).toString();
    }
    
    /**
     * 保持原始文件名，并在基础名称后附加版本号
     * 与原AbstractFileStrategy.preserveOriginalFilename保持一致
     * 
     * @param originalFilename 原始文件名
     * @param version 版本号
     * @return 处理后的文件名
     */
    public String preserveOriginalFilename(String originalFilename, String version) {
        if (StringUtils.isEmpty(originalFilename)) {
            return "uploaded_file_v" + version + ".bin";
        }
        String extension = FilenameUtils.getExtension(originalFilename);
        String baseName = FilenameUtils.getBaseName(originalFilename);
        String safeVersion = version.replaceAll("[^a-zA-Z0-9.-]", "_");
        return String.format("%s_v%s.%s", baseName, safeVersion, extension);
    }
    
    /**
     * 检查文件扩展名是否有效
     * 与原AbstractFileStrategy.isValidFileExtension保持一致
     * 
     * @param filename 文件名
     * @return 是否有效
     */
    public boolean isValidFileExtension(String filename) {
        if (StringUtils.isEmpty(filename)) {
            return false;
        }
        String extension = FilenameUtils.getExtension(filename).toLowerCase();
        return FileConstants.ALLOWED_EXTENSIONS.contains(extension);
    }
    
    /**
     * 确保目录存在，如果不存在则创建
     * 
     * @param directoryPath 目录路径字符串
     * @return 目录的 Path 对象
     * @throws ServiceException 如果创建目录失败
     */
    public Path ensureDirectoryExists(String directoryPath) {
        Path directory = Paths.get(directoryPath);
        if (!Files.exists(directory)) {
            try {
                Files.createDirectories(directory);
                log.info("创建目录: {}", directory);
            } catch (IOException e) {
                log.error("无法创建目录: {}", directoryPath, e);
                throw new ServiceException("无法创建文件保存目录: " + directoryPath);
            }
        }
        return directory;
    }

    /**
     * 存储文件到指定目录，并返回最终路径
     * (合并了原 FileSystemStorage 的职责)
     * 
     * @param file 上传的文件
     * @param targetDir 目标目录字符串
     * @param fileName 最终文件名
     * @return 保存后的文件 Path 对象
     * @throws ServiceException 如果创建目录或保存文件失败
     */
    public Path storeFile(MultipartFile file, String targetDir, String fileName) {
        // 1. 确保目录存在
        Path directory = ensureDirectoryExists(targetDir);

        // 2. 解析最终文件路径
        Path filePath = directory.resolve(fileName);

        // 3. 保存文件 (执行 IO 操作)
        try {
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            log.info("文件已保存: {}", filePath);
        } catch (IOException e) {
            log.error("无法保存文件 {} 到 {}: {}", fileName, filePath, e.getMessage(), e);
            // 可以考虑在 ServiceException 中包含原始异常
            throw new ServiceException("文件保存失败: " + fileName);
        }
        return filePath;
    }
}
