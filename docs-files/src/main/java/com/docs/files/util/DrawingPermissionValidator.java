package com.docs.files.util;

import com.docs.files.config.DrawingPermissionProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 图纸权限配置验证器
 * 在应用启动时验证配置的正确性
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DrawingPermissionValidator {

    private final DrawingPermissionProperties drawingPermissionProperties;

    /**
     * 应用启动完成后验证配置
     */
    @EventListener(ApplicationReadyEvent.class)
    public void validateConfigurationOnStartup() {
        log.info("开始验证图纸权限配置...");
        
        try {
            validateConfiguration();
            log.info("图纸权限配置验证通过");
        } catch (Exception e) {
            log.error("图纸权限配置验证失败", e);
        }
    }

    /**
     * 验证配置的完整性和正确性
     */
    public void validateConfiguration() {
        // 验证基本配置
        validateBasicConfiguration();
        
        // 验证权限映射
        validatePermissionMappings();
        
        // 验证默认配置
        validateDefaultConfiguration();
        
        // 打印配置摘要
        printConfigurationSummary();
    }

    /**
     * 验证基本配置
     */
    private void validateBasicConfiguration() {
        if (drawingPermissionProperties.getValidation() == null) {
            throw new IllegalStateException("验证配置不能为空");
        }
        
        if (drawingPermissionProperties.getDefaultConfig() == null) {
            throw new IllegalStateException("默认配置不能为空");
        }
        
        log.debug("基本配置验证通过");
    }

    /**
     * 验证权限映射
     */
    private void validatePermissionMappings() {
        if (drawingPermissionProperties.getSubtypeMappings() == null) {
            throw new IllegalStateException("权限映射配置不能为空");
        }
        
        if (drawingPermissionProperties.getSubtypeMappings().isEmpty()) {
            log.warn("权限映射配置为空，所有图纸类型将使用默认处理逻辑");
            return;
        }
        
        // 验证每个映射的有效性
        drawingPermissionProperties.getSubtypeMappings().forEach((subType, permissionCode) -> {
            if (subType == null || subType.trim().isEmpty()) {
                throw new IllegalStateException("图纸子类型不能为空");
            }
            
            if (permissionCode == null || permissionCode.trim().isEmpty()) {
                throw new IllegalStateException("权限代码不能为空: " + subType);
            }
            
            if (!permissionCode.matches("^[a-zA-Z0-9:_-]+$")) {
                log.warn("权限代码格式可能不正确: {} -> {}", subType, permissionCode);
            }
        });
        
        log.debug("权限映射验证通过，共 {} 个映射", drawingPermissionProperties.getSubtypeMappings().size());
    }

    /**
     * 验证默认配置
     */
    private void validateDefaultConfiguration() {
        String unknownPermission = drawingPermissionProperties.getDefaultConfig().getUnknownSubtypePermission();
        boolean allowUnknown = drawingPermissionProperties.getDefaultConfig().isAllowUnknownSubtype();
        
        if (!allowUnknown && (unknownPermission == null || unknownPermission.trim().isEmpty())) {
            log.warn("不允许未知子类型但未配置默认权限，可能导致所有未知类型被拒绝");
        }
        
        log.debug("默认配置验证通过");
    }

    /**
     * 打印配置摘要
     */
    private void printConfigurationSummary() {
        log.info("=== 图纸权限配置摘要 ===");
        log.info("权限检查启用状态: {}", drawingPermissionProperties.getValidation().isEnabled());
        log.info("大小写敏感: {}", drawingPermissionProperties.getValidation().isCaseSensitive());
        log.info("记录权限日志: {}", drawingPermissionProperties.getValidation().isLogPermissionChecks());
        log.info("允许未知子类型: {}", drawingPermissionProperties.getDefaultConfig().isAllowUnknownSubtype());
        log.info("未知子类型权限: {}", drawingPermissionProperties.getDefaultConfig().getUnknownSubtypePermission());
        log.info("权限映射数量: {}", drawingPermissionProperties.getSubtypeMappings().size());
        
        if (!drawingPermissionProperties.getSubtypeMappings().isEmpty()) {
            log.info("支持的图纸类型: {}", String.join(", ", drawingPermissionProperties.getSupportedSubTypes()));
        }
        log.info("========================");
    }

    /**
     * 测试特定子类型的权限映射
     */
    public boolean testSubTypeMapping(String subType) {
        try {
            String permissionCode = drawingPermissionProperties.getPermissionCode(subType);
            boolean hasMapping = drawingPermissionProperties.hasPermissionMapping(subType);
            
            log.debug("测试子类型 '{}': 权限代码='{}', 有映射={}", subType, permissionCode, hasMapping);
            return hasMapping;
        } catch (Exception e) {
            log.error("测试子类型 '{}' 失败", subType, e);
            return false;
        }
    }

    /**
     * 获取配置健康状态
     */
    public boolean isConfigurationHealthy() {
        try {
            validateConfiguration();
            return true;
        } catch (Exception e) {
            log.warn("配置健康检查失败", e);
            return false;
        }
    }
}
