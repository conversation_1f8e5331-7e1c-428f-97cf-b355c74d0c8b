package com.docs.files.config.state.action;

import com.docs.common.enums.ApprovalActionType;
import com.docs.common.enums.ApprovalEvent;
import com.docs.common.enums.ApprovalStatus;
import com.docs.common.jooq.generated.tables.records.ApprovalActionRecordsRecord;
import org.jooq.DSLContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;

import java.time.LocalDateTime;

import static com.docs.common.jooq.generated.Tables.APPROVAL_ACTION_RECORDS;

/**
 * 审批状态机动作的基类，提供一些共通的功能
 */
public abstract class BaseApprovalAction implements Action<ApprovalStatus, ApprovalEvent> {

    protected final Logger logger = LoggerFactory.getLogger(getClass());
    protected final DSLContext dsl;

    public BaseApprovalAction(DSLContext dsl) {
        this.dsl = dsl;
    }

    /**
     * 从状态机上下文中获取审批请求ID
     */
    protected Long getApprovalRequestId(StateContext<ApprovalStatus, ApprovalEvent> context) {
        return Long.valueOf(context.getStateMachine().getId());
    }

    /**
     * 从消息头中获取操作人ID
     */
    protected Long getActorId(StateContext<ApprovalStatus, ApprovalEvent> context) {
        return context.getMessageHeaders().get("actorId", Long.class);
    }

    /**
     * 从消息头中获取评论/意见
     */
    protected String getComment(StateContext<ApprovalStatus, ApprovalEvent> context) {
        return context.getMessageHeaders().get("comment", String.class);
    }

    /**
     * 记录审批操作
     */
    protected void createActionRecord(Long approvalRequestId, Long actorId, 
                                    ApprovalActionType actionType, 
                                    String comment, 
                                    ApprovalStatus previousStatus, 
                                    ApprovalStatus nextStatus) {
        logger.info("Creating action record: requestId={}, actionType={}, from {} to {}", 
                 approvalRequestId, actionType, previousStatus, nextStatus);
        
        ApprovalActionRecordsRecord actionRecord = dsl.newRecord(APPROVAL_ACTION_RECORDS);
        actionRecord.setApprovalRequestId(approvalRequestId);
        actionRecord.setActorId(actorId);
        actionRecord.setActionType(actionType.name());
        actionRecord.setComment(comment);
        actionRecord.setPreviousStatus(previousStatus != null ? previousStatus.name() : null);
        actionRecord.setNextStatus(nextStatus != null ? nextStatus.name() : null);
        actionRecord.setActionTime(LocalDateTime.now());
        actionRecord.store();
    }
}
