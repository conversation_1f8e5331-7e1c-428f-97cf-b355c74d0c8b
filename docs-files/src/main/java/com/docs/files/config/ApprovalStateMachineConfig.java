package com.docs.files.config;

import com.docs.common.enums.ApprovalEvent;
import com.docs.common.enums.ApprovalStatus;
import com.docs.files.config.state.action.*;
import com.docs.files.config.persist.JooqStateMachinePersist;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;

import java.util.EnumSet;

@Configuration
@EnableStateMachineFactory // 使用工厂创建每个审批请求的多个实例
@RequiredArgsConstructor
public class ApprovalStateMachineConfig extends EnumStateMachineConfigurerAdapter<ApprovalStatus, ApprovalEvent> {

    public static final String STATE_MACHINE_ID = "downloadApprovalStateMachine";

    // 注入各状态转换的动作类
    private final SupervisorApproveAction supervisorApproveAction;
    private final SupervisorRejectAction supervisorRejectAction;
    private final ManagerApproveAction managerApproveAction;
    private final ManagerRejectAction managerRejectAction;
    private final CancelRequestAction cancelRequestAction;

    @Override
    public void configure(StateMachineConfigurationConfigurer<ApprovalStatus, ApprovalEvent> config) throws Exception {
        config
            .withConfiguration()
            .machineId(STATE_MACHINE_ID)
            .autoStartup(false); // 实例由服务按需启动
    }

    @Override
    public void configure(StateMachineStateConfigurer<ApprovalStatus, ApprovalEvent> states) throws Exception {
        states
            .withStates()
            // 提交新请求并创建状态机实例时，初始状态将被设置。
            // 对于普通员工：PENDING_SUPERVISOR_APPROVAL 将是第一个运行状态
            // 对于主管：PENDING_MANAGER_APPROVAL 将是第一个运行状态
            .initial(ApprovalStatus.PENDING_SUPERVISOR_APPROVAL)
            .states(EnumSet.allOf(ApprovalStatus.class))
            .end(ApprovalStatus.APPROVED)
            .end(ApprovalStatus.REJECTED_BY_SUPERVISOR)
            .end(ApprovalStatus.REJECTED_BY_MANAGER)
            .end(ApprovalStatus.CANCELLED);
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<ApprovalStatus, ApprovalEvent> transitions) throws Exception {
        // 主管审批流程
        transitions
            .withExternal()
                .source(ApprovalStatus.PENDING_SUPERVISOR_APPROVAL).target(ApprovalStatus.PENDING_MANAGER_APPROVAL)
                .event(ApprovalEvent.SUPERVISOR_APPROVE)
                // 主管已在动作中验证身份，这里不需要额外的守卫
                .action(supervisorApproveAction)
            .and()
            .withExternal()
                .source(ApprovalStatus.PENDING_SUPERVISOR_APPROVAL).target(ApprovalStatus.REJECTED_BY_SUPERVISOR)
                .event(ApprovalEvent.SUPERVISOR_REJECT)
                .action(supervisorRejectAction)
            .and()
        // 经理审批流程
            .withExternal()
                .source(ApprovalStatus.PENDING_MANAGER_APPROVAL).target(ApprovalStatus.APPROVED)
                .event(ApprovalEvent.MANAGER_APPROVE)
                .action(managerApproveAction)
            .and()
            .withExternal()
                .source(ApprovalStatus.PENDING_MANAGER_APPROVAL).target(ApprovalStatus.REJECTED_BY_MANAGER)
                .event(ApprovalEvent.MANAGER_REJECT)
                .action(managerRejectAction)
            .and()
        // 请求人取消（可在待主管或待经理状态下取消）
            .withExternal()
                .source(ApprovalStatus.PENDING_SUPERVISOR_APPROVAL).target(ApprovalStatus.CANCELLED)
                .event(ApprovalEvent.CANCEL_BY_REQUESTER)
                .action(cancelRequestAction)
            .and()
            .withExternal()
                .source(ApprovalStatus.PENDING_MANAGER_APPROVAL).target(ApprovalStatus.CANCELLED)
                .event(ApprovalEvent.CANCEL_BY_REQUESTER)
                .action(cancelRequestAction);
    }

    // Added Beans for persistence
    @Bean
    public JooqStateMachinePersist jooqStateMachinePersist(DSLContext dslContext, ObjectMapper objectMapper) {
        return new JooqStateMachinePersist(dslContext, objectMapper);
    }

    @Bean
    public StateMachinePersister<ApprovalStatus, ApprovalEvent, String> persister(
            JooqStateMachinePersist jooqStateMachinePersist) {
        return new DefaultStateMachinePersister<>(jooqStateMachinePersist);
    }
}
