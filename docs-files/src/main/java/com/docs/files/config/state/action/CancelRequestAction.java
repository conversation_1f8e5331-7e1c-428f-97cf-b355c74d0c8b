package com.docs.files.config.state.action;

import com.docs.common.enums.ApprovalActionType;
import com.docs.common.enums.ApprovalEvent;
import com.docs.common.enums.ApprovalStatus;
import com.docs.common.jooq.generated.tables.records.DownloadApprovalRequestsRecord;

import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.statemachine.StateContext;
import org.springframework.stereotype.Component;
import static com.docs.common.jooq.generated.Tables.DOWNLOAD_APPROVAL_REQUESTS;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 申请人撤销请求动作
 * 当状态从 PENDING_SUPERVISOR_APPROVAL 或 PENDING_MANAGER_APPROVAL 转换到 CANCELLED 时执行
 */
@Component
@Slf4j
public class CancelRequestAction extends BaseApprovalAction {

    // 可被撤销的状态列表
    private static final List<ApprovalStatus> CANCELLABLE_STATUSES = Arrays.asList(
            ApprovalStatus.PENDING_SUPERVISOR_APPROVAL,
            ApprovalStatus.PENDING_MANAGER_APPROVAL
    );

    public CancelRequestAction(DSLContext dsl) {
        super(dsl);
    }

    @Override
    public void execute(StateContext<ApprovalStatus, ApprovalEvent> context) {
        Long approvalRequestId = getApprovalRequestId(context);
        Long actorId = getActorId(context);
        String comment = getComment(context);
        
        logger.info("执行申请撤销动作，审批请求ID: {}, 操作人ID: {}", approvalRequestId, actorId);
        
        // 获取当前请求记录，确认状态
        DownloadApprovalRequestsRecord requestRecord = dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
                .fetchOne();
        
        if (requestRecord == null) {
            logger.error("未找到审批请求: {}", approvalRequestId);
            throw new IllegalStateException("审批请求不存在: " + approvalRequestId);
        }
        
        // 确认当前状态是可撤销的状态
        ApprovalStatus currentStatus = ApprovalStatus.valueOf(requestRecord.getStatus());
        if (!CANCELLABLE_STATUSES.contains(currentStatus)) {
            logger.error("当前状态 {} 不可撤销", currentStatus);
            throw new IllegalStateException("当前状态不可撤销: " + currentStatus);
        }
        
        // 确认操作人是申请人自己
        if (!actorId.equals(requestRecord.getRequesterId())) {
            logger.error("操作人 {} 不是申请人 {}", actorId, requestRecord.getRequesterId());
            throw new IllegalStateException("只有申请人自己可以撤销此请求");
        }
        
        // 更新审批请求状态
        dsl.update(DOWNLOAD_APPROVAL_REQUESTS)
           .set(DOWNLOAD_APPROVAL_REQUESTS.STATUS, ApprovalStatus.CANCELLED.name())
           .set(DOWNLOAD_APPROVAL_REQUESTS.UPDATE_TIME, LocalDateTime.now())
           .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
           .execute();
        
        // 记录操作
        createActionRecord(
            approvalRequestId,
            actorId,
            ApprovalActionType.CANCEL_REQUEST,
            comment != null ? comment : "申请人撤销请求",
            currentStatus,
            ApprovalStatus.CANCELLED
        );
        
        logger.info("申请撤销动作完成。请求 {} 状态已从 {} 更新为 {}", 
                  approvalRequestId, currentStatus, ApprovalStatus.CANCELLED);
    }
}
