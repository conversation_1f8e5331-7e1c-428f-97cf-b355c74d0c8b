package com.docs.files.config.state.action;

import static com.docs.common.jooq.generated.Tables.APPROVAL_ACTION_RECORDS;
import static com.docs.common.jooq.generated.Tables.DOWNLOAD_APPROVAL_REQUESTS;

import org.jooq.DSLContext;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.state.State;
import org.springframework.stereotype.Component;

// SysUser通过UserService间接使用，不再直接导入
import com.docs.common.constant.UserConstants;
import com.docs.common.enums.ApprovalActionType;
import com.docs.common.enums.ApprovalEvent;
import com.docs.common.enums.ApprovalStatus;
import com.docs.common.jooq.generated.tables.records.DownloadApprovalRequestsRecord;
import com.docs.files.service.UserService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 主管批准动作
 * 当状态从 PENDING_SUPERVISOR_APPROVAL 转换到 PENDING_MANAGER_APPROVAL 时执行
 */
@Component
@Slf4j
public class SupervisorApproveAction extends BaseApprovalAction {

    private final UserService userService;

    public SupervisorApproveAction(DSLContext dsl, UserService userService) {
        super(dsl);
        this.userService = userService;
    }

    @Override
    public void execute(StateContext<ApprovalStatus, ApprovalEvent> context) {
        Long approvalRequestId = getApprovalRequestId(context);
        Long actorId = getActorId(context);
        String comment = getComment(context);

        logger.info("执行主管批准动作，审批请求ID: {}, 操作人ID: {}", approvalRequestId, actorId);

        // 获取当前请求记录，确认状态
        DownloadApprovalRequestsRecord requestRecord = dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
                .fetchOne();

        if (requestRecord == null) {
            logger.error("未找到审批请求: {}", approvalRequestId);
            throw new IllegalStateException("审批请求不存在: " + approvalRequestId);
        }

        // 确认当前状态是 PENDING_SUPERVISOR_APPROVAL
        ApprovalStatus currentStatus = ApprovalStatus.valueOf(requestRecord.getStatus());
        if (currentStatus != ApprovalStatus.PENDING_SUPERVISOR_APPROVAL) {
            logger.error("当前状态不符: {}, 期望状态: {}",
                       currentStatus, ApprovalStatus.PENDING_SUPERVISOR_APPROVAL);
            throw new IllegalStateException("当前状态不符: " + currentStatus);
        }

        // 确认操作人是指定的主管
        if (!actorId.equals(requestRecord.getSupervisorId())) {
            logger.error("操作人 {} 不是指定的主管 {}", actorId, requestRecord.getSupervisorId());
            throw new IllegalStateException("只有指定的主管可以批准此请求");
        }

        // 确定经理ID (这里是简化实现，实际应该根据业务规则确定)
        Long managerId = determineManagerId(requestRecord.getRequesterId());

        // 更新审批请求状态
        dsl.update(DOWNLOAD_APPROVAL_REQUESTS)
           .set(DOWNLOAD_APPROVAL_REQUESTS.STATUS, ApprovalStatus.PENDING_MANAGER_APPROVAL.name())
           .set(DOWNLOAD_APPROVAL_REQUESTS.MANAGER_ID, managerId)
           .set(DOWNLOAD_APPROVAL_REQUESTS.UPDATE_TIME, LocalDateTime.now())
           .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
           .execute();

        // 记录操作
        createActionRecord(
            approvalRequestId,
            actorId,
            ApprovalActionType.SUPERVISOR_APPROVE,
            comment,
            ApprovalStatus.PENDING_SUPERVISOR_APPROVAL,
            ApprovalStatus.PENDING_MANAGER_APPROVAL
        );

        log.info("主管批准动作完成。请求 {} 状态已更新为 {} 并分配给经理 {}",
                  approvalRequestId, ApprovalStatus.PENDING_MANAGER_APPROVAL, managerId);
    }

    /**
     * 确定经理ID的方法
     * 使用UserService的findApproverByRole方法
     *
     * @param requesterId 申请人ID
     * @return 经理ID
     */
    private Long determineManagerId(Long requesterId) {
        return userService.findApproverByRole(requesterId, UserConstants.POST_MANAGER);
    }
}
