package com.docs.files.config.persist;

import com.docs.common.enums.ApprovalEvent;
import com.docs.common.enums.ApprovalStatus;
import com.docs.common.jooq.generated.tables.records.StateMachineContextsRecord;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.statemachine.ExtendedState;
import org.springframework.statemachine.StateMachineContext;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.support.DefaultExtendedState;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.docs.common.jooq.generated.Tables.STATE_MACHINE_CONTEXTS;

@Slf4j
@RequiredArgsConstructor
public class JooqStateMachinePersist implements StateMachinePersist<ApprovalStatus, ApprovalEvent, String> {

    private final DSLContext dsl;
    private final ObjectMapper objectMapper;

    @Override
    public void write(StateMachineContext<ApprovalStatus, ApprovalEvent> context, String machineId) {
        log.debug("持久化状态机上下文，machineId: {}", machineId);
        try {
            String serializedExtendedState = null;
            if (context.getExtendedState() != null && context.getExtendedState().getVariables() != null) {
                serializedExtendedState = objectMapper.writeValueAsString(context.getExtendedState().getVariables());
            }

            dsl.insertInto(STATE_MACHINE_CONTEXTS)
                .set(STATE_MACHINE_CONTEXTS.MACHINE_ID, machineId)
                .set(STATE_MACHINE_CONTEXTS.CURRENT_STATE, context.getState().name())
                .set(STATE_MACHINE_CONTEXTS.STATE_CONTEXT, serializedExtendedState)
                // MySQL表已配置ON UPDATE CURRENT_TIMESTAMP, 无需显式设置 last_updated
                .onDuplicateKeyUpdate()
                .set(STATE_MACHINE_CONTEXTS.CURRENT_STATE, context.getState().name())
                .set(STATE_MACHINE_CONTEXTS.STATE_CONTEXT, serializedExtendedState)
                .execute();
            log.info("成功持久化状态机上下文，machineId: {}", machineId);
        } catch (JsonProcessingException e) {
            log.error("序列化状态机上下文失败，machineId: {}", machineId, e);
            throw new RuntimeException("序列化状态机上下文失败", e);
        } catch (Exception e) {
            log.error("持久化状态机上下文失败，machineId: {}", machineId, e);
            throw new RuntimeException("持久化状态机上下文失败", e);
        }
    }

    @Override
    public StateMachineContext<ApprovalStatus, ApprovalEvent> read(String machineId) {
        log.debug("读取状态机上下文，machineId: {}", machineId);
        try {
            StateMachineContextsRecord record = dsl.selectFrom(STATE_MACHINE_CONTEXTS)
                .where(STATE_MACHINE_CONTEXTS.MACHINE_ID.eq(machineId))
                .fetchOne();

            if (record == null) {
                log.info("未找到持久化的状态机上下文，machineId: {}", machineId);
                return null;
            }

            ApprovalStatus status = ApprovalStatus.valueOf(record.getCurrentState());
            Map<Object, Object> extendedStateVariables = new HashMap<>();

            if (record.getStateContext() != null && !record.getStateContext().isEmpty()) {
                extendedStateVariables = objectMapper.readValue(record.getStateContext(), new TypeReference<Map<Object, Object>>() {});
            }

            ExtendedState extendedState = new DefaultExtendedState(extendedStateVariables);

            log.info("成功读取持久化的状态机上下文，machineId: {}, 状态: {}, 上下文大小: {}",
                    machineId, status, extendedState.getVariables().size());

            return new DefaultStateMachineContext<ApprovalStatus, ApprovalEvent>(status, null, null, extendedState, null, machineId);

        } catch (IOException e) {
            log.error("反序列化状态机上下文失败，machineId: {}", machineId, e);
            throw new RuntimeException("反序列化状态机上下文失败", e);
        } catch (Exception e) {
            log.error("读取状态机上下文失败，machineId: {}", machineId, e);
            throw new RuntimeException("读取状态机上下文失败", e);
        }
    }
}
