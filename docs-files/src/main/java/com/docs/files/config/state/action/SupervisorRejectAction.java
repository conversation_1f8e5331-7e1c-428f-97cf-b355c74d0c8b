package com.docs.files.config.state.action;

import com.docs.common.enums.ApprovalActionType;
import com.docs.common.enums.ApprovalEvent;
import com.docs.common.enums.ApprovalStatus;
import com.docs.common.jooq.generated.tables.records.DownloadApprovalRequestsRecord;

import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.statemachine.StateContext;
import org.springframework.stereotype.Component;
import static com.docs.common.jooq.generated.Tables.DOWNLOAD_APPROVAL_REQUESTS;

import java.time.LocalDateTime;

/**
 * 主管拒绝动作
 * 当状态从 PENDING_SUPERVISOR_APPROVAL 转换到 REJECTED_BY_SUPERVISOR 时执行
 */
@Component
@Slf4j
public class SupervisorRejectAction extends BaseApprovalAction {

    public SupervisorRejectAction(DSLContext dsl) {
        super(dsl);
    }

    @Override
    public void execute(StateContext<ApprovalStatus, ApprovalEvent> context) {
        Long approvalRequestId = getApprovalRequestId(context);
        Long actorId = getActorId(context);
        String comment = getComment(context);
        
        logger.info("执行主管拒绝动作，审批请求ID: {}, 操作人ID: {}", approvalRequestId, actorId);
        
        // 获取当前请求记录，确认状态
        DownloadApprovalRequestsRecord requestRecord = dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
                .fetchOne();
        
        if (requestRecord == null) {
            logger.error("未找到审批请求: {}", approvalRequestId);
            throw new IllegalStateException("审批请求不存在: " + approvalRequestId);
        }
        
        // 确认当前状态是 PENDING_SUPERVISOR_APPROVAL
        ApprovalStatus currentStatus = ApprovalStatus.valueOf(requestRecord.getStatus());
        if (currentStatus != ApprovalStatus.PENDING_SUPERVISOR_APPROVAL) {
            logger.error("当前状态不符: {}, 期望状态: {}", 
                       currentStatus, ApprovalStatus.PENDING_SUPERVISOR_APPROVAL);
            throw new IllegalStateException("当前状态不符: " + currentStatus);
        }
        
        // 确认操作人是指定的主管
        if (!actorId.equals(requestRecord.getSupervisorId())) {
            logger.error("操作人 {} 不是指定的主管 {}", actorId, requestRecord.getSupervisorId());
            throw new IllegalStateException("只有指定的主管可以拒绝此请求");
        }
        
        // 更新审批请求状态
        dsl.update(DOWNLOAD_APPROVAL_REQUESTS)
           .set(DOWNLOAD_APPROVAL_REQUESTS.STATUS, ApprovalStatus.REJECTED_BY_SUPERVISOR.name())
           .set(DOWNLOAD_APPROVAL_REQUESTS.UPDATE_TIME, LocalDateTime.now())
           .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
           .execute();
        
        // 记录操作
        createActionRecord(
            approvalRequestId,
            actorId,
            ApprovalActionType.SUPERVISOR_REJECT,
            comment,
            ApprovalStatus.PENDING_SUPERVISOR_APPROVAL,
            ApprovalStatus.REJECTED_BY_SUPERVISOR
        );
        
        logger.info("主管拒绝动作完成。请求 {} 状态已更新为 {}", 
                  approvalRequestId, ApprovalStatus.REJECTED_BY_SUPERVISOR);
    }
}
