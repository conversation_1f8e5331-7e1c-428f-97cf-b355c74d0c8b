package com.docs.files.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.HashMap;
import java.util.Map;

/**
 * 图纸权限配置属性类
 * 从配置文件中加载图纸子类型权限映射关系
 * 
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "drawing.permissions")
@PropertySource(value = "classpath:config/drawing-permissions.yml", factory = YamlPropertySourceFactory.class)
public class DrawingPermissionProperties {

    /**
     * 图纸子类型到权限代码的映射
     */
    private Map<String, String> subtypeMappings = new HashMap<>();

    /**
     * 默认权限配置
     */
    private DefaultConfig defaultConfig = new DefaultConfig();

    /**
     * 权限验证配置
     */
    private ValidationConfig validation = new ValidationConfig();

    /**
     * 默认配置
     */
    @Data
    public static class DefaultConfig {
        /**
         * 当子类型为空或未知时是否允许下载（向后兼容）
         */
        private boolean allowUnknownSubtype = true;

        /**
         * 未知子类型的默认权限代码
         */
        private String unknownSubtypePermission = "files:download:unknown";
    }

    /**
     * 验证配置
     */
    @Data
    public static class ValidationConfig {
        /**
         * 是否启用图纸子类型权限检查
         */
        private boolean enabled = true;

        /**
         * 是否对子类型进行大小写敏感匹配
         */
        private boolean caseSensitive = false;

        /**
         * 是否记录权限检查日志
         */
        private boolean logPermissionChecks = true;
    }

    /**
     * 根据子类型获取权限代码
     * 
     * @param subType 图纸子类型
     * @return 对应的权限代码，如果未找到则返回null
     */
    public String getPermissionCode(String subType) {
        if (subType == null || subType.trim().isEmpty()) {
            return null;
        }

        // 根据配置决定是否大小写敏感
        String key = validation.caseSensitive ? subType : subType.toUpperCase();
        
        // 如果不是大小写敏感，需要将映射的key也转换为大写进行匹配
        if (!validation.caseSensitive) {
            for (Map.Entry<String, String> entry : subtypeMappings.entrySet()) {
                if (entry.getKey().toUpperCase().equals(key)) {
                    return entry.getValue();
                }
            }
            return null;
        } else {
            return subtypeMappings.get(key);
        }
    }

    /**
     * 检查是否有指定子类型的权限映射
     * 
     * @param subType 图纸子类型
     * @return 是否存在权限映射
     */
    public boolean hasPermissionMapping(String subType) {
        return getPermissionCode(subType) != null;
    }

    /**
     * 获取所有支持的子类型
     * 
     * @return 支持的子类型集合
     */
    public java.util.Set<String> getSupportedSubTypes() {
        return subtypeMappings.keySet();
    }
}
