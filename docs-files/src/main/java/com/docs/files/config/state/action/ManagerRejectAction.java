package com.docs.files.config.state.action;

import com.docs.common.enums.ApprovalActionType;
import com.docs.common.enums.ApprovalEvent;
import com.docs.common.enums.ApprovalStatus;
import com.docs.common.jooq.generated.tables.records.DownloadApprovalRequestsRecord;

import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.statemachine.StateContext;
import org.springframework.stereotype.Component;
import static com.docs.common.jooq.generated.Tables.DOWNLOAD_APPROVAL_REQUESTS;

import java.time.LocalDateTime;

/**
 * 经理拒绝动作
 * 当状态从 PENDING_MANAGER_APPROVAL 转换到 REJECTED_BY_MANAGER 时执行
 */
@Component
@Slf4j
public class ManagerRejectAction extends BaseApprovalAction {

    public ManagerRejectAction(DSLContext dsl) {
        super(dsl);
    }

    @Override
    public void execute(StateContext<ApprovalStatus, ApprovalEvent> context) {
        Long approvalRequestId = getApprovalRequestId(context);
        Long actorId = getActorId(context);
        String comment = getComment(context);
        
        logger.info("执行经理拒绝动作，审批请求ID: {}, 操作人ID: {}", approvalRequestId, actorId);
        
        // 获取当前请求记录，确认状态
        DownloadApprovalRequestsRecord requestRecord = dsl.selectFrom(DOWNLOAD_APPROVAL_REQUESTS)
                .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
                .fetchOne();
        
        if (requestRecord == null) {
            logger.error("未找到审批请求: {}", approvalRequestId);
            throw new IllegalStateException("审批请求不存在: " + approvalRequestId);
        }
        
        // 确认当前状态是 PENDING_MANAGER_APPROVAL
        ApprovalStatus currentStatus = ApprovalStatus.valueOf(requestRecord.getStatus());
        if (currentStatus != ApprovalStatus.PENDING_MANAGER_APPROVAL) {
            logger.error("当前状态不符: {}, 期望状态: {}", 
                       currentStatus, ApprovalStatus.PENDING_MANAGER_APPROVAL);
            throw new IllegalStateException("当前状态不符: " + currentStatus);
        }
        
        // 确认操作人是指定的经理
        if (!actorId.equals(requestRecord.getManagerId())) {
            logger.error("操作人 {} 不是指定的经理 {}", actorId, requestRecord.getManagerId());
            throw new IllegalStateException("只有指定的经理可以拒绝此请求");
        }
        
        // 更新审批请求状态
        dsl.update(DOWNLOAD_APPROVAL_REQUESTS)
           .set(DOWNLOAD_APPROVAL_REQUESTS.STATUS, ApprovalStatus.REJECTED_BY_MANAGER.name())
           .set(DOWNLOAD_APPROVAL_REQUESTS.UPDATE_TIME, LocalDateTime.now())
           .where(DOWNLOAD_APPROVAL_REQUESTS.ID.eq(approvalRequestId))
           .execute();
        
        // 记录操作
        createActionRecord(
            approvalRequestId,
            actorId,
            ApprovalActionType.MANAGER_REJECT,
            comment,
            ApprovalStatus.PENDING_MANAGER_APPROVAL,
            ApprovalStatus.REJECTED_BY_MANAGER
        );
        
        logger.info("经理拒绝动作完成。请求 {} 状态已更新为 {}", 
                  approvalRequestId, ApprovalStatus.REJECTED_BY_MANAGER);
        
        // 在实际应用中，可能需要在此处添加通知逻辑，例如通知申请人审批被拒绝
        // notifyRequester(requestRecord.getRequesterId(), approvalRequestId, "经理拒绝");
    }
}
