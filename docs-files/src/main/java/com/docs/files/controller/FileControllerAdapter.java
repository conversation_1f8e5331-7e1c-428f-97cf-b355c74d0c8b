package com.docs.files.controller;

import com.docs.common.core.domain.AjaxResult;
import com.docs.files.service.processing.FileProcessingService;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.util.FileValidationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件控制器适配器 - 用于将旧的控制器请求适配到新的处理器架构
 */
@Deprecated
@Component
@RequiredArgsConstructor
@Slf4j
public class FileControllerAdapter {
    private final FileProcessingService fileProcessingService;
    
    /**
     * 处理文件上传请求
     * 
     * @param files 上传的文件数组
     * @param productModel 产品型号
     * @param fileType 文件类型
     * @param subType 子类型
     * @param drawingNo 图纸编号
     * @param version 版本号
     * @return 处理结果
     */
    public AjaxResult handleFileUpload(
            MultipartFile[] files,
            String productModel,
            String fileType,
            String subType,
            String drawingNo,
            String version) {
        
        // 验证文件
        AjaxResult validationResult = FileValidationUtils.validateFiles(files);
        if (validationResult != null) {
            return validationResult;
        }
        
        try {
            // 创建文件上传请求对象
            FileUploadRequest request = FileUploadRequest.builder()
                .files(files)
                .productModel(productModel)
                .fileType(fileType)
                .subType(subType)
                .drawingNo(drawingNo)
                .version(version)
                .build();
            
            // 使用统一的文件处理服务处理请求
            return fileProcessingService.processUpload(request);
        } catch (Exception e) {
            log.error("文件上传处理失败", e);
            return AjaxResult.error("文件上传处理失败: " + e.getMessage());
        }
    }
}
