# 研发文件权限配置文件
# 用于配置研发文件的权限控制和审批流程

rd-file:
  permissions:
    # 操作权限映射
    operation-mappings:
      upload: "files:rd:upload"
      download: "files:rd:download"
      delete: "files:rd:upload"  # 删除使用上传权限
      history: "files:rd:history"
    
    # 文件类型配置
    file-type-config:
      allowed-extensions: []  # 允许所有文件类型
      max-file-size: 100     # 最大100MB
      check-content-type: false
    
    # 审批配置
    approval-config:
      require-approval-for-download: true
      require-approval-for-replace: true
      approval-workflow-type: "file_download"
    
    # 权限验证配置
    validation:
      # 是否启用研发文件权限检查
      enabled: true
      # 是否记录权限检查日志
      log-permission-checks: true
