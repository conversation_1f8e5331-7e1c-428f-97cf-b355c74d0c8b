# 图纸权限配置文件
# 用于配置图纸子类型到权限代码的映射关系

drawing:
  permissions:
    # 图纸子类型到权限代码的映射
    subtype-mappings:
      PDF: "files:download:pdf"
      2D: "files:download:2d"
      3D: "files:download:3d"
      2D/3D: "files:download:2d3d"
      RD_FILE: "files:rd:download"
    # 默认配置
    default-config:
      # 当子类型为空或未知时是否允许下载（向后兼容）
      allow-unknown-subtype: false
      # 未知子类型的默认权限代码
      unknown-subtype-permission: "files:download:unknown"

    # 权限验证配置
    validation:
      # 是否启用图纸子类型权限检查
      enabled: true
      # 是否对子类型进行大小写敏感匹配
      case-sensitive: false
      # 是否记录权限检查日志
      log-permission-checks: true

