<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.docs.files.mapper.ProductModelsMapper">
  <resultMap id="BaseResultMap" type="com.docs.common.core.domain.entity.ProductModels">
    <!--@mbg.generated-->
    <!--@Table product_models-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="model_code" jdbcType="VARCHAR" property="modelCode" />
    <result column="status" jdbcType="OTHER" property="status" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, model_code, `status`, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from product_models
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from product_models
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.docs.common.core.domain.entity.ProductModels" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into product_models (model_code, `status`)
    values (#{modelCode,jdbcType=VARCHAR}, #{status,jdbcType=OTHER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.docs.common.core.domain.entity.ProductModels" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into product_models
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelCode != null and modelCode != ''">
        model_code,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelCode != null and modelCode != ''">
        #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=OTHER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.docs.common.core.domain.entity.ProductModels">
    <!--@mbg.generated-->
    update product_models
    <set>
      <if test="modelCode != null and modelCode != ''">
        model_code = #{modelCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=OTHER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.docs.common.core.domain.entity.ProductModels">
    <!--@mbg.generated-->
    update product_models
    set model_code = #{modelCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=OTHER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update product_models
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="model_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.modelCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=OTHER}
        </foreach>
      </trim>
      <trim prefix="created_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createdAt,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updated_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.updatedAt,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
      <!--@mbg.generated-->
      update product_models
      <trim prefix="set" suffixOverrides=",">
          <trim prefix="model_code = case" suffix="end,">
              <foreach collection="list" index="index" item="item">
                  <if test="item.modelCode != null">
                      when id = #{item.id,jdbcType=INTEGER} then #{item.modelCode,jdbcType=VARCHAR}
                  </if>
              </foreach>
          </trim>
          <trim prefix="`status` = case" suffix="end,">
              <foreach collection="list" index="index" item="item">
                  <if test="item.status != null">
                      when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=OTHER}
                  </if>
              </foreach>
          </trim>
          <trim prefix="created_at = case" suffix="end,">
              <foreach collection="list" index="index" item="item">
                  <if test="item.createdAt != null">
                      when id = #{item.id,jdbcType=INTEGER} then #{item.createdAt,jdbcType=TIMESTAMP}
                  </if>
              </foreach>
          </trim>
          <trim prefix="updated_at = case" suffix="end,">
              <foreach collection="list" index="index" item="item">
                  <if test="item.updatedAt != null">
                      when id = #{item.id,jdbcType=INTEGER} then #{item.updatedAt,jdbcType=TIMESTAMP}
                  </if>
              </foreach>
          </trim>
      </trim>
      where id in
      <foreach close=")" collection="list" item="item" open="(" separator=", ">
          #{item.id,jdbcType=INTEGER}
      </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into product_models
    (model_code, `status`, created_at, updated_at)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.modelCode,jdbcType=VARCHAR}, #{item.status,jdbcType=OTHER}, #{item.createdAt,jdbcType=TIMESTAMP}, 
        #{item.updatedAt,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <!-- 查询机型列表 -->
  <select id="selectProductModelsList" parameterType="ProductModels" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List"/>
    from product_models
    <where>
        <if test="modelCode != null and modelCode != ''">
            AND model_code like concat('%', #{modelCode}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </where>
    order by created_at desc
  </select>
  <!-- 检查机型是否存在 -->
  <select id="checkModelExists" parameterType="String" resultType="Boolean">
    select count(1) > 0 
    from product_models 
    where model_code = #{modelCode}
  </select>
  <select id="selectByModelCode" parameterType="String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List"/>
    from product_models
    where model_code = #{modelCode}
  </select>
</mapper>