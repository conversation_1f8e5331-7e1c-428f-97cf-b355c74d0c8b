<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.docs.files.mapper.MaterialDrawingRelationsMapper">
    <resultMap id="BaseResultMap" type="com.docs.common.core.domain.entity.MaterialDrawingRelations">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="material_code" property="materialCode" jdbcType="VARCHAR"/>
        <result column="material_name" property="materialName" jdbcType="VARCHAR"/>
        <result column="specification" property="specification" jdbcType="VARCHAR"/>
        <result column="drawing_no" property="drawingNo" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, material_code, material_name, specification, drawing_no, created_at, updated_at
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from material_drawing_relations
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByMaterialCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from material_drawing_relations
        where material_code = #{materialCode,jdbcType=VARCHAR}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from material_drawing_relations
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from material_drawing_relations
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.docs.common.core.domain.entity.MaterialDrawingRelations">
        insert into material_drawing_relations (
            material_code, material_name, specification, drawing_no, created_at, updated_at
        )
        values (
            #{materialCode,jdbcType=VARCHAR},
            #{materialName,jdbcType=VARCHAR},
            #{specification,jdbcType=VARCHAR},
            #{drawingNo,jdbcType=VARCHAR},
            #{createdAt,jdbcType=TIMESTAMP},
            #{updatedAt,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.docs.common.core.domain.entity.MaterialDrawingRelations">
        insert into material_drawing_relations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialCode != null">
                material_code,
            </if>
            <if test="materialName != null">
                material_name,
            </if>
            <if test="specification != null">
                specification,
            </if>
            <if test="drawingNo != null">
                drawing_no,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="updatedAt != null">
                updated_at,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialCode != null">
                #{materialCode,jdbcType=VARCHAR},
            </if>
            <if test="materialName != null">
                #{materialName,jdbcType=VARCHAR},
            </if>
            <if test="specification != null">
                #{specification,jdbcType=VARCHAR},
            </if>
            <if test="drawingNo != null">
                #{drawingNo,jdbcType=VARCHAR},
            </if>
            <if test="createdAt != null">
                #{createdAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedAt != null">
                #{updatedAt,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.docs.common.core.domain.entity.MaterialDrawingRelations">
        update material_drawing_relations
        <set>
            <if test="materialCode != null">
                material_code = #{materialCode,jdbcType=VARCHAR},
            </if>
            <if test="materialName != null">
                material_name = #{materialName,jdbcType=VARCHAR},
            </if>
            <if test="specification != null">
                specification = #{specification,jdbcType=VARCHAR},
            </if>
            <if test="drawingNo != null">
                drawing_no = #{drawingNo,jdbcType=VARCHAR},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.docs.common.core.domain.entity.MaterialDrawingRelations">
        update material_drawing_relations
        set material_code = #{materialCode,jdbcType=VARCHAR},
            material_name = #{materialName,jdbcType=VARCHAR},
            specification = #{specification,jdbcType=VARCHAR},
            drawing_no = #{drawingNo,jdbcType=VARCHAR},
            updated_at = #{updatedAt,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into material_drawing_relations (
            material_code, material_name, specification, drawing_no, created_at, updated_at
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.materialCode,jdbcType=VARCHAR},
                #{item.materialName,jdbcType=VARCHAR},
                #{item.specification,jdbcType=VARCHAR},
                #{item.drawingNo,jdbcType=VARCHAR},
                #{item.createdAt,jdbcType=TIMESTAMP},
                #{item.updatedAt,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update material_drawing_relations
            set material_name = #{item.materialName,jdbcType=VARCHAR},
                specification = #{item.specification,jdbcType=VARCHAR},
                drawing_no = #{item.drawingNo,jdbcType=VARCHAR},
                updated_at = #{item.updatedAt,jdbcType=TIMESTAMP}
            where material_code = #{item.materialCode,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>