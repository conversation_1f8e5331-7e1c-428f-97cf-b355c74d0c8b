<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.docs.filesEntity.mapper.BomItemMapper">
  <resultMap id="BaseResultMap" type="com.docs.common.core.domain.entity.BomItem">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="file_id" jdbcType="INTEGER" property="fileId" />
    <result column="item_no" jdbcType="VARCHAR" property="itemNo" />
    <result column="depth" jdbcType="INTEGER" property="depth" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="supplier" jdbcType="VARCHAR" property="supplier" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="production_property" jdbcType="VARCHAR" property="productionProperty" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="bom_type" jdbcType="VARCHAR" property="bomType" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, file_id, item_no, depth, material_code, material_name, specification, supplier, 
    unit, quantity, production_property, category, remark, bom_type, created_at, updated_at
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bom_items
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from bom_items
    where id = #{id,jdbcType=INTEGER}
  </delete>
  
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.docs.common.core.domain.entity.BomItem" useGeneratedKeys="true">
    insert into bom_items (file_id, item_no, depth, 
      material_code, material_name, specification, 
      supplier, unit, quantity, 
      production_property, category, remark, 
      bom_type)
    values (#{fileId,jdbcType=INTEGER}, #{itemNo,jdbcType=VARCHAR}, #{depth,jdbcType=INTEGER}, 
      #{materialCode,jdbcType=VARCHAR}, #{materialName,jdbcType=VARCHAR}, #{specification,jdbcType=VARCHAR}, 
      #{supplier,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{quantity,jdbcType=DECIMAL}, 
      #{productionProperty,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{bomType,jdbcType=VARCHAR})
  </insert>
  
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.docs.common.core.domain.entity.BomItem" useGeneratedKeys="true">
    insert into bom_items
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileId != null">
        file_id,
      </if>
      <if test="itemNo != null">
        item_no,
      </if>
      <if test="depth != null">
        depth,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="materialName != null">
        material_name,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="supplier != null">
        supplier,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="productionProperty != null">
        production_property,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="bomType != null">
        bom_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileId != null">
        #{fileId,jdbcType=INTEGER},
      </if>
      <if test="itemNo != null">
        #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="depth != null">
        #{depth,jdbcType=INTEGER},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="supplier != null">
        #{supplier,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="productionProperty != null">
        #{productionProperty,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="bomType != null">
        #{bomType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.docs.common.core.domain.entity.BomItem">
    update bom_items
    <set>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=INTEGER},
      </if>
      <if test="itemNo != null">
        item_no = #{itemNo,jdbcType=VARCHAR},
      </if>
      <if test="depth != null">
        depth = #{depth,jdbcType=INTEGER},
      </if>
      <if test="materialCode != null">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        material_name = #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="supplier != null">
        supplier = #{supplier,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="productionProperty != null">
        production_property = #{productionProperty,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="bomType != null">
        bom_type = #{bomType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.docs.common.core.domain.entity.BomItem">
    update bom_items
    set file_id = #{fileId,jdbcType=INTEGER},
      item_no = #{itemNo,jdbcType=VARCHAR},
      depth = #{depth,jdbcType=INTEGER},
      material_code = #{materialCode,jdbcType=VARCHAR},
      material_name = #{materialName,jdbcType=VARCHAR},
      specification = #{specification,jdbcType=VARCHAR},
      supplier = #{supplier,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=DECIMAL},
      production_property = #{productionProperty,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      bom_type = #{bomType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <delete id="deleteByFileId" parameterType="java.lang.Integer">
    delete from bom_items
    where file_id = #{fileId,jdbcType=INTEGER}
  </delete>
  
  <select id="selectByFileId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bom_items
    where file_id = #{fileId,jdbcType=INTEGER}
    order by id asc
  </select>
  
  <select id="selectBomItemList" parameterType="com.docs.common.core.domain.entity.BomItem" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bom_items
    <where>
      <if test="fileId != null">
        and file_id = #{fileId}
      </if>
      <if test="bomType != null and bomType != ''">
        and bom_type = #{bomType}
      </if>
      <if test="materialCode != null and materialCode != ''">
        and material_code like concat('%', #{materialCode}, '%')
      </if>
      <if test="materialName != null and materialName != ''">
        and material_name like concat('%', #{materialName}, '%')
      </if>
      <if test="category != null and category != ''">
        and category = #{category}
      </if>
    </where>
    order by id asc
  </select>
  
  <update id="updateBatch" parameterType="java.util.List">
    update bom_items
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="file_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.fileId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="item_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.itemNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="depth = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.depth,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="material_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.materialCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="material_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.materialName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="specification = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.specification,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="supplier = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.supplier,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="unit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.unit,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="quantity = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.quantity,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="production_property = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.productionProperty,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="category = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.category,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="bom_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.bomType,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into bom_items (file_id, item_no, depth, 
      material_code, material_name, specification, 
      supplier, unit, quantity, 
      production_property, category, remark, 
      bom_type)
    values 
    <foreach collection="list" item="item" separator=",">
      (#{item.fileId,jdbcType=INTEGER}, #{item.itemNo,jdbcType=VARCHAR}, #{item.depth,jdbcType=INTEGER}, 
      #{item.materialCode,jdbcType=VARCHAR}, #{item.materialName,jdbcType=VARCHAR}, #{item.specification,jdbcType=VARCHAR}, 
      #{item.supplier,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.quantity,jdbcType=DECIMAL}, 
      #{item.productionProperty,jdbcType=VARCHAR}, #{item.category,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
      #{item.bomType,jdbcType=VARCHAR})
    </foreach>
  </insert>
  
  <update id="updateBatchSelective" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
      update bom_items
      <set>
        <if test="item.fileId != null">
          file_id = #{item.fileId,jdbcType=INTEGER},
        </if>
        <if test="item.itemNo != null">
          item_no = #{item.itemNo,jdbcType=VARCHAR},
        </if>
        <if test="item.depth != null">
          depth = #{item.depth,jdbcType=INTEGER},
        </if>
        <if test="item.materialCode != null">
          material_code = #{item.materialCode,jdbcType=VARCHAR},
        </if>
        <if test="item.materialName != null">
          material_name = #{item.materialName,jdbcType=VARCHAR},
        </if>
        <if test="item.specification != null">
          specification = #{item.specification,jdbcType=VARCHAR},
        </if>
        <if test="item.supplier != null">
          supplier = #{item.supplier,jdbcType=VARCHAR},
        </if>
        <if test="item.unit != null">
          unit = #{item.unit,jdbcType=VARCHAR},
        </if>
        <if test="item.quantity != null">
          quantity = #{item.quantity,jdbcType=DECIMAL},
        </if>
        <if test="item.productionProperty != null">
          production_property = #{item.productionProperty,jdbcType=VARCHAR},
        </if>
        <if test="item.category != null">
          category = #{item.category,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null">
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.bomType != null">
          bom_type = #{item.bomType,jdbcType=VARCHAR},
        </if>
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper> 