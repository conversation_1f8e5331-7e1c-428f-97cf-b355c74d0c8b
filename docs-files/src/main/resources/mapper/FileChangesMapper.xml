<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.docs.filesEntity.mapper.FileChangesMapper">
  <resultMap id="BaseResultMap" type="com.docs.common.core.domain.entity.FileChanges">
    <!--@mbg.generated-->
    <!--@Table file_changes-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="file_id" jdbcType="INTEGER" property="fileId" />
    <result column="old_version" jdbcType="VARCHAR" property="oldVersion" />
    <result column="new_version" jdbcType="VARCHAR" property="newVersion" />
    <result column="change_reason" jdbcType="VARCHAR" property="changeReason" />
    <result column="changed_by" jdbcType="INTEGER" property="changedBy" />
    <result column="changed_at" jdbcType="TIMESTAMP" property="changedAt" />
    <result column="oper_name" jdbcType="VARCHAR" property="operName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, file_id, old_version, new_version, change_reason, changed_by, changed_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from file_changes
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from file_changes
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.docs.common.core.domain.entity.FileChanges" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into file_changes (file_id, old_version, new_version, 
      change_reason, changed_by, changed_at
      )
    values (#{fileId,jdbcType=INTEGER}, #{oldVersion,jdbcType=VARCHAR}, #{newVersion,jdbcType=VARCHAR}, 
      #{changeReason,jdbcType=VARCHAR}, #{changedBy,jdbcType=INTEGER}, #{changedAt,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.docs.common.core.domain.entity.FileChanges" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into file_changes
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileId != null">
        file_id,
      </if>
      <if test="oldVersion != null and oldVersion != ''">
        old_version,
      </if>
      <if test="newVersion != null and newVersion != ''">
        new_version,
      </if>
      <if test="changeReason != null and changeReason != ''">
        change_reason,
      </if>
      <if test="changedBy != null">
        changed_by,
      </if>
      <if test="changedAt != null">
        changed_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileId != null">
        #{fileId,jdbcType=INTEGER},
      </if>
      <if test="oldVersion != null and oldVersion != ''">
        #{oldVersion,jdbcType=VARCHAR},
      </if>
      <if test="newVersion != null and newVersion != ''">
        #{newVersion,jdbcType=VARCHAR},
      </if>
      <if test="changeReason != null and changeReason != ''">
        #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="changedBy != null">
        #{changedBy,jdbcType=INTEGER},
      </if>
      <if test="changedAt != null">
        #{changedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.docs.common.core.domain.entity.FileChanges">
    <!--@mbg.generated-->
    update file_changes
    <set>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=INTEGER},
      </if>
      <if test="oldVersion != null and oldVersion != ''">
        old_version = #{oldVersion,jdbcType=VARCHAR},
      </if>
      <if test="newVersion != null and newVersion != ''">
        new_version = #{newVersion,jdbcType=VARCHAR},
      </if>
      <if test="changeReason != null and changeReason != ''">
        change_reason = #{changeReason,jdbcType=VARCHAR},
      </if>
      <if test="changedBy != null">
        changed_by = #{changedBy,jdbcType=INTEGER},
      </if>
      <if test="changedAt != null">
        changed_at = #{changedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.docs.common.core.domain.entity.FileChanges">
    <!--@mbg.generated-->
    update file_changes
    set file_id = #{fileId,jdbcType=INTEGER},
      old_version = #{oldVersion,jdbcType=VARCHAR},
      new_version = #{newVersion,jdbcType=VARCHAR},
      change_reason = #{changeReason,jdbcType=VARCHAR},
      changed_by = #{changedBy,jdbcType=INTEGER},
      changed_at = #{changedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update file_changes
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="file_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.fileId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="old_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.oldVersion,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="new_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.newVersion,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="change_reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.changeReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="changed_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.changedBy,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="changed_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.changedAt,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update file_changes
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="file_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fileId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.fileId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="old_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.oldVersion != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.oldVersion,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="new_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.newVersion != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.newVersion,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="change_reason = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.changeReason != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.changeReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="changed_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.changedBy != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.changedBy,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="changed_at = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.changedAt != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.changedAt,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into file_changes
    (file_id, old_version, new_version, change_reason, changed_by, changed_at)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fileId,jdbcType=INTEGER}, #{item.oldVersion,jdbcType=VARCHAR}, #{item.newVersion,jdbcType=VARCHAR}, 
        #{item.changeReason,jdbcType=VARCHAR}, #{item.changedBy,jdbcType=INTEGER}, #{item.changedAt,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <select id="selectFileChangesList" parameterType="FileChanges" resultMap="BaseResultMap">
      SELECT fc.*,
             su.user_name as oper_name
      FROM file_changes fc
               LEFT JOIN sys_user su ON fc.changed_by = su.user_id
               LEFT JOIN filesEntity f ON fc.file_id = f.id
      <where>
          <if test="fileId != null">
              AND fc.file_id = #{fileId}
          </if>
          <if test="productModelId != null">
              AND f.product_model_id = #{productModelId}
          </if>
          <if test="fileType != null and fileType != ''">
              AND f.file_type = #{fileType}
          </if>
          <if test="subType != null and subType != ''">
              AND (
                  CASE
                      WHEN f.file_type = 'BOM' THEN f.sub_type = #{subType}
                      WHEN f.file_type = '图纸' THEN f.drawing_no = #{subType}
                      ELSE 1 = 1
                      END
                  )
          </if>
      </where>
      ORDER BY fc.changed_at DESC
  </select>
</mapper>