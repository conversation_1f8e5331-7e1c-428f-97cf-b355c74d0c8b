name: CI/CD Pipeline

on:
  # push:
  #   branches: [main, master]
  workflow_dispatch:
    inputs:
      reset_mysql:
        description: '重置MySQL数据（清空数据库）'
        required: false
        default: false
        type: boolean

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: 123456
          MYSQL_DATABASE: file_manage
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping -h localhost -u root -p123456"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=5

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up JDK 8
      uses: actions/setup-java@v3
      with:
        java-version: '8'
        distribution: 'temurin'

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 16

    - name: Initialize database for jOOQ
      run: |
        while ! mysqladmin ping -h 127.0.0.1 -P 3306 -u root -p123456 --silent; do
          sleep 2
        done
        mysql -h 127.0.0.1 -P 3306 -u root -p123456 file_manage < sql/init.sql

    - name: Build Frontend
      run: |
        echo "Building frontend..."
        cd docs-ui
        npm install
        npm run build:prod
        cd ..
        echo "Frontend build complete."

    - name: Maven build
      run: |
        # 使用JDK 8进行构建
        java -version
        echo "Running Maven build..."
        mvn clean package -DskipTests
      env:
        SPRING_DATASOURCE_DRUID_MASTER_URL: *****************************************************************************************************************************************************
        SPRING_DATASOURCE_DRUID_MASTER_USERNAME: root
        SPRING_DATASOURCE_DRUID_MASTER_PASSWORD: 123456

    - name: Copy deployment files to server
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        source: "docker-compose.yml,deploy.sh,scripts/deploy-hook.sh,Dockerfile,docker-entrypoint.sh,sql/,docs-admin/target/*.jar,docs-ui/dist/,docs-ui/nginx.conf" # 确保 sql 目录和其内容被包含
        target: "/data/docs-manage"
        # strip_components: 0 # 保持文件在 target 目录下的原始相对路径
        overwrite: true
        # 如果源路径中有目录，scp-action 默认会递归复制。
        # 例如 'sql/' 会复制整个 sql 目录。
        # 'docs-admin/target/*.jar' 会复制 target 目录下的所有 jar 文件到 /data/docs-manage/docs-admin/target/ (如果strip_components未改变层级)
        # 或者直接到 /data/docs-manage/ 如果 strip_components 调整了。
        # 为了保持结构，通常不使用或小心使用 strip_components，并确保 source 路径正确。
        # 如果 source 是 "target/app.jar, other_dir/"
        # target 是 "/deploy"
        # 结果是 /deploy/app.jar 和 /deploy/other_dir/*
        # 如果您的文件都在项目根目录，并且希望它们在服务器的 /data/docs-manage 下保持相同的相对结构，
        # 那么 scp 的 source 应该是这些文件和目录的列表，target 是 /data/docs-manage。
        # appleboy/scp-action 会尝试保持这种结构。

    - name: Deploy to server
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USERNAME }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        command_timeout: "15m"
        script: |
          # 进入部署目录
          cd /data/docs-manage
          echo "当前目录: $(pwd)"

          # 停止运行中的容器
          echo "停止现有容器..."
          docker compose down || true
          echo "容器已停止"

          # 确保目录结构存在
          echo "确保目录结构存在..."
          mkdir -p /data/docs-manage/sql
          mkdir -p /data/docs-manage/images
          mkdir -p /data/docs-manage/logs
          mkdir -p /data/docs-manage/scripts
          mkdir -p /data/docs-manage/docs-ui/dist
          mkdir -p /data/docs-manage/docs-admin/target
          mkdir -p /data/docs-manage/nginx/ssl

          # 检查文件是否存在
          echo "验证文件..."
          ls -la

          # 如果存在deploy-hook.sh，也设置执行权限
          if [ -f "scripts/deploy-hook.sh" ]; then
            chmod +x scripts/deploy-hook.sh
            echo "部署钩子脚本就绪"
          fi

          # 设置执行权限
          chmod +x docker-entrypoint.sh || echo "警告: 未找到docker-entrypoint.sh"

          # 在服务器上构建镜像
          echo "开始在服务器构建Docker镜像..."
          # 确保清理旧镜像(如果存在)以防止冲突
          docker rmi docs-manage:latest || true

          # 构建新镜像
          docker build -t docs-manage:latest .
          if [ $? -ne 0 ]; then
            echo "镜像构建失败，检查问题..."
            exit 1
          fi

          # 执行部署脚本
          # 显式传递重置标志，确保没有格式问题
          if [ "${{ github.event.inputs.reset_mysql }}" = "true" ]; then
            echo "User selected to reset MySQL, passing 'true' to script"
            # 直接执行部署脚本，让脚本处理MySQL重置
            bash scripts/deploy-hook.sh "true" local
          else
            echo "No MySQL reset requested, passing 'false' to script"
            bash scripts/deploy-hook.sh "false" local
          fi