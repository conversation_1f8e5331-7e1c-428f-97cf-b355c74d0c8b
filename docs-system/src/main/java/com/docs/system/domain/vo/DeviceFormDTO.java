package com.docs.system.domain.vo;

import com.docs.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

@Data
public class DeviceFormDTO implements Serializable{

    private static final long serialVersionUID = -2193017187656232497L;

    @Excel(name = "货架ID")
    private Long shelfId;

    @Excel(name = "货架编号")
    private String shelfNumber;

    @Excel(name = "文件夹编号")
    private String folderNumber;

    @Excel(name = "机种名称")
    private String modelName;

    @Excel(name = "指令单号")
    private String orderNumber;

    @Excel(name = "部门")
    private String department;

    @Excel(name = "年份")
    private String year;

    @Excel(name = "表单名称")
    private String formName;

    @Excel(name = "产品序列号")
    private String productSerialNumber;

    @Excel(name = "借出状态")
    private Integer loanStatus;
}

