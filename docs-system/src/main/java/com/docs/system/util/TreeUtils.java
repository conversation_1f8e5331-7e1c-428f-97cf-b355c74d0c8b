package com.docs.system.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;

/**
 * 树操作工具类。
 * 提供构建和遍历树结构的通用方法。
 */
public class TreeUtils {
    
    private static final Logger log = LoggerFactory.getLogger(TreeUtils.class);
    
    /**
     * 使用BFS遍历查找指定节点的所有祖先。
     * 
     * @param <T> 节点ID的类型
     * @param startNodeIds 要查找祖先的节点ID集合
     * @param childToParentMap 子节点ID到父节点ID的关系映射
     * @return 祖先节点ID的集合
     */
    public static <T> Set<T> findAncestors(Set<T> startNodeIds, Map<T, T> childToParentMap) {
        Set<T> ancestors = new HashSet<>();
        Queue<T> queue = new ArrayDeque<>(startNodeIds);
        
        while (!queue.isEmpty()) {
            T currentId = queue.poll();
            T parentId = childToParentMap.get(currentId);
            
            if (parentId != null && !ancestors.contains(parentId) && !startNodeIds.contains(parentId)) {
                ancestors.add(parentId);
                queue.add(parentId); // 将父节点添加到队列以查找其祖先
            }
        }
        
        return ancestors;
    }
    
    /**
     * 使用BFS遍历查找指定节点的所有后代。
     * 
     * @param <T> 节点ID的类型
     * @param startNodeIds 要查找后代的节点ID集合
     * @param parentToChildrenMap 父节点ID到子节点ID列表的关系映射
     * @return 后代节点ID的集合
     */
    public static <T> Set<T> findDescendants(Set<T> startNodeIds, Map<T, List<T>> parentToChildrenMap) {
        Set<T> descendants = new HashSet<>();
        Queue<T> queue = new ArrayDeque<>(startNodeIds);
        
        while (!queue.isEmpty()) {
            T currentId = queue.poll();
            List<T> childrenIds = parentToChildrenMap.getOrDefault(currentId, Collections.emptyList());
            
            for (T childId : childrenIds) {
                if (!descendants.contains(childId) && !startNodeIds.contains(childId)) {
                    descendants.add(childId);
                    queue.add(childId); // 将子节点添加到队列以查找其后代
                }
            }
        }
        
        return descendants;
    }
    
    /**
     * 基于深度值构建父子关系的通用方法。
     * 用于将带有深度指示器的扁平列表转换为树结构。
     * 
     * @param <T> 列表中项的类型
     * @param <ID> 项ID的类型
     * @param items 要处理的项列表
     * @param idExtractor 从项中提取ID的函数
     * @param depthExtractor 从项中提取深度的函数
     * @return 包含父子映射的TreeRelationships对象
     */
    public static <T, ID> TreeRelationships<ID, T> buildTreeRelationships(
            List<T> items, 
            Function<T, ID> idExtractor,
            Function<T, Integer> depthExtractor) {
        
        if (items == null || items.isEmpty()) {
            return new TreeRelationships<>(
                new HashMap<>(), 
                new HashMap<>(), 
                new HashMap<>()
            );
        }
        
        Map<ID, T> idToItemMap = new HashMap<>();
        Map<ID, ID> childToParentMap = new HashMap<>();
        Map<ID, List<ID>> parentToChildrenMap = new HashMap<>();
        Map<Integer, T> depthStack = new HashMap<>();
        
        for (T item : items) {
            ID itemId = idExtractor.apply(item);
            idToItemMap.put(itemId, item);
            parentToChildrenMap.computeIfAbsent(itemId, k -> new ArrayList<>());
            
            final Integer itemDepth = depthExtractor.apply(item);
            final int depth = itemDepth != null ? itemDepth : 0;
            
            // 基于深度查找父节点
            if (depth > 0) {
                T parentItem = depthStack.get(depth - 1);
                if (parentItem != null) {
                    ID parentId = idExtractor.apply(parentItem);
                    childToParentMap.put(itemId, parentId);
                    parentToChildrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(itemId);
                } else {
                    log.warn("树构建: 无法为项ID {} 在深度 {} 找到父项, 假定其为根节点。", itemId, depth);
                }
            }
            
            // 更新深度栈
            depthStack.put(depth, item);
            // 清理比当前层级更深的栈
            depthStack.keySet().removeIf(key -> key > depth);
        }
        
        return new TreeRelationships<>(idToItemMap, childToParentMap, parentToChildrenMap);
    }

    /**
     * 通用方法，用于从扁平列表构建层级树结构。
     *
     * @param <T>  扁平列表中项的类型
     * @param <ID> 项ID的类型
     * @param <VO> 树节点视图对象的类型
     * @param items 要处理的项列表
     * @param includedIds 要包含在树中的节点ID集合
     * @param idExtractor 从项中提取ID的函数
     * @param depthExtractor 从项中提取深度的函数
     * @param itemToVoMapper 将原始项T映射到其对应树节点VO的函数
     * @param getChildrenFunc 从视图对象VO获取子列表的函数
     * @param setChildrenFunc 设置视图对象VO子列表的函数
     * @return 根节点的列表 (List<VO>)
     */
    public static <T, ID, VO> List<VO> buildTree(
            List<T> items,
            Set<ID> includedIds,
            Function<T, ID> idExtractor,
            Function<T, Integer> depthExtractor,
            Function<T, VO> itemToVoMapper,
            Function<VO, List<VO>> getChildrenFunc,
            java.util.function.BiConsumer<VO, List<VO>> setChildrenFunc) {

        if (items == null || items.isEmpty() || includedIds == null || includedIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<VO> roots = new ArrayList<>();
        Map<Integer, VO> parentStack = new HashMap<>();

        for (T item : items) {
            ID itemId = idExtractor.apply(item);

            // 跳过未标记为包含的项目
            if (!includedIds.contains(itemId)) {
                continue;
            }

            VO node = itemToVoMapper.apply(item);
            setChildrenFunc.accept(node, new ArrayList<>()); // 初始化子列表

            final Integer itemDepth = depthExtractor.apply(item);
            final int depth = itemDepth != null ? itemDepth : 0;

            if (depth == 0) {
                roots.add(node);
            } else {
                VO parent = parentStack.get(depth - 1);
                if (parent != null) {
                    List<VO> children = getChildrenFunc.apply(parent);
                    if (children != null) { // 确保父节点的子列表不为null
                        children.add(node);
                    } else {
                         log.warn("树构建: 父节点ID {} 的子列表为null。", idExtractor.apply(item)); 
                         // 可以选择创建子列表或记录更详细的错误
                         List<VO> newChildren = new ArrayList<>();
                         newChildren.add(node);
                         setChildrenFunc.accept(parent, newChildren);
                    }
                } else {
                    log.warn("树构建: 无法为项ID {} (深度 {}) 在深度 {} 找到父项, 添加为根节点。",
                             itemId, depth, depth - 1);
                    roots.add(node);
                }
            }

            parentStack.put(depth, node);
            // 清理比当前层级更深的栈
            parentStack.keySet().removeIf(key -> key > depth);
        }

        return roots;
    }

    /**
     * 用于保存树关系映射的通用类。
     * 
     * @param <ID> 项ID的类型
     * @param <T> 项的类型
     */
    public static class TreeRelationships<ID, T> {
        private final Map<ID, T> idToItemMap;
        private final Map<ID, ID> childToParentMap;
        private final Map<ID, List<ID>> parentToChildrenMap;
        
        public TreeRelationships(
            Map<ID, T> idToItemMap,
            Map<ID, ID> childToParentMap,
            Map<ID, List<ID>> parentToChildrenMap
        ) {
            this.idToItemMap = idToItemMap;
            this.childToParentMap = childToParentMap;
            this.parentToChildrenMap = parentToChildrenMap;
        }
        
        public Map<ID, T> getIdToItemMap() {
            return this.idToItemMap;
        }
        
        public Map<ID, ID> getChildToParentMap() {
            return this.childToParentMap;
        }
        
        public Map<ID, List<ID>> getParentToChildrenMap() {
            return this.parentToChildrenMap;
        }
    }
}
