package com.docs.framework.config;

import java.nio.charset.Charset;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

/**
 * Redis使用FastJson序列化
 * 
 * <AUTHOR>
 */
public class FastJson2JsonRedisSerializer<T> implements RedisSerializer<T>
{
    public static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");

    private final ObjectMapper objectMapper;
    private Class<T> clazz;

    public FastJson2JsonRedisSerializer(Class<T> clazz) {
        super();
        this.clazz = clazz;
        this.objectMapper = new ObjectMapper(); // 默认的 ObjectMapper，或通过构造注入自定义 ObjectMapper
    }

    public FastJson2JsonRedisSerializer(Class<T> clazz, ObjectMapper objectMapper) {
        super();
        this.clazz = clazz;
        this.objectMapper = objectMapper != null ? objectMapper : new ObjectMapper();
    }

    @Override
    public byte[] serialize(T t) throws SerializationException {
        if (t == null) {
            return new byte[0];
        }
        try {
            return objectMapper.writeValueAsString(t).getBytes(DEFAULT_CHARSET);
        } catch (JsonProcessingException e) {
            throw new SerializationException("Could not serialize object", e);
        }
    }

    @Override
    public T deserialize(byte[] bytes) throws SerializationException {
        if (bytes == null || bytes.length <= 0) {
            return null;
        }
        String str = new String(bytes, DEFAULT_CHARSET);
        try {
            return objectMapper.readValue(str, clazz);
        } catch (Exception e) {
            throw new SerializationException("Could not deserialize object", e);
        }
    }
}
