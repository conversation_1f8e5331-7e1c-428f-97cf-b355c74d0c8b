# 新部署流程说明

## 概述

本项目采用了基于GitHub Actions的一体化CI/CD流程，实现了自动构建、部署和更新。通过多阶段Docker构建，将前后端打包为单一容器镜像，简化了部署和维护流程。

## 架构说明

整个部署流程包含以下组件：

1. **多阶段Dockerfile** - 在一个构建过程中完成前后端编译和打包
2. **GitHub Actions工作流** - 自动构建和推送Docker镜像
3. **服务器端部署钩子** - 自动更新容器和初始化数据库
4. **Docker Compose配置** - 统一管理所有容器服务

## 服务器环境配置

请确保服务器已安装以下软件：
- Docker (20.10+)
- Docker Compose (2.0+)

在服务器上需要执行以下准备工作：

1. 创建部署目录：
```bash
mkdir -p /data/docs-manage
mkdir -p /data/docs-manage/sql
mkdir -p /data/docs-manage/images
mkdir -p /data/docs-manage/logs
```

2. 将部署钩子脚本复制到服务器：
```bash
# 在项目根目录执行
scp scripts/deploy-hook.sh user@your-server:/data/docs-manage/
chmod +x /data/docs-manage/deploy-hook.sh
```

3. 将docker-compose.yml文件复制到服务器：
```bash
scp docker-compose.yml user@your-server:/data/docs-manage/
```

4. 配置环境变量（可以添加到~/.bashrc）：
```bash
echo 'export DOCKER_USERNAME="你的Docker用户名"' >> ~/.bashrc
source ~/.bashrc
```

## GitHub Secrets配置

在GitHub仓库设置中，添加以下Secrets：

- `DOCKER_USERNAME` - Docker Hub用户名
- `DOCKER_PASSWORD` - Docker Hub密码
- `SERVER_HOST` - 服务器IP或域名
- `SERVER_USERNAME` - SSH用户名
- `SERVER_SSH_KEY` - SSH私钥内容

## 构建说明

### 数据库设置
为了在本地成功构建项目，特别是进行jOOQ代码生成，您需要确保满足以下数据库要求：

1. MySQL 8.0 运行在 localhost:3306
2. 创建一个名为 `file_manage` 的数据库
3. 使用以下凭据:
   - 用户名: root
   - 密码: 123456
4. 确保使用 `sql/init.sql` 脚本初始化数据库结构

如果您遇到以下jOOQ代码生成错误:
```
Error: Cannot execute query. No Connection configured
```
请确认您已正确设置并初始化了数据库。

### 部署流程
本项目采用了基于GitHub Actions的简化CI/CD流程，实现了自动构建、部署和更新。通过多阶段Docker构建和直接服务器构建，避免了Docker Hub中转，提高了部署安全性和效率。

详细部署说明请参阅 [DEPLOYMENT.md](DEPLOYMENT.md)。

主要优势：
- **无需Docker Hub** - 直接在服务器上构建镜像
- **简化认证** - 减少认证需求和安全风险
- **一体化构建** - 前后端在同一个流程中处理
- **更快的部署** - 避免了镜像上传下载的时间

部署方式包括：
- 自动部署：推送代码到主分支时自动触发
- 手动部署：通过GitHub Actions界面手动触发
- 数据库重置：可选择是否重置MySQL数据（通过删除数据卷实现完全重置）

## 故障排查

### 检查部署状态
```bash
# 查看容器状态
docker ps

# 查看应用日志
docker logs docs-manage

# 检查镜像构建
docker images | grep docs-manage
```

### 常见问题

1. **数据库重置失败**
   - 确认MySQL容器已正确停止：`docker compose stop mysql`
   - 查找并删除MySQL数据卷：
   ```bash
   # 查找MySQL数据卷
   MYSQL_VOLUME=$(docker volume ls | grep mysql_data | awk '{print $2}')
   # 删除数据卷
   docker volume rm $MYSQL_VOLUME
   ```
   - 重新启动MySQL容器：`docker compose up -d mysql`
   - 等待MySQL容器完全启动（至少30秒）
   - 检查MySQL容器日志：`docker logs docs-mysql`