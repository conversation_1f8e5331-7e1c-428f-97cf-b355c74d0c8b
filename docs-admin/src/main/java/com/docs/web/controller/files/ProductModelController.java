package com.docs.web.controller.files;

import com.docs.common.core.controller.BaseController;
import com.docs.common.core.domain.AjaxResult;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import com.docs.files.service.ProductModelsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品型号管理 Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/files/model")
@RequiredArgsConstructor
@Slf4j
public class ProductModelController extends BaseController {

    private final ProductModelsService productModelsService;

    /**
     * 获取机型下拉列表
     */
    @GetMapping("/list")
    public AjaxResult getModelList() {
        List<ProductModels> list = productModelsService.selectProductModelsList(new ProductModels());
        return AjaxResult.success(list);
    }

    /**
     * 新增机型
     */
    @PreAuthorize("@ss.hasPermi('files:upload')")
    @PostMapping
    public AjaxResult addModel(@RequestBody @Validated ProductModels productModel) {
        if (productModelsService.checkModelExists(productModel.getModelCode())) {
            return AjaxResult.error("机型已存在");
        }
        return toAjax(productModelsService.insertProductModel(productModel));
    }
}
