package com.docs.web.controller.files;

import com.docs.common.core.controller.BaseController;
import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.entity.ApprovalRequestVO;
import com.docs.common.core.domain.entity.FilesEntity;
import com.docs.common.core.domain.vo.FileListVO;
import com.docs.common.core.page.PageDomain;
import com.docs.common.core.page.TableDataInfo;
import com.docs.common.core.page.TableSupport;
import com.docs.common.utils.SecurityUtils;
import com.docs.files.service.ApprovalService;
import com.docs.files.service.FilesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件下载审批 Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/files/approval")
@Slf4j
@RequiredArgsConstructor
public class FileDownloadApprovalController extends BaseController {

    private final ApprovalService approvalService;
    private final FilesService filesService;

    /**
     * 获取文件下载申请信息
     */
    @PreAuthorize("@ss.hasPermi('files:download:apply')")
    @GetMapping("/apply")
    public AjaxResult getApplyInfo(@RequestParam("fileId") Long fileId) {
        try {
            // 获取文件信息
            FilesEntity filesEntity = new FilesEntity();
            filesEntity.setId(fileId.intValue());
            List<FileListVO> fileList = filesService.selectFilesList(filesEntity);

            if (fileList == null || fileList.isEmpty()) {
                return AjaxResult.error("文件不存在");
            }

            FileListVO file = fileList.get(0);

            // 返回文件信息
            return AjaxResult.success(file);
        } catch (Exception e) {
            log.error("获取申请信息失败", e);
            return AjaxResult.error("获取申请信息失败: " + e.getMessage());
        }
    }

    /**
     * 提交文件下载申请
     */
    @PreAuthorize("@ss.hasPermi('files:download:apply')")
    @PostMapping("/submit")
    public AjaxResult submitRequest(@RequestParam("fileId") Long fileId,
                                   @RequestParam("reason") String reason) {
        try {
            // 获取当前用户ID
            Long userId = SecurityUtils.getUserId();

            // 提交申请
            Long requestId = approvalService.submitRequest(fileId, userId, reason);

            return AjaxResult.success("申请提交成功，请等待审批", requestId);
        } catch (Exception e) {
            log.error("提交文件下载申请失败", e);
            return AjaxResult.error("提交申请失败: " + e.getMessage());
        }
    }

    /**
     * 批准申请
     */
    @PreAuthorize("@ss.hasPermi('files:download:approve')")
    @PostMapping("/approve")
    public AjaxResult approveRequest(@RequestParam("requestId") Long requestId,
                                    @RequestParam(value = "comment", required = false) String comment) {
        try {
            // 获取当前用户ID
            Long userId = SecurityUtils.getUserId();

            // 批准申请
            approvalService.approve(requestId, userId, comment);

            return AjaxResult.success("审批操作成功");
        } catch (Exception e) {
            log.error("批准申请失败", e);
            return AjaxResult.error("批准申请失败: " + e.getMessage());
        }
    }

    /**
     * 拒绝申请
     */
    @PreAuthorize("@ss.hasPermi('files:download:reject')")
    @PostMapping("/reject")
    public AjaxResult rejectRequest(@RequestParam("requestId") Long requestId,
                                   @RequestParam("comment") String comment) {
        try {
            // 获取当前用户ID
            Long userId = SecurityUtils.getUserId();

            // 拒绝申请
            approvalService.reject(requestId, userId, comment);

            return AjaxResult.success("拒绝操作成功");
        } catch (Exception e) {
            log.error("拒绝申请失败", e);
            return AjaxResult.error("拒绝申请失败: " + e.getMessage());
        }
    }

    /**
     * 撤销申请
     */
    @PreAuthorize("@ss.hasPermi('files:download:cancel')")
    @PostMapping("/cancel")
    public AjaxResult cancelRequest(@RequestParam("requestId") Long requestId) {
        try {
            // 获取当前用户ID
            Long userId = SecurityUtils.getUserId();

            // 撤销申请
            approvalService.cancelRequest(requestId, userId);

            return AjaxResult.success("撤销申请成功");
        } catch (Exception e) {
            log.error("撤销申请失败", e);
            return AjaxResult.error("撤销申请失败: " + e.getMessage());
        }
    }

    /**
     * 获取申请详情
     */
    @PreAuthorize("@ss.hasPermi('files:download:view')")
    @GetMapping("/detail/{requestId}")
    public AjaxResult getRequestDetail(@PathVariable("requestId") Long requestId) {
        try {
            ApprovalRequestVO detail = approvalService.getApprovalRequestDetails(requestId);
            return AjaxResult.success(detail);
        } catch (Exception e) {
            log.error("获取申请详情失败", e);
            return AjaxResult.error("获取申请详情失败: " + e.getMessage());
        }
    }

    /**
     * 查询我的申请列表
     */
    @PreAuthorize("@ss.hasPermi('files:download:list')")
    @GetMapping("/my-requests")
    public TableDataInfo listMyRequests() {
        startPage();
        Long userId = SecurityUtils.getUserId();
        // 从 TableSupport 获取分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        int pageNum = pageDomain.getPageNum();
        int pageSize = pageDomain.getPageSize();
        return approvalService.listUserRequestsByRequester(userId, pageNum, pageSize);
    }

    /**
     * 查询待我审批的列表
     */
    @PreAuthorize("@ss.hasPermi('files:download:approve')")
    @GetMapping("/pending-approvals")
    public TableDataInfo listPendingApprovals() {
        startPage();
        Long userId = SecurityUtils.getUserId();
        // 从 TableSupport 获取分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        int pageNum = pageDomain.getPageNum();
        int pageSize = pageDomain.getPageSize();
        return approvalService.listPendingApprovalsByApprover(userId, pageNum, pageSize);
    }

    /**
     * 查询已完成的审批记录
     */
    @PreAuthorize("@ss.hasPermi('files:download:view')")
    @GetMapping("/completed-approvals")
    public TableDataInfo listCompletedApprovals(
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "requesterName", required = false) String requesterName,
            @RequestParam(value = "status", required = false) String status) {
        startPage();
        Long userId = SecurityUtils.getUserId();
        // 从 TableSupport 获取分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        int pageNum = pageDomain.getPageNum();
        int pageSize = pageDomain.getPageSize();
        return approvalService.listCompletedApprovals(userId, pageNum, pageSize, fileName, requesterName, status);
    }
}
