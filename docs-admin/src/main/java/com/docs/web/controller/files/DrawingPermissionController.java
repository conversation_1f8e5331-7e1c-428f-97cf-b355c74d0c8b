package com.docs.web.controller.files;

import com.docs.common.annotation.Log;
import com.docs.common.core.controller.BaseController;
import com.docs.common.core.domain.AjaxResult;
import com.docs.common.enums.BusinessType;
import com.docs.files.config.DrawingPermissionProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 图纸权限配置管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/files/drawing/permissions")
@RequiredArgsConstructor
public class DrawingPermissionController extends BaseController {

    private final DrawingPermissionProperties drawingPermissionProperties;

    /**
     * 获取图纸权限配置
     */
    @PreAuthorize("@ss.hasPermi('files:drawing:permissions:view')")
    @GetMapping("/config")
    public AjaxResult getConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("subtypeMappings", drawingPermissionProperties.getSubtypeMappings());
        config.put("defaultConfig", drawingPermissionProperties.getDefaultConfig());
        config.put("validation", drawingPermissionProperties.getValidation());
        config.put("supportedSubTypes", drawingPermissionProperties.getSupportedSubTypes());
        
        return AjaxResult.success(config);
    }

    /**
     * 获取指定子类型的权限代码
     */
    @PreAuthorize("@ss.hasPermi('files:drawing:permissions:view')")
    @GetMapping("/subtype/{subType}")
    public AjaxResult getPermissionCode(@PathVariable String subType) {
        String permissionCode = drawingPermissionProperties.getPermissionCode(subType);
        boolean hasMapping = drawingPermissionProperties.hasPermissionMapping(subType);
        
        Map<String, Object> result = new HashMap<>();
        result.put("subType", subType);
        result.put("permissionCode", permissionCode);
        result.put("hasMapping", hasMapping);
        
        return AjaxResult.success(result);
    }

    /**
     * 获取所有支持的子类型
     */
    @PreAuthorize("@ss.hasPermi('files:drawing:permissions:view')")
    @GetMapping("/subtypes")
    public AjaxResult getSupportedSubTypes() {
        return AjaxResult.success(drawingPermissionProperties.getSupportedSubTypes());
    }

    /**
     * 测试权限映射
     */
    @PreAuthorize("@ss.hasPermi('files:drawing:permissions:test')")
    @PostMapping("/test")
    @Log(title = "图纸权限配置", businessType = BusinessType.OTHER)
    public AjaxResult testPermissionMapping(@RequestBody Map<String, String> testData) {
        String subType = testData.get("subType");
        if (subType == null || subType.trim().isEmpty()) {
            return AjaxResult.error("子类型不能为空");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("subType", subType);
        result.put("permissionCode", drawingPermissionProperties.getPermissionCode(subType));
        result.put("hasMapping", drawingPermissionProperties.hasPermissionMapping(subType));
        result.put("caseSensitive", drawingPermissionProperties.getValidation().isCaseSensitive());
        result.put("enabled", drawingPermissionProperties.getValidation().isEnabled());
        
        // 测试不同大小写的情况
        if (!drawingPermissionProperties.getValidation().isCaseSensitive()) {
            result.put("upperCaseTest", drawingPermissionProperties.getPermissionCode(subType.toUpperCase()));
            result.put("lowerCaseTest", drawingPermissionProperties.getPermissionCode(subType.toLowerCase()));
        }
        
        return AjaxResult.success(result);
    }

    /**
     * 获取配置状态信息
     */
    @PreAuthorize("@ss.hasPermi('files:drawing:permissions:view')")
    @GetMapping("/status")
    public AjaxResult getConfigStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("enabled", drawingPermissionProperties.getValidation().isEnabled());
        status.put("caseSensitive", drawingPermissionProperties.getValidation().isCaseSensitive());
        status.put("logPermissionChecks", drawingPermissionProperties.getValidation().isLogPermissionChecks());
        status.put("allowUnknownSubtype", drawingPermissionProperties.getDefaultConfig().isAllowUnknownSubtype());
        status.put("unknownSubtypePermission", drawingPermissionProperties.getDefaultConfig().getUnknownSubtypePermission());
        status.put("totalMappings", drawingPermissionProperties.getSubtypeMappings().size());
        
        return AjaxResult.success(status);
    }
}
