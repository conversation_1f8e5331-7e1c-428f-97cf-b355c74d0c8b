package com.docs.web.controller.files;


import com.docs.common.core.controller.BaseController;
import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.entity.FilesEntity;
import com.docs.common.core.domain.model.FileChangeRequest;
import com.docs.common.constant.HttpStatus;
import com.docs.common.core.domain.vo.FileChangeHistoryDTO;
import com.docs.common.exception.ServiceException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.docs.common.core.domain.vo.FileListVO;
import com.docs.common.core.page.TableDataInfo;

import com.docs.common.jooq.generated.tables.pojos.FileChanges;
import com.docs.common.utils.SecurityUtils;
import com.docs.files.service.FileChangesService;
import com.docs.files.service.FileDownloadAuthService;
import com.docs.files.service.FilesService;
import com.docs.files.repository.FileRepository;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.files.service.processing.FileProcessingService;
import com.docs.files.service.strategy.dto.FileUploadRequest;
import com.docs.files.util.FileValidationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 文件管理 Controller - 负责核心文件操作
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/files")
@Slf4j
@RequiredArgsConstructor
public class FileController extends BaseController {

    private final FilesService filesService;
    private final FileProcessingService fileProcessingService;
    private final FileChangesService fileChangesService;
    private final FileDownloadAuthService fileDownloadAuthService;
    private final FileRepository fileRepository;

    /**
     * 查询文件列表
     */
    @PreAuthorize("@ss.hasPermi('files:list')")
    @GetMapping("/list")
    public TableDataInfo list(FilesEntity filesEntity) {
        startPage();
        List<FileListVO> list = filesService.selectFilesList(filesEntity);
        return getDataTable(list);
    }

    /**
     * 上传文件
     */
    @PreAuthorize("@ss.hasPermi('files:upload')")
    @PostMapping("/upload")
    public AjaxResult uploadFile(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam("productModel") String productModel,
            @RequestParam("fileType") String fileType,
            @RequestParam("subType") String subType,
            @RequestParam("version") String version,
            @RequestParam("drawingNo") String drawingNo) {

        // 验证文件 (使用静态方法)
        AjaxResult validationResult = FileValidationUtils.validateFiles(files);
        if (validationResult != null) {
            return validationResult;
        }

        // 根据文件类型进行相应的子类型验证
        if (files.length > 0) {
            if ("DRAWING".equals(fileType)) {
                AjaxResult fileTypeValidation = FileValidationUtils.validateDrawingFileBySubType(files[0], subType);
                if (fileTypeValidation != null) {
                    return fileTypeValidation;
                }
            } else if ("BOM".equals(fileType)) {
                AjaxResult fileTypeValidation = FileValidationUtils.validateBomFileBySubType(files, subType);
                if (fileTypeValidation != null) {
                    return fileTypeValidation;
                }
            }
        }

        try {
            // 创建文件上传请求对象
            FileUploadRequest request = FileUploadRequest.builder()
                .files(files)
                .productModel(productModel)
                .fileType(fileType)
                .subType(subType)
                .drawingNo(drawingNo)
                .version(version)
                .build();

            // 使用注入的 fileProcessingService 处理请求
            return fileProcessingService.processUpload(request);
        } catch (Exception e) {
            // 异常处理和日志记录
            log.error("文件上传处理失败: productModel={}, fileType={}, subType={}, version={}, drawingNo={}",
                    productModel, fileType, subType, version, drawingNo, e);
            return AjaxResult.error("文件上传处理失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     * 需要检查用户是否有权限下载该文件：
     * 1. 管理员可以直接下载
     * 2. 普通用户需要有已批准的下载申请
     * 3. 如果没有权限，根据情况返回不同的错误信息
     */
    @PreAuthorize("@ss.hasPermi('files:download')")
    @GetMapping("/download/{fileId}")
    public void downloadFile(@PathVariable Long fileId, HttpServletResponse response) throws IOException {
        // 获取当前用户ID
        Long userId = SecurityUtils.getUserId();

        // 检查用户下载权限状态
        FileDownloadAuthService.PermissionResult permissionResult =
            fileDownloadAuthService.checkDownloadPermissionStatus(fileId, userId);

        if (permissionResult != FileDownloadAuthService.PermissionResult.ALLOWED) {
            log.info("用户 {} 尝试下载文件 {} 但权限状态为: {}", userId, fileId, permissionResult);

            // 设置响应状态为403（禁止访问）
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.setContentType("application/json;charset=UTF-8");

            // 获取文件信息来生成更具体的错误消息
            Files fileInfo = fileRepository.fetchFileById(fileId.intValue());
            String fileTypeMessage = generateFileTypeMessage(fileInfo);
            
            AjaxResult result;
            if (permissionResult == FileDownloadAuthService.PermissionResult.NO_PERMISSION) {
                // 用户未被分配权限
                String message = "您暂无权限下载" + fileTypeMessage + "，请联系管理员";
                result = AjaxResult.error(HttpStatus.FORBIDDEN, message);
                result.put("fileId", fileId);
                result.put("needApproval", false);
            } else {
                // 用户有权限但需要审批
                String message = "您没有权限下载此" + fileTypeMessage + "，需要申请权限";
                result = AjaxResult.error(HttpStatus.FORBIDDEN, message);
                result.put("fileId", fileId);
                result.put("needApproval", true);
            }

            // 写入响应
            response.getWriter().write(new ObjectMapper().writeValueAsString(result));
            return;
        }

        // 记录下载操作
        fileDownloadAuthService.recordDownloadAction(fileId, userId);

        try {
            // 执行文件下载
            filesService.downloadFile(fileId.intValue(), response);
        } catch (ServiceException e) {
            log.error("文件下载业务异常，fileId: {}, userId: {}", fileId, userId, e);

            // 设置响应状态为500（服务器内部错误）
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("application/json;charset=UTF-8");

            // 返回友好的错误信息
            AjaxResult result = AjaxResult.error(HttpStatus.ERROR, e.getMessage());
            result.put("fileId", fileId);
            result.put("errorType", "FILE_ERROR");

            // 写入响应
            response.getWriter().write(new ObjectMapper().writeValueAsString(result));
        } catch (IOException e) {
            log.error("文件下载IO异常，fileId: {}, userId: {}", fileId, userId, e);

            // 设置响应状态为500（服务器内部错误）
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("application/json;charset=UTF-8");

            // 返回友好的错误信息
            AjaxResult result = AjaxResult.error(HttpStatus.ERROR, "文件下载失败，请稍后重试");
            result.put("fileId", fileId);
            result.put("errorType", "IO_ERROR");

            // 写入响应
            response.getWriter().write(new ObjectMapper().writeValueAsString(result));
        }
    }

    /**
     * 删除文件
     */
    @PreAuthorize("@ss.hasPermi('files:delete')")
    @DeleteMapping("/delete/{fileId}")
    public AjaxResult deleteFile(@PathVariable Integer fileId) {
        return toAjax(filesService.deleteFile(fileId));
    }

    /**
     * 获取文件详情
     */
    @PreAuthorize("@ss.hasPermi('files:list')")
    @GetMapping("/{fileId}")
    public AjaxResult getFileDetail(@PathVariable Long fileId) {
        try {
            FilesEntity filesEntity = new FilesEntity();
            filesEntity.setId(fileId.intValue());
            List<FileListVO> fileList = filesService.selectFilesList(filesEntity);

            if (fileList == null || fileList.isEmpty()) {
                return AjaxResult.error("文件不存在");
            }

            FileListVO file = fileList.get(0);
            return AjaxResult.success(file);
        } catch (Exception e) {
            log.error("获取文件详情失败", e);
            return AjaxResult.error("获取文件详情失败: " + e.getMessage());
        }
    }

      /**
     * 变更版本
     */
    @PreAuthorize("@ss.hasPermi('files:change')")
    @PostMapping("/version/change")
    public AjaxResult changeVersion(
            @RequestParam("files") MultipartFile[] files,
            @RequestParam("fileId") Integer fileId,
            @RequestParam(value = "productModelId", required = false) Integer productModelId,
            @RequestParam("newVersion") String newVersion,
            @RequestParam("changeReason") String changeReason,
            @RequestParam("drawingNo") String drawingNo) {

        // 验证文件
        AjaxResult validationResult = FileValidationUtils.validateFiles(files);
        if (validationResult != null) {
            return validationResult;
        }

        FileChangeRequest request = new FileChangeRequest();
        request.setFileId(fileId);
        request.setProductModelId(productModelId);
        request.setNewVersion(newVersion);
        request.setChangeReason(changeReason);
        request.setFiles(files);
        // 使用带版本号校验的方法
        return filesService.changeFileVersionWithValidation(request);
    }

    /**
     * 获取变更历史
     */
    @PreAuthorize("@ss.hasPermi('files:list')")
    @GetMapping("/version/history")
    public TableDataInfo getFileChanges(FileChanges fileChanges) {
        startPage();
        // 使用包含详细信息的查询方法
        List<FileChangeHistoryDTO> list = fileChangesService.selectFileChangesDetailList(fileChanges);
        return getDataTable(list);
    }

    /**
     * 根据文件信息生成具体的文件类型描述
     *
     * @param fileInfo 文件信息
     * @return 文件类型描述字符串
     */
    private String generateFileTypeMessage(Files fileInfo) {
        if (fileInfo == null) {
            return "文件";
        }

        switch (fileInfo.getFileType()) {
            case DRAWING:
                return getDrawingTypeMessage(fileInfo.getSubType());
            case BOM_DRAWING:
                // BOM_DRAWING 类型需要区分是图纸还是研发文件
                if ("RD_FILE".equals(fileInfo.getSubType())) {
                    return "研发文件";
                } else {
                    return getDrawingTypeMessage(fileInfo.getSubType());
                }
            case BOM:
                return "BOM文件";
            case SOFTWARE:
                return "软件文件";
            default:
                return "文件";
        }
    }
    
    /**
     * 根据图纸子类型生成具体的图纸类型描述
     *
     * @param subType 图纸子类型
     * @return 图纸类型描述字符串
     */
    private String getDrawingTypeMessage(String subType) {
        if (subType == null) {
            return "图纸";
        }

        switch (subType) {
            case "PDF":
                return "PDF图纸";
            case "2D":
                return "2D图纸";
            case "3D":
                return "3D图纸";
            case "2D/3D":
                return "2D/3D图纸";
            case "RD_FILE":
                return "研发文件";
            default:
                return "图纸";
        }
    }
}
