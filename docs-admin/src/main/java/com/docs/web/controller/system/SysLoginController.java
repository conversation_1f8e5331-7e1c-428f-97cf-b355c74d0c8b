package com.docs.web.controller.system;

import com.docs.common.constant.Constants;
import com.docs.common.core.domain.AjaxResult;
import com.docs.common.core.domain.entity.SysMenu;
import com.docs.common.core.domain.entity.SysUser;
import com.docs.system.domain.SysPost;
import com.docs.common.core.domain.model.LoginBody;
import com.docs.common.utils.SecurityUtils;
import com.docs.framework.web.service.SysLoginService;
import com.docs.framework.web.service.SysPermissionService;
import com.docs.system.service.ISysMenuService;
import com.docs.system.service.ISysPostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ISysPostService postService;

    /**
     * 登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);

        // 获取用户岗位信息
        List<Long> postIds = postService.selectPostListByUserId(user.getUserId());
        List<String> postCodes = postIds.stream()
            .map(postId -> {
                SysPost post = postService.selectPostById(postId);
                return post != null ? post.getPostCode() : null;
            })
            .filter(postCode -> postCode != null)
            .collect(Collectors.toList());

        // 计算用户权限标识
        boolean isSupervisor = postCodes.contains("supervisor");
        boolean isManager = postCodes.contains("manager");
        boolean isEmployee = postCodes.contains("user");

        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        // 新增：岗位相关信息
        ajax.put("postCodes", postCodes);
        ajax.put("isSupervisor", isSupervisor);
        ajax.put("isManager", isManager);
        ajax.put("isEmployee", isEmployee);
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
