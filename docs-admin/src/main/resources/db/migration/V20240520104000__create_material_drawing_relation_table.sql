-- 创建物料编码和图纸编号关联表
CREATE TABLE IF NOT EXISTS material_drawing_relations (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    material_code VARCHAR(100) NOT NULL COMMENT '物料编码',
    drawing_no VARCHAR(100) NOT NULL COMMENT '图纸编号',
    INDEX idx_material_code (material_code),
    INDEX idx_drawing_no (drawing_no),
    UNIQUE INDEX unq_material_drawing (material_code, drawing_no)
) COMMENT = '物料与图纸关联表'; 