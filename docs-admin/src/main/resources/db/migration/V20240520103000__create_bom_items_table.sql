-- 创建BOM表项表
CREATE TABLE IF NOT EXISTS bom_items (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    file_id INT NOT NULL COMMENT '关联文件ID',
    item_no VARCHAR(50) COMMENT '序号',
    depth INT COMMENT '深度',
    material_code VARCHAR(100) COMMENT '物料编码',
    material_name VARCHAR(255) COMMENT '物料名称',
    specification VARCHAR(255) COMMENT '型号规格',
    supplier VARCHAR(255) COMMENT '供应商/品牌',
    unit VARCHAR(50) COMMENT '单位',
    quantity DECIMAL(10,2) COMMENT '数量',
    production_property VARCHAR(100) COMMENT '生产属性',
    category VARCHAR(100) COMMENT '类别',
    remark TEXT COMMENT '备注',
    bom_type VARCHAR(50) COMMENT '所属BOM类型(电子BOM/结构BOM)',
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_file_id (file_id)
) COMMENT = 'BOM表项数据';

-- 外键约束
ALTER TABLE bom_items 
ADD CONSTRAINT fk_bom_items_file_id
FOREIGN KEY (file_id) REFERENCES files(id)
ON DELETE CASCADE ON UPDATE CASCADE; 