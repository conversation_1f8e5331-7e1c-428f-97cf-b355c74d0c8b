-- =============================================
-- 为BOM项添加研发文件支持
-- 扩展 material_drawing_map 表，支持研发文件管理
-- =============================================

-- 1. 首先扩展 files 表，添加必要的字段和新的文件类型
-- 添加RD_FILE到file_type枚举
ALTER TABLE files
MODIFY COLUMN file_type ENUM('BOM','DRAWING','BOM_DRAWING','SOFTWARE','RD_FILE') DEFAULT NULL COMMENT '文件类型';

ALTER TABLE files
ADD COLUMN file_size BIGINT DEFAULT NULL COMMENT '文件大小(字节)' AFTER file_name;

ALTER TABLE files
ADD COLUMN upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间' AFTER file_size;

ALTER TABLE files
ADD COLUMN upload_user_id BIGINT DEFAULT NULL COMMENT '上传用户ID' AFTER upload_time;

-- 添加外键约束
ALTER TABLE files
ADD CONSTRAINT fk_files_upload_user_id
FOREIGN KEY (upload_user_id) REFERENCES sys_user(user_id)
ON DELETE SET NULL ON UPDATE CASCADE;

-- 2. 添加研发文件字段到 material_drawing_map 表
ALTER TABLE material_drawing_map 
ADD COLUMN rd_file_id INT DEFAULT NULL COMMENT '研发文件ID' AFTER compressed_file_id;

-- 3. 添加外键约束
ALTER TABLE material_drawing_map
ADD CONSTRAINT fk_material_drawing_map_rd_file_id
FOREIGN KEY (rd_file_id) REFERENCES files(id)
ON DELETE SET NULL ON UPDATE CASCADE;

-- 4. 添加索引提升查询性能
CREATE INDEX idx_material_drawing_map_rd_file_id ON material_drawing_map(rd_file_id);

-- 5. 扩展变更历史表，支持研发文件类型
-- 修改 file_type 字段，扩展长度并添加新类型支持
ALTER TABLE bom_drawing_change_history 
MODIFY COLUMN file_type VARCHAR(30) NOT NULL COMMENT '文件类型: PDF, 2D, 3D, COMPRESSED, RD_FILE';

-- 5. 添加研发文件相关的系统权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES 
('研发文件上传', 0, 999, '', '', 1, 0, 'F', '0', '0', 'files:rd:upload', '', 'admin', NOW(), 'admin', NOW(), '研发文件上传权限'),
('研发文件下载', 0, 999, '', '', 1, 0, 'F', '0', '0', 'files:rd:download', '', 'admin', NOW(), 'admin', NOW(), '研发文件下载权限'),
('研发文件历史', 0, 999, '', '', 1, 0, 'F', '0', '0', 'files:rd:history', '', 'admin', NOW(), 'admin', NOW(), '研发文件历史查看权限');

-- 6. 插入配置说明
INSERT IGNORE INTO sys_config (config_name, config_key, config_value, config_type, remark) VALUES 
('BOM研发文件功能版本', 'bom.rd.file.version', 'v1.0', 'Y', 'BOM项研发文件管理功能，支持独立的权限控制和变更历史追踪');

-- 7. 为提高查询性能创建复合索引
CREATE INDEX idx_material_drawing_map_material_rd_file ON material_drawing_map(material_code, rd_file_id);

-- 8. 为files表的新字段创建索引
CREATE INDEX idx_files_upload_user_id ON files(upload_user_id);
CREATE INDEX idx_files_upload_time ON files(upload_time);