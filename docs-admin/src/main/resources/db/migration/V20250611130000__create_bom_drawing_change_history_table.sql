-- =============================================
-- BOM图纸变更历史表重构
-- 支持4种文件类型的完整变更历史追踪
-- =============================================

-- 创建新的BOM图纸变更历史表
CREATE TABLE `bom_drawing_change_history` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bom_item_id` INT NOT NULL COMMENT 'BOM项ID',
  `material_code` VARCHAR(50) NOT NULL COMMENT '物料编码',
  `change_type` VARCHAR(20) NOT NULL COMMENT '变更类型: ADD, UPDATE, DELETE',
  `file_type` VARCHAR(20) NOT NULL COMMENT '文件类型: PDF, 2D, 3D, COMPRESSED',
  
  -- 变更前信息
  `old_drawing_no` VARCHAR(100) DEFAULT NULL COMMENT '变更前图纸编号',
  `old_file_id` INT DEFAULT NULL COMMENT '变更前文件ID',
  `old_file_name` VARCHAR(255) DEFAULT NULL COMMENT '变更前文件名',
  `old_file_version` VARCHAR(50) DEFAULT NULL COMMENT '变更前文件版本',
  
  -- 变更后信息
  `new_drawing_no` VARCHAR(100) DEFAULT NULL COMMENT '变更后图纸编号',
  `new_file_id` INT DEFAULT NULL COMMENT '变更后文件ID',
  `new_file_name` VARCHAR(255) DEFAULT NULL COMMENT '变更后文件名',
  `new_file_version` VARCHAR(50) DEFAULT NULL COMMENT '变更后文件版本',
  
  -- 变更元信息
  `change_reason` VARCHAR(500) DEFAULT NULL COMMENT '变更原因',
  `changed_by` INT NOT NULL COMMENT '变更人ID',
  `changed_by_name` VARCHAR(100) NOT NULL COMMENT '变更人姓名',
  `changed_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_bom_item_id` (`bom_item_id`),
  KEY `idx_material_code` (`material_code`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_changed_at` (`changed_at`),
  KEY `idx_changed_by` (`changed_by`),
  
  -- 外键约束
  CONSTRAINT `fk_bom_drawing_history_bom_item` 
    FOREIGN KEY (`bom_item_id`) REFERENCES `bom_items` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_bom_drawing_history_old_file` 
    FOREIGN KEY (`old_file_id`) REFERENCES `files` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_bom_drawing_history_new_file` 
    FOREIGN KEY (`new_file_id`) REFERENCES `files` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_bom_drawing_history_changed_by` 
    FOREIGN KEY (`changed_by`) REFERENCES `sys_user` (`user_id`) ON DELETE RESTRICT
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='BOM图纸变更历史表';

-- 添加变更类型检查约束
ALTER TABLE `bom_drawing_change_history` 
ADD CONSTRAINT `chk_change_type` 
CHECK (`change_type` IN ('ADD', 'UPDATE', 'DELETE'));

-- 添加文件类型检查约束
ALTER TABLE `bom_drawing_change_history` 
ADD CONSTRAINT `chk_file_type` 
CHECK (`file_type` IN ('PDF', '2D', '3D', 'COMPRESSED'));

-- 创建视图用于查询完整的变更历史信息
CREATE VIEW `bom_drawing_change_history_view` AS
SELECT 
    h.id,
    h.bom_item_id,
    h.material_code,
    h.change_type,
    h.file_type,
    h.old_drawing_no,
    h.old_file_id,
    h.old_file_name,
    h.old_file_version,
    h.new_drawing_no,
    h.new_file_id,
    h.new_file_name,
    h.new_file_version,
    h.change_reason,
    h.changed_by,
    h.changed_by_name,
    h.changed_at,
    -- BOM项信息
    bi.material_name,
    bi.specification,
    -- 变更前文件详细信息
    old_f.file_path AS old_file_path,
    old_f.file_size AS old_file_size,
    old_f.status AS old_file_status,
    -- 变更后文件详细信息
    new_f.file_path AS new_file_path,
    new_f.file_size AS new_file_size,
    new_f.status AS new_file_status
FROM 
    `bom_drawing_change_history` h
    LEFT JOIN `bom_items` bi ON h.bom_item_id = bi.id
    LEFT JOIN `files` old_f ON h.old_file_id = old_f.id
    LEFT JOIN `files` new_f ON h.new_file_id = new_f.id;

-- 为提高查询性能创建复合索引
CREATE INDEX `idx_bom_item_file_type_time` ON `bom_drawing_change_history` 
(`bom_item_id`, `file_type`, `changed_at` DESC);

-- 插入说明注释
INSERT INTO `sys_config` (`config_name`, `config_key`, `config_value`, `config_type`, `remark`) 
VALUES 
('BOM图纸变更历史表版本', 'bom.drawing.history.version', 'v2.0', 'Y', 
'新版BOM图纸变更历史表，支持4种文件类型的完整变更追踪');