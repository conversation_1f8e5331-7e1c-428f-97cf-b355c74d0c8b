-- Add multi-file support to material_drawing_map table
-- This migration adds separate columns for different file types to support uploading 3 files simultaneously

-- Add new columns for different file types
ALTER TABLE material_drawing_map
ADD COLUMN pdf_file_id INT DEFAULT NULL COMMENT 'PDF文件ID' AFTER drawing_file_id,
ADD COLUMN drawing_2d_file_id INT DEFAULT NULL COMMENT '2D图纸文件ID' AFTER pdf_file_id,
ADD COLUMN drawing_3d_file_id INT DEFAULT NULL COMMENT '3D图纸文件ID' AFTER drawing_2d_file_id,
ADD COLUMN compressed_file_id INT DEFAULT NULL COMMENT '2D/3D压缩包文件ID' AFTER drawing_3d_file_id;

-- Add foreign key constraints for the new file ID columns
ALTER TABLE material_drawing_map
ADD CONSTRAINT fk_material_drawing_map_pdf_file_id
FOREIGN KEY (pdf_file_id) REFERENCES files(id)
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE material_drawing_map
ADD CONSTRAINT fk_material_drawing_map_drawing_2d_file_id
FOREIGN KEY (drawing_2d_file_id) REFERENCES files(id)
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE material_drawing_map
ADD CONSTRAINT fk_material_drawing_map_drawing_3d_file_id
FOREIGN KEY (drawing_3d_file_id) REFERENCES files(id)
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE material_drawing_map
ADD CONSTRAINT fk_material_drawing_map_compressed_file_id
FOREIGN KEY (compressed_file_id) REFERENCES files(id)
ON DELETE SET NULL ON UPDATE CASCADE;

-- Migrate existing data from drawing_file_id to appropriate new columns based on file sub_type
-- This will move existing files to the correct new column based on their type
UPDATE material_drawing_map mdm
JOIN files f ON mdm.drawing_file_id = f.id
SET mdm.pdf_file_id = f.id
WHERE f.sub_type = 'PDF';

UPDATE material_drawing_map mdm
JOIN files f ON mdm.drawing_file_id = f.id
SET mdm.drawing_2d_file_id = f.id
WHERE f.sub_type = '2D';

UPDATE material_drawing_map mdm
JOIN files f ON mdm.drawing_file_id = f.id
SET mdm.drawing_3d_file_id = f.id
WHERE f.sub_type = '3D';

UPDATE material_drawing_map mdm
JOIN files f ON mdm.drawing_file_id = f.id
SET mdm.compressed_file_id = f.id
WHERE f.sub_type = '2D/3D';

-- Note: We keep the original drawing_file_id column for now to maintain backward compatibility
-- It can be dropped in a future migration after confirming all data has been migrated successfully
-- ALTER TABLE material_drawing_map DROP COLUMN drawing_file_id;