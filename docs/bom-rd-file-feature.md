# BOM研发文件管理功能开发文档

## 概述

BOM研发文件管理功能是对现有BOM系统的扩展，为BOM项添加独立的研发文件管理能力。该功能复用现有的文件管理、权限控制和审批流程基础设施，提供研发文件的上传、下载、删除和变更历史追踪功能。

## 功能特性

### 1. 核心功能
- **研发文件上传**：支持任意格式文件上传，每个BOM项只能有一个研发文件
- **研发文件下载**：支持权限控制和审批流程的文件下载
- **研发文件删除**：支持文件删除和变更历史记录
- **变更历史追踪**：完整的文件变更历史记录和查询

### 2. 权限控制
- **独立权限管理**：研发文件有独立的权限配置
- **分级权限控制**：上传、下载、历史查看分别控制
- **审批流程集成**：下载文件需要审批（可配置）

### 3. 系统集成
- **复用现有基础设施**：文件存储、审批流程、权限系统
- **统一的变更历史**：与图纸文件使用相同的历史记录表
- **一致的操作体验**：与现有图纸管理保持一致的交互方式

## 数据库设计

### 1. 扩展 material_drawing_map 表

```sql
-- 添加研发文件字段
ALTER TABLE material_drawing_map 
ADD COLUMN rd_file_id INT DEFAULT NULL COMMENT '研发文件ID' AFTER compressed_file_id;

-- 添加外键约束
ALTER TABLE material_drawing_map
ADD CONSTRAINT fk_material_drawing_map_rd_file_id
FOREIGN KEY (rd_file_id) REFERENCES files(id)
ON DELETE SET NULL ON UPDATE CASCADE;

-- 添加索引
CREATE INDEX idx_material_drawing_map_rd_file_id ON material_drawing_map(rd_file_id);
```

### 2. 扩展变更历史表

```sql
-- 更新文件类型检查约束，添加 RD_FILE
ALTER TABLE bom_drawing_change_history 
DROP CONSTRAINT IF EXISTS chk_file_type;

ALTER TABLE bom_drawing_change_history 
ADD CONSTRAINT chk_file_type 
CHECK (file_type IN ('PDF', '2D', '3D', 'COMPRESSED', 'RD_FILE'));
```

### 3. 系统权限配置

```sql
INSERT IGNORE INTO sys_menu (menu_name, perms, menu_type) VALUES 
('研发文件上传', 'files:rd:upload', 'F'),
('研发文件下载', 'files:rd:download', 'F'),
('研发文件历史', 'files:rd:history', 'F');
```

## 后端API设计

### 1. Controller 接口

#### 上传研发文件
```
POST /bomItem/uploadRdFile
Content-Type: multipart/form-data

Parameters:
- file: MultipartFile (必需) - 研发文件
- bomItemId: Integer (必需) - BOM项ID  
- reason: String (可选) - 上传原因

Response:
{
  "code": 200,
  "msg": "研发文件上传成功",
  "data": {
    "fileId": 123,
    "fileName": "研发文档.docx",
    "fileSize": 1024000,
    "isReplacement": false
  }
}
```

#### 下载研发文件
```
GET /bomItem/downloadRdFile/{bomItemId}

Parameters:
- bomItemId: Integer (路径参数) - BOM项ID

Response: 文件流或重定向到审批申请页面
```

#### 删除研发文件
```
DELETE /bomItem/deleteRdFile/{bomItemId}

Parameters:
- bomItemId: Integer (路径参数) - BOM项ID
- reason: String (可选) - 删除原因

Response:
{
  "code": 200,
  "msg": "研发文件删除成功",
  "data": {
    "deletedFileId": 123,
    "deletedFileName": "研发文档.docx"
  }
}
```

#### 获取研发文件信息
```
GET /bomItem/rdFileInfo/{bomItemId}

Response:
{
  "code": 200,
  "data": {
    "hasRdFile": true,
    "fileId": 123,
    "fileName": "研发文档.docx",
    "fileSize": 1024000,
    "fileType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "uploadTime": "2025-06-28T10:00:00",
    "status": "ACTIVE"
  }
}
```

#### 获取变更历史
```
GET /bomItem/rdFileHistory/{bomItemId}

Response:
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "changeType": "ADD",
      "newFileName": "研发文档v1.0.docx",
      "changeReason": "首次上传研发文档",
      "changedBy": "张三",
      "changedAt": "2025-06-28T10:00:00"
    }
  ]
}
```

### 2. 权限配置

#### drawing-permissions.yml
```yaml
drawing:
  permissions:
    subtype-mappings:
      PDF: "files:download:pdf"
      2D: "files:download:2d"
      3D: "files:download:3d"
      2D/3D: "files:download:2d3d"
      RD_FILE: "files:download:rd"  # 新增研发文件权限
```

#### rd-file-permissions.yml
```yaml
rd-file:
  permissions:
    operation-mappings:
      upload: "files:rd:upload"
      download: "files:rd:download"
      delete: "files:rd:upload"
      history: "files:rd:history"
    
    file-type-config:
      allowed-extensions: []  # 允许所有文件类型
      max-file-size: 100     # 最大100MB
      check-content-type: false
    
    approval-config:
      require-approval-for-download: true
      require-approval-for-replace: true
      approval-workflow-type: "file_download"
```

## 核心业务逻辑

### 1. 文件上传流程

1. **参数验证**：验证文件和BOM项ID
2. **权限检查**：验证用户是否有研发文件上传权限
3. **BOM项验证**：确认BOM项存在
4. **映射记录查找**：查找material_drawing_map记录
5. **重复文件检查**：检查是否已存在研发文件
6. **文件上传**：上传到文件系统
7. **数据库更新**：更新material_drawing_map表
8. **历史记录**：记录变更历史
9. **审批触发**：如需要，触发审批流程

### 2. 文件下载流程

1. **参数验证**：验证BOM项ID
2. **权限检查**：验证用户是否有研发文件下载权限
3. **BOM项验证**：确认BOM项存在
4. **文件存在性检查**：确认研发文件存在
5. **审批检查**：检查是否需要审批
6. **文件下载**：执行文件下载或重定向到审批页面

### 3. 变更历史记录

所有研发文件操作都会记录到 `bom_drawing_change_history` 表：
- **文件类型**：标记为 `RD_FILE`
- **操作类型**：`ADD`（新增）、`UPDATE`（替换）、`DELETE`（删除）
- **详细信息**：包含操作人、时间、原因等

## 部署说明

### 1. 数据库迁移

执行以下SQL脚本：
```bash
# 执行数据库迁移脚本
mysql -u username -p database_name < docs-admin/src/main/resources/db/migration/V20250628145000__add_rd_file_support_to_material_drawing_map.sql
```

### 2. 权限配置

1. **更新权限配置文件**：
   - `drawing-permissions.yml`
   - `rd-file-permissions.yml`

2. **分配用户权限**：
   - 在系统管理中为相关角色分配研发文件权限
   - `files:rd:upload` - 研发文件上传权限
   - `files:rd:download` - 研发文件下载权限
   - `files:rd:history` - 研发文件历史权限

### 3. 应用重启

重启应用以加载新的权限配置和数据库表结构。

## 前端集成指南

### 1. API调用示例

```javascript
// 上传研发文件
const formData = new FormData();
formData.append('file', file);
formData.append('bomItemId', bomItemId);
formData.append('reason', reason);

fetch('/bomItem/uploadRdFile', {
  method: 'POST',
  body: formData
});

// 获取研发文件信息
fetch(`/bomItem/rdFileInfo/${bomItemId}`)
  .then(response => response.json())
  .then(data => {
    if (data.code === 200 && data.data.hasRdFile) {
      // 显示研发文件信息
    }
  });

// 下载研发文件
window.open(`/bomItem/downloadRdFile/${bomItemId}`);
```

### 2. 权限控制

```javascript
// 检查权限
if (hasPermission('files:rd:upload')) {
  // 显示上传按钮
}

if (hasPermission('files:rd:download')) {
  // 显示下载按钮
}

if (hasPermission('files:rd:history')) {
  // 显示历史按钮
}
```

## 测试用例

### 1. 功能测试

- [ ] 研发文件上传成功
- [ ] 研发文件下载成功
- [ ] 研发文件删除成功
- [ ] 变更历史记录正确
- [ ] 文件信息查询正确

### 2. 权限测试

- [ ] 无上传权限时上传失败
- [ ] 无下载权限时下载失败
- [ ] 无历史权限时查询失败

### 3. 边界测试

- [ ] 大文件上传（接近100MB限制）
- [ ] 不存在的BOM项ID
- [ ] 重复上传文件
- [ ] 删除不存在的文件

### 4. 审批流程测试

- [ ] 下载时正确触发审批流程
- [ ] 审批通过后可以下载
- [ ] 审批拒绝后无法下载

## 维护说明

### 1. 日志监控

关键操作都有完整的日志记录：
```
INFO  - 开始上传研发文件: bomItemId=123, fileName=研发文档.docx, reason=初次上传
INFO  - 研发文件上传成功: bomItemId=123, fileId=456, fileName=研发文档.docx
ERROR - 上传研发文件失败: bomItemId=123, fileName=研发文档.docx
```

### 2. 性能优化

- 使用索引优化查询性能
- 批量操作时注意事务管理
- 大文件上传时注意内存使用

### 3. 数据备份

- 定期备份 `material_drawing_map` 表
- 定期备份 `bom_drawing_change_history` 表
- 定期备份 `files` 表和物理文件

## 扩展计划

### 1. 短期扩展

- 支持多个研发文件上传
- 文件版本管理
- 文件预览功能

### 2. 长期规划

- 工艺文件管理
- 质量文件管理
- 文件协作功能

## 版本历史

- **v1.0** (2025-06-28): 初始版本，基础的研发文件管理功能