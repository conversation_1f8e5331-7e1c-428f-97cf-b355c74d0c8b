# 手动部署脚本使用文档

## 概述

本文档详细介绍了 `scripts/manual-deploy.sh` 脚本的使用方法。该脚本专为中国大陆服务器环境优化，支持原生部署方式（前后端不使用Docker），仅MySQL和Redis使用Docker容器。

## 系统要求

### 服务器环境
- **操作系统**: CentOS 7+ / Ubuntu 18.04+ / RHEL 7+
- **架构**: x86_64
- **内存**: 最少2GB，推荐4GB+
- **磁盘**: 最少10GB可用空间

### 必需软件
- **Java**: OpenJDK 8+
- **Maven**: 3.6+
- **Node.js**: 16+
- **npm**: 8+
- **Nginx**: 任意版本（支持编译安装和包管理器安装）
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

## 配置说明

### 项目配置
```bash
PROJECT_ROOT="/data/docs-manage"          # 项目根目录
BACKEND_MODULE="docs-admin"               # 后端模块名
BACKEND_JAR_NAME="docs-admin.jar"         # JAR文件名
BACKEND_PORT="8081"                       # 后端服务端口
FRONTEND_DIST_DIR="docs-ui/dist"          # 前端构建目录
NGINX_WEB_ROOT="/var/www/html/docs"       # Nginx静态文件目录
```

### 数据库配置
```bash
MYSQL_HOST="localhost"                    # MySQL主机
MYSQL_PORT="3306"                        # MySQL端口
MYSQL_USER="root"                        # MySQL用户名
MYSQL_PASSWORD="123456"                  # MySQL密码
MYSQL_DATABASE="file_manage"             # 数据库名
```

### Redis配置
```bash
REDIS_HOST="localhost"                    # Redis主机
REDIS_PORT="6379"                        # Redis端口
REDIS_DATABASE="0"                       # Redis数据库编号
```

## 使用方法

### 基本语法
```bash
./scripts/manual-deploy.sh [选项]
```

### 可用选项

| 选项 | 说明 | 用途 |
|------|------|------|
| `build-backend` | 仅构建后端 | 开发调试时单独构建后端 |
| `build-frontend` | 仅构建前端 | 开发调试时单独构建前端 |
| `deploy-backend` | 仅部署后端 | 后端代码更新后快速部署 |
| `deploy-frontend` | 仅部署前端 | 前端代码更新后快速部署 |
| `full-deploy` | 完整部署（默认） | 首次部署或重大更新 |
| `stop` | 停止所有服务 | 维护或故障排除 |
| `status` | 查看服务状态 | 监控服务运行状态 |
| `diagnose` | 诊断nginx配置问题 | 故障排除和问题诊断 |
| `help` | 显示帮助信息 | 查看使用说明 |

### 使用示例

#### 1. 完整部署（推荐首次使用）
```bash
./scripts/manual-deploy.sh
# 或
./scripts/manual-deploy.sh full-deploy
```

#### 2. 仅更新前端
```bash
./scripts/manual-deploy.sh build-frontend
./scripts/manual-deploy.sh deploy-frontend
```

#### 3. 仅更新后端
```bash
./scripts/manual-deploy.sh build-backend
./scripts/manual-deploy.sh deploy-backend
```

#### 4. 查看服务状态
```bash
./scripts/manual-deploy.sh status
```

#### 5. 故障诊断
```bash
./scripts/manual-deploy.sh diagnose
```

## 部署流程

### 完整部署流程
1. **环境检查** - 验证必需软件和目录
2. **数据库连接检查** - 确保MySQL容器运行正常
3. **停止现有服务** - 优雅停止后端服务
4. **构建后端** - Maven构建包含jOOQ代码生成
5. **构建前端** - npm构建生产版本
6. **部署前端** - 复制文件到nginx目录
7. **配置nginx** - 生成并加载配置文件
8. **启动后端** - 启动Spring Boot应用
9. **验证部署** - 检查服务状态

### 构建优化
- **Maven**: 使用阿里云镜像加速依赖下载
- **npm**: 使用淘宝镜像加速包安装
- **Docker**: 支持国内镜像源

## 访问地址

部署成功后，可通过以下地址访问：

- **前端应用**: http://localhost
- **后端API**: http://localhost/api 或 http://localhost/prod-api
- **直接后端**: http://localhost:8081

## 测试命令

```bash
# 测试前端
curl localhost

# 测试后端API
curl localhost/api

# 测试直接后端访问
curl localhost:8081
```

## 日志文件

- **后端日志**: `/data/docs-manage/logs/backend.log`
- **Nginx访问日志**: `/var/log/nginx/access.log`
- **Nginx错误日志**: `/var/log/nginx/error.log`

## 故障排除

### 常见问题

#### 1. nginx配置文件未加载
**症状**: 访问显示nginx默认页面
**解决**: 运行诊断命令
```bash
./scripts/manual-deploy.sh diagnose
```

#### 2. 后端服务启动失败
**症状**: API请求返回502错误
**检查**: 查看后端日志
```bash
tail -f /data/docs-manage/logs/backend.log
```

#### 3. 数据库连接失败
**症状**: 后端启动时报数据库连接错误
**检查**: 确认MySQL容器状态
```bash
docker ps | grep mysql
```

#### 4. 前端文件未更新
**症状**: 前端显示旧版本内容
**解决**: 清除浏览器缓存或强制刷新

### 诊断工具

脚本内置诊断功能，可快速定位问题：

```bash
./scripts/manual-deploy.sh diagnose
```

诊断内容包括：
- Nginx配置文件检查
- 前端文件部署检查
- 配置语法验证
- 当前加载的配置显示

## 高级配置

### 自定义配置
如需修改默认配置，编辑脚本顶部的配置区域：

```bash
# 项目配置
PROJECT_ROOT="/data/docs-manage"
BACKEND_MODULE="docs-admin"
# ... 其他配置
```

### Nginx配置定制
脚本会自动生成nginx配置，如需定制，可在 `configure_nginx()` 函数中修改。

### 环境变量
后端服务支持以下环境变量：
- `SPRING_DATASOURCE_DRUID_MASTER_URL`
- `SPRING_DATASOURCE_DRUID_MASTER_USERNAME`
- `SPRING_DATASOURCE_DRUID_MASTER_PASSWORD`
- `SPRING_REDIS_HOST`
- `SPRING_REDIS_PORT`
- `SPRING_REDIS_DATABASE`

## 维护建议

### 定期维护
1. **日志清理**: 定期清理过大的日志文件
2. **磁盘空间**: 监控磁盘使用情况
3. **服务监控**: 定期检查服务状态

### 备份建议
1. **数据库备份**: 定期备份MySQL数据
2. **配置备份**: 备份nginx和应用配置文件
3. **代码备份**: 确保代码仓库同步

## 安全注意事项

1. **密码安全**: 修改默认数据库密码
2. **防火墙**: 配置适当的防火墙规则
3. **SSL证书**: 生产环境建议启用HTTPS
4. **访问控制**: 限制管理接口访问

## 性能优化

1. **JVM参数**: 根据服务器配置调整内存设置
2. **数据库连接池**: 优化数据库连接参数
3. **Nginx缓存**: 启用静态资源缓存
4. **gzip压缩**: 已默认启用

## 支持与反馈

如遇到问题或需要功能改进，请：
1. 首先运行诊断命令排查问题
2. 查看相关日志文件
3. 检查本文档的故障排除部分
4. 联系技术支持团队

## 部署架构

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户浏览器     │    │     Nginx       │    │   Spring Boot   │
│                │    │   (端口 80)      │    │   (端口 8081)    │
│                │◄──►│                │◄──►│                │
│                │    │  静态文件服务    │    │    后端API      │
│                │    │  反向代理       │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              │                        │
                              ▼                        ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │   静态文件目录   │    │   Docker容器     │
                    │/var/www/html/docs│    │                │
                    │                │    │  MySQL + Redis  │
                    │  前端构建产物    │    │                │
                    └─────────────────┘    └─────────────────┘
```

### 请求流程
1. **静态资源请求** → Nginx直接服务
2. **API请求** → Nginx反向代理到Spring Boot
3. **SPA路由** → Nginx重写到index.html

## 最佳实践

### 部署前准备
1. **代码检查**
   ```bash
   # 确保代码已提交并推送
   git status
   git push origin main
   ```

2. **环境准备**
   ```bash
   # 检查Docker容器状态
   docker ps

   # 检查磁盘空间
   df -h

   # 检查内存使用
   free -h
   ```

3. **备份现有部署**
   ```bash
   # 备份当前JAR文件
   cp /data/docs-manage/docs-admin/target/docs-admin.jar /backup/

   # 备份nginx配置
   cp /usr/local/nginx/conf/conf.d/docs-manage.conf /backup/
   ```

### 部署步骤建议

#### 首次部署
```bash
# 1. 克隆代码到服务器
git clone https://github.com/merryluoo/docs-manage.git /data/docs-manage

# 2. 启动数据库容器
cd /data/docs-manage
docker-compose up -d mysql redis

# 3. 等待数据库初始化完成
sleep 30

# 4. 执行完整部署
./scripts/manual-deploy.sh full-deploy

# 5. 验证部署
./scripts/manual-deploy.sh status
curl localhost
```

#### 日常更新
```bash
# 1. 拉取最新代码
cd /data/docs-manage
git pull origin main

# 2. 根据更改内容选择部署方式
# 仅前端更改
./scripts/manual-deploy.sh build-frontend
./scripts/manual-deploy.sh deploy-frontend

# 仅后端更改
./scripts/manual-deploy.sh build-backend
./scripts/manual-deploy.sh deploy-backend

# 重大更改
./scripts/manual-deploy.sh full-deploy
```

### 监控与维护

#### 服务监控脚本
创建监控脚本 `/data/scripts/monitor.sh`:
```bash
#!/bin/bash
echo "=== 服务状态监控 $(date) ==="
./scripts/manual-deploy.sh status

echo ""
echo "=== 磁盘使用情况 ==="
df -h | grep -E "(/$|/data)"

echo ""
echo "=== 内存使用情况 ==="
free -h

echo ""
echo "=== 最近错误日志 ==="
tail -5 /data/docs-manage/logs/backend.log | grep -i error
```

#### 定时任务设置
```bash
# 添加到crontab
crontab -e

# 每小时检查服务状态
0 * * * * /data/scripts/monitor.sh >> /var/log/service-monitor.log

# 每天凌晨清理日志
0 2 * * * find /data/docs-manage/logs -name "*.log" -size +100M -delete
```

### 性能调优

#### JVM参数优化
根据服务器配置调整脚本中的JVM参数：

```bash
# 2GB内存服务器
-Xms512m -Xmx1024m

# 4GB内存服务器
-Xms1024m -Xmx2048m

# 8GB内存服务器
-Xms2048m -Xmx4096m
```

#### Nginx优化
在nginx配置中添加性能优化设置：

```nginx
# 工作进程数
worker_processes auto;

# 连接数限制
events {
    worker_connections 1024;
}

# 缓冲区大小
client_body_buffer_size 128k;
client_max_body_size 100m;
```

### 安全加固

#### 1. 防火墙配置
```bash
# 仅开放必要端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --reload
```

#### 2. nginx安全配置
```nginx
# 隐藏nginx版本
server_tokens off;

# 安全头
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
```

#### 3. 应用安全
- 修改默认数据库密码
- 启用应用安全配置
- 定期更新依赖包

## 故障恢复

### 快速恢复步骤
1. **停止所有服务**
   ```bash
   ./scripts/manual-deploy.sh stop
   ```

2. **恢复备份**
   ```bash
   cp /backup/docs-admin.jar /data/docs-manage/docs-admin/target/
   cp /backup/docs-manage.conf /usr/local/nginx/conf/conf.d/
   ```

3. **重启服务**
   ```bash
   ./scripts/manual-deploy.sh deploy-backend
   nginx -s reload
   ```

### 数据恢复
```bash
# 恢复MySQL数据
docker exec -i mysql_container mysql -uroot -p123456 file_manage < /backup/database.sql

# 恢复Redis数据
docker exec -i redis_container redis-cli --rdb /backup/dump.rdb
```

## 版本管理

### 发布版本标记
```bash
# 创建版本标签
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0

# 部署特定版本
git checkout v1.0.0
./scripts/manual-deploy.sh full-deploy
```

### 回滚操作
```bash
# 回滚到上一个版本
git checkout v0.9.0
./scripts/manual-deploy.sh full-deploy
```

---

**版本**: 1.0
**更新日期**: 2025-06-03
**适用环境**: 中国大陆服务器环境
**维护团队**: 技术开发部
